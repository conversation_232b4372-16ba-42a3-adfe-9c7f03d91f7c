# keep
!Podfile.lock

# User-specific files
xcuserdata/
*.xcuserstate

# Workspace data
*.xcworkspace/xcuserdata

# Build products
build/
DerivedData/

# Scheme management
xcshareddata/WorkspaceSettings.xcsettings

# Auto-generated by Xcode
*.moved-aside
*.pbxuser
*.mode1v3
*.mode2v3
*.perspectivev3
*.xccheckout
*.xcscmblueprint

# Archives
*.xcarchive

# Swift Package Manager
.build/
.swiftpm/

# CocoaPods
Pods/
# Podfile.lock

# Carthage
Carthage/Build/

# Fastlane
fastlane/report.xml
fastlane/Preview.html
fastlane/screenshots/
fastlane/test_output/

# Swift Playgrounds
timeline.xctimeline
playground.xcworkspace

# macOS system files
.DS_Store
*.swp
*.lock
*.orig
*.tmp

# Icon files (from Finder)
Icon?
._*

# Firebase config (optional)
GoogleService-Info.plist

# Xcode Device Support
DeviceSupport/

# ai
.claude/
.gemini/
.gemini-clipboard/
.augment-guidelines
CLAUDE.md
GEMINI.md
QWEN.md