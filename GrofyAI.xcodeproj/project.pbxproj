// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		404FC6870DE01D71CCA3D880 /* Pods_GrofyAI.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = BFAA9B2A46CF14456B5F1747 /* Pods_GrofyAI.framework */; };
		745D4A5C2DB88F8A009868CB /* FlowStacks in Frameworks */ = {isa = PBXBuildFile; productRef = 745D4A5B2DB88F8A009868CB /* FlowStacks */; };
		745D4D162DB9D2B3009868CB /* Alamofire in Frameworks */ = {isa = PBXBuildFile; productRef = 745D4D152DB9D2B3009868CB /* Alamofire */; };
		745D4D1C2DB9D305009868CB /* Kingfisher in Frameworks */ = {isa = PBXBuildFile; productRef = 745D4D1B2DB9D305009868CB /* Kingfisher */; };
		745D4D1F2DB9D320009868CB /* MijickPopups in Frameworks */ = {isa = PBXBuildFile; productRef = 745D4D1E2DB9D320009868CB /* MijickPopups */; };
		745D4D222DB9D333009868CB /* Starscream in Frameworks */ = {isa = PBXBuildFile; productRef = 745D4D212DB9D333009868CB /* Starscream */; };
		745D544D2DBB9059009868CB /* KeychainAccess in Frameworks */ = {isa = PBXBuildFile; productRef = 745D544C2DBB9059009868CB /* KeychainAccess */; };
		74700D6E2E0952E800F92C90 /* SwiftMermaid in Frameworks */ = {isa = PBXBuildFile; productRef = 74700D6D2E0952E800F92C90 /* SwiftMermaid */; };
		74700D712E09537C00F92C90 /* MarkdownView in Frameworks */ = {isa = PBXBuildFile; productRef = 74700D702E09537C00F92C90 /* MarkdownView */; };
		74A8B8DB2DB793D600A59F17 /* StoreKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 74A8B8DA2DB793D600A59F17 /* StoreKit.framework */; };
		74B880502E019C81001749B1 /* EventSource in Frameworks */ = {isa = PBXBuildFile; productRef = 74B8804F2E019C81001749B1 /* EventSource */; };
		74DD30A52E24981A00F09374 /* GoogleSignIn in Frameworks */ = {isa = PBXBuildFile; productRef = 74DD30A42E24981A00F09374 /* GoogleSignIn */; };
		CBC379842E162281003D7ADB /* SDWebImageSwiftUI in Frameworks */ = {isa = PBXBuildFile; productRef = CBC379832E162281003D7ADB /* SDWebImageSwiftUI */; };
		CBC412D72E17CA460003DF25 /* SDWebImageWebPCoder in Frameworks */ = {isa = PBXBuildFile; productRef = CBC412D62E17CA460003DF25 /* SDWebImageWebPCoder */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		7452BD542DB78BB50040D4A2 /* GrofyAI.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = GrofyAI.app; sourceTree = BUILT_PRODUCTS_DIR; };
		74A8B8DA2DB793D600A59F17 /* StoreKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = StoreKit.framework; path = System/Library/Frameworks/StoreKit.framework; sourceTree = SDKROOT; };
		87CBFAC26089664AC68FBDBC /* Pods-GrofyAI.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-GrofyAI.release.xcconfig"; path = "Target Support Files/Pods-GrofyAI/Pods-GrofyAI.release.xcconfig"; sourceTree = "<group>"; };
		BFAA9B2A46CF14456B5F1747 /* Pods_GrofyAI.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_GrofyAI.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		C76BB4C05B53853DC3A78060 /* Pods-GrofyAI.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-GrofyAI.debug.xcconfig"; path = "Target Support Files/Pods-GrofyAI/Pods-GrofyAI.debug.xcconfig"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedBuildFileExceptionSet section */
		CBA0EE0B2DF6EFD40031850B /* Exceptions for "GrofyAI" folder in "GrofyAI" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				Info.plist,
			);
			target = 7452BD532DB78BB50040D4A2 /* GrofyAI */;
		};
/* End PBXFileSystemSynchronizedBuildFileExceptionSet section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		7452BD562DB78BB50040D4A2 /* GrofyAI */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			exceptions = (
				CBA0EE0B2DF6EFD40031850B /* Exceptions for "GrofyAI" folder in "GrofyAI" target */,
			);
			path = GrofyAI;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		7452BD512DB78BB50040D4A2 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				CBC412D72E17CA460003DF25 /* SDWebImageWebPCoder in Frameworks */,
				74DD30A52E24981A00F09374 /* GoogleSignIn in Frameworks */,
				74B880502E019C81001749B1 /* EventSource in Frameworks */,
				74700D712E09537C00F92C90 /* MarkdownView in Frameworks */,
				745D4D222DB9D333009868CB /* Starscream in Frameworks */,
				745D544D2DBB9059009868CB /* KeychainAccess in Frameworks */,
				74700D6E2E0952E800F92C90 /* SwiftMermaid in Frameworks */,
				745D4D1F2DB9D320009868CB /* MijickPopups in Frameworks */,
				745D4D162DB9D2B3009868CB /* Alamofire in Frameworks */,
				CBC379842E162281003D7ADB /* SDWebImageSwiftUI in Frameworks */,
				74A8B8DB2DB793D600A59F17 /* StoreKit.framework in Frameworks */,
				745D4A5C2DB88F8A009868CB /* FlowStacks in Frameworks */,
				745D4D1C2DB9D305009868CB /* Kingfisher in Frameworks */,
				404FC6870DE01D71CCA3D880 /* Pods_GrofyAI.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		7452BD4B2DB78BB50040D4A2 = {
			isa = PBXGroup;
			children = (
				7452BD562DB78BB50040D4A2 /* GrofyAI */,
				74A8B8D92DB793D600A59F17 /* Frameworks */,
				7452BD552DB78BB50040D4A2 /* Products */,
				89BA638F0D11899A9FD71E14 /* Pods */,
			);
			sourceTree = "<group>";
		};
		7452BD552DB78BB50040D4A2 /* Products */ = {
			isa = PBXGroup;
			children = (
				7452BD542DB78BB50040D4A2 /* GrofyAI.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		74A8B8D92DB793D600A59F17 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				74A8B8DA2DB793D600A59F17 /* StoreKit.framework */,
				BFAA9B2A46CF14456B5F1747 /* Pods_GrofyAI.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		89BA638F0D11899A9FD71E14 /* Pods */ = {
			isa = PBXGroup;
			children = (
				C76BB4C05B53853DC3A78060 /* Pods-GrofyAI.debug.xcconfig */,
				87CBFAC26089664AC68FBDBC /* Pods-GrofyAI.release.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		7452BD532DB78BB50040D4A2 /* GrofyAI */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 7452BD622DB78BB70040D4A2 /* Build configuration list for PBXNativeTarget "GrofyAI" */;
			buildPhases = (
				CD29F498D1E02A39B152D147 /* [CP] Check Pods Manifest.lock */,
				7452BD502DB78BB50040D4A2 /* Sources */,
				7452BD512DB78BB50040D4A2 /* Frameworks */,
				7452BD522DB78BB50040D4A2 /* Resources */,
				54C575CE24E2B7B6ECFB67C0 /* [CP] Embed Pods Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				7452BD562DB78BB50040D4A2 /* GrofyAI */,
			);
			name = GrofyAI;
			packageProductDependencies = (
				745D4A5B2DB88F8A009868CB /* FlowStacks */,
				745D4D152DB9D2B3009868CB /* Alamofire */,
				745D4D1B2DB9D305009868CB /* Kingfisher */,
				745D4D1E2DB9D320009868CB /* MijickPopups */,
				745D4D212DB9D333009868CB /* Starscream */,
				745D544C2DBB9059009868CB /* KeychainAccess */,
				74B8804F2E019C81001749B1 /* EventSource */,
				74700D6D2E0952E800F92C90 /* SwiftMermaid */,
				74700D702E09537C00F92C90 /* MarkdownView */,
				CBC379832E162281003D7ADB /* SDWebImageSwiftUI */,
				CBC412D62E17CA460003DF25 /* SDWebImageWebPCoder */,
				74DD30A42E24981A00F09374 /* GoogleSignIn */,
			);
			productName = grofyai;
			productReference = 7452BD542DB78BB50040D4A2 /* GrofyAI.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		7452BD4C2DB78BB50040D4A2 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1610;
				LastUpgradeCheck = 1610;
				TargetAttributes = {
					7452BD532DB78BB50040D4A2 = {
						CreatedOnToolsVersion = 16.1;
					};
				};
			};
			buildConfigurationList = 7452BD4F2DB78BB50040D4A2 /* Build configuration list for PBXProject "GrofyAI" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 7452BD4B2DB78BB50040D4A2;
			minimizedProjectReferenceProxies = 1;
			packageReferences = (
				745D4A5A2DB88F8A009868CB /* XCRemoteSwiftPackageReference "FlowStacks" */,
				745D4D142DB9D2B3009868CB /* XCRemoteSwiftPackageReference "Alamofire" */,
				745D4D1A2DB9D305009868CB /* XCRemoteSwiftPackageReference "Kingfisher" */,
				745D4D1D2DB9D320009868CB /* XCRemoteSwiftPackageReference "Popups" */,
				745D4D202DB9D333009868CB /* XCRemoteSwiftPackageReference "Starscream" */,
				745D544B2DBB9059009868CB /* XCRemoteSwiftPackageReference "KeychainAccess" */,
				74B8804E2E019C81001749B1 /* XCRemoteSwiftPackageReference "EventSource" */,
				74700D6C2E0952E800F92C90 /* XCRemoteSwiftPackageReference "Swift-Mermaid" */,
				74700D6F2E09537C00F92C90 /* XCRemoteSwiftPackageReference "MarkdownView" */,
				CBC379822E161DB5003D7ADB /* XCRemoteSwiftPackageReference "SDWebImageSwiftUI" */,
				CBC412D52E17CA460003DF25 /* XCRemoteSwiftPackageReference "SDWebImageWebPCoder" */,
				74DD30A32E24981A00F09374 /* XCRemoteSwiftPackageReference "GoogleSignIn-iOS" */,
			);
			preferredProjectObjectVersion = 77;
			productRefGroup = 7452BD552DB78BB50040D4A2 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				7452BD532DB78BB50040D4A2 /* GrofyAI */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		7452BD522DB78BB50040D4A2 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		54C575CE24E2B7B6ECFB67C0 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-GrofyAI/Pods-GrofyAI-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-GrofyAI/Pods-GrofyAI-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-GrofyAI/Pods-GrofyAI-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		CD29F498D1E02A39B152D147 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-GrofyAI-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		7452BD502DB78BB50040D4A2 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		7452BD602DB78BB70040D4A2 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 16;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		7452BD612DB78BB70040D4A2 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 16;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		7452BD632DB78BB70040D4A2 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = C76BB4C05B53853DC3A78060 /* Pods-GrofyAI.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = GrofyAI/GrofyAI.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"grofyai/Common/Preview Content\"";
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = 6AV3B463NP;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = GrofyAI/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = GrofyAI;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.productivity";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 16;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.grofyai.app;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = t_profile_dev;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		7452BD642DB78BB70040D4A2 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 87CBFAC26089664AC68FBDBC /* Pods-GrofyAI.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = GrofyAI/GrofyAI.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"grofyai/Common/Preview Content\"";
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = 6AV3B463NP;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = GrofyAI/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = GrofyAI;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.productivity";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 16;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.grofyai.app;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = t_profile_dev;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		7452BD4F2DB78BB50040D4A2 /* Build configuration list for PBXProject "GrofyAI" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				7452BD602DB78BB70040D4A2 /* Debug */,
				7452BD612DB78BB70040D4A2 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		7452BD622DB78BB70040D4A2 /* Build configuration list for PBXNativeTarget "GrofyAI" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				7452BD632DB78BB70040D4A2 /* Debug */,
				7452BD642DB78BB70040D4A2 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCRemoteSwiftPackageReference section */
		745D4A5A2DB88F8A009868CB /* XCRemoteSwiftPackageReference "FlowStacks" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/johnpatrickmorgan/FlowStacks.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 0.8.3;
			};
		};
		745D4D142DB9D2B3009868CB /* XCRemoteSwiftPackageReference "Alamofire" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/Alamofire/Alamofire.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 5.10.2;
			};
		};
		745D4D1A2DB9D305009868CB /* XCRemoteSwiftPackageReference "Kingfisher" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/onevcat/Kingfisher.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 8.3.2;
			};
		};
		745D4D1D2DB9D320009868CB /* XCRemoteSwiftPackageReference "Popups" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/Mijick/Popups.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 4.0.1;
			};
		};
		745D4D202DB9D333009868CB /* XCRemoteSwiftPackageReference "Starscream" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/daltoniam/Starscream.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 4.0.8;
			};
		};
		745D544B2DBB9059009868CB /* XCRemoteSwiftPackageReference "KeychainAccess" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/kishikawakatsumi/KeychainAccess.git";
			requirement = {
				branch = master;
				kind = branch;
			};
		};
		74700D6C2E0952E800F92C90 /* XCRemoteSwiftPackageReference "Swift-Mermaid" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/zxss702/Swift-Mermaid.git";
			requirement = {
				branch = main;
				kind = branch;
			};
		};
		74700D6F2E09537C00F92C90 /* XCRemoteSwiftPackageReference "MarkdownView" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/LiYanan2004/MarkdownView.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 2.4.0;
			};
		};
		74B8804E2E019C81001749B1 /* XCRemoteSwiftPackageReference "EventSource" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/Recouse/EventSource.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 0.1.5;
			};
		};
		74DD30A32E24981A00F09374 /* XCRemoteSwiftPackageReference "GoogleSignIn-iOS" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/google/GoogleSignIn-iOS";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 9.0.0;
			};
		};
		CBC379822E161DB5003D7ADB /* XCRemoteSwiftPackageReference "SDWebImageSwiftUI" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/SDWebImage/SDWebImageSwiftUI.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 3.0.0;
			};
		};
		CBC412D52E17CA460003DF25 /* XCRemoteSwiftPackageReference "SDWebImageWebPCoder" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/SDWebImage/SDWebImageWebPCoder.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 0.14.6;
			};
		};
/* End XCRemoteSwiftPackageReference section */

/* Begin XCSwiftPackageProductDependency section */
		745D4A5B2DB88F8A009868CB /* FlowStacks */ = {
			isa = XCSwiftPackageProductDependency;
			package = 745D4A5A2DB88F8A009868CB /* XCRemoteSwiftPackageReference "FlowStacks" */;
			productName = FlowStacks;
		};
		745D4D152DB9D2B3009868CB /* Alamofire */ = {
			isa = XCSwiftPackageProductDependency;
			package = 745D4D142DB9D2B3009868CB /* XCRemoteSwiftPackageReference "Alamofire" */;
			productName = Alamofire;
		};
		745D4D1B2DB9D305009868CB /* Kingfisher */ = {
			isa = XCSwiftPackageProductDependency;
			package = 745D4D1A2DB9D305009868CB /* XCRemoteSwiftPackageReference "Kingfisher" */;
			productName = Kingfisher;
		};
		745D4D1E2DB9D320009868CB /* MijickPopups */ = {
			isa = XCSwiftPackageProductDependency;
			package = 745D4D1D2DB9D320009868CB /* XCRemoteSwiftPackageReference "Popups" */;
			productName = MijickPopups;
		};
		745D4D212DB9D333009868CB /* Starscream */ = {
			isa = XCSwiftPackageProductDependency;
			package = 745D4D202DB9D333009868CB /* XCRemoteSwiftPackageReference "Starscream" */;
			productName = Starscream;
		};
		745D544C2DBB9059009868CB /* KeychainAccess */ = {
			isa = XCSwiftPackageProductDependency;
			package = 745D544B2DBB9059009868CB /* XCRemoteSwiftPackageReference "KeychainAccess" */;
			productName = KeychainAccess;
		};
		74700D6D2E0952E800F92C90 /* SwiftMermaid */ = {
			isa = XCSwiftPackageProductDependency;
			package = 74700D6C2E0952E800F92C90 /* XCRemoteSwiftPackageReference "Swift-Mermaid" */;
			productName = SwiftMermaid;
		};
		74700D702E09537C00F92C90 /* MarkdownView */ = {
			isa = XCSwiftPackageProductDependency;
			package = 74700D6F2E09537C00F92C90 /* XCRemoteSwiftPackageReference "MarkdownView" */;
			productName = MarkdownView;
		};
		74B8804F2E019C81001749B1 /* EventSource */ = {
			isa = XCSwiftPackageProductDependency;
			package = 74B8804E2E019C81001749B1 /* XCRemoteSwiftPackageReference "EventSource" */;
			productName = EventSource;
		};
		74DD30A42E24981A00F09374 /* GoogleSignIn */ = {
			isa = XCSwiftPackageProductDependency;
			package = 74DD30A32E24981A00F09374 /* XCRemoteSwiftPackageReference "GoogleSignIn-iOS" */;
			productName = GoogleSignIn;
		};
		CBC379832E162281003D7ADB /* SDWebImageSwiftUI */ = {
			isa = XCSwiftPackageProductDependency;
			package = CBC379822E161DB5003D7ADB /* XCRemoteSwiftPackageReference "SDWebImageSwiftUI" */;
			productName = SDWebImageSwiftUI;
		};
		CBC412D62E17CA460003DF25 /* SDWebImageWebPCoder */ = {
			isa = XCSwiftPackageProductDependency;
			package = CBC412D52E17CA460003DF25 /* XCRemoteSwiftPackageReference "SDWebImageWebPCoder" */;
			productName = SDWebImageWebPCoder;
		};
/* End XCSwiftPackageProductDependency section */
	};
	rootObject = 7452BD4C2DB78BB50040D4A2 /* Project object */;
}
