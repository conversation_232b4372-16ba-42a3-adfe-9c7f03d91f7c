{"originHash": "c9911d61213ba3521a218a2b412bc39e9ddb73a25bdde2248eac9dc6c4ffe9a0", "pins": [{"identity": "alamofire", "kind": "remoteSourceControl", "location": "https://github.com/Alamofire/Alamofire.git", "state": {"revision": "513364f870f6bfc468f9d2ff0a95caccc10044c5", "version": "5.10.2"}}, {"identity": "app-check", "kind": "remoteSourceControl", "location": "https://github.com/google/app-check.git", "state": {"revision": "61b85103a1aeed8218f17c794687781505fbbef5", "version": "11.2.0"}}, {"identity": "appauth-ios", "kind": "remoteSourceControl", "location": "https://github.com/openid/AppAuth-iOS.git", "state": {"revision": "145104f5ea9d58ae21b60add007c33c1cc0c948e", "version": "2.0.0"}}, {"identity": "eventsource", "kind": "remoteSourceControl", "location": "https://github.com/Recouse/EventSource.git", "state": {"revision": "7b2f4f585d3927876bd76eaede9fdff779eff102", "version": "0.1.5"}}, {"identity": "flowstacks", "kind": "remoteSourceControl", "location": "https://github.com/johnpatrickmorgan/FlowStacks.git", "state": {"revision": "0c2d1dcabcaf1a6b725f4a9d01796308d9b03db1", "version": "0.8.3"}}, {"identity": "googlesignin-ios", "kind": "remoteSourceControl", "location": "https://github.com/google/GoogleSignIn-iOS", "state": {"revision": "3996d908c7b3ce8a87d39c808f9a6b2a08fbe043", "version": "9.0.0"}}, {"identity": "googleutilities", "kind": "remoteSourceControl", "location": "https://github.com/google/GoogleUtilities.git", "state": {"revision": "60da361632d0de02786f709bdc0c4df340f7613e", "version": "8.1.0"}}, {"identity": "gtm-session-fetcher", "kind": "remoteSourceControl", "location": "https://github.com/google/gtm-session-fetcher.git", "state": {"revision": "a2ab612cb980066ee56d90d60d8462992c07f24b", "version": "3.5.0"}}, {"identity": "gtmappauth", "kind": "remoteSourceControl", "location": "https://github.com/google/GTMAppAuth.git", "state": {"revision": "56e0ccf09a6dd29dc7e68bdf729598240ca8aa16", "version": "5.0.0"}}, {"identity": "highlightr", "kind": "remoteSourceControl", "location": "https://github.com/raspu/Highlightr.git", "state": {"revision": "05e7fcc63b33925cd0c1faaa205cdd5681e7bbef", "version": "2.3.0"}}, {"identity": "keychainaccess", "kind": "remoteSourceControl", "location": "https://github.com/kishikawakatsumi/KeychainAccess.git", "state": {"branch": "master", "revision": "e0c7eebc5a4465a3c4680764f26b7a61f567cdaf"}}, {"identity": "kingfisher", "kind": "remoteSourceControl", "location": "https://github.com/onevcat/Kingfisher.git", "state": {"revision": "7deda23bbdca612076c5c315003d8638a08ed0f1", "version": "8.3.2"}}, {"identity": "latexswiftui", "kind": "remoteSourceControl", "location": "https://github.com/colinc86/LaTeXSwiftUI.git", "state": {"revision": "c45e0fd45f64923c49c5904a9f9626bc8939f05f", "version": "1.5.0"}}, {"identity": "libwebp-xcode", "kind": "remoteSourceControl", "location": "https://github.com/SDWebImage/libwebp-Xcode.git", "state": {"revision": "0d60654eeefd5d7d2bef3835804892c40225e8b2", "version": "1.5.0"}}, {"identity": "markdownview", "kind": "remoteSourceControl", "location": "https://github.com/LiYanan2004/MarkdownView.git", "state": {"revision": "27d7b96c1097ba158b37a65e37fb4da592623888", "version": "2.4.0"}}, {"identity": "mathjaxswift", "kind": "remoteSourceControl", "location": "https://github.com/colinc86/MathJaxSwift", "state": {"revision": "e23d6eab941da699ac4a60fb0e60f3ba5c937459", "version": "3.4.0"}}, {"identity": "popups", "kind": "remoteSourceControl", "location": "https://github.com/Mijick/Popups.git", "state": {"revision": "62457a91042e8ef68615fd8d2d27eb8240d2cd5b", "version": "4.0.1"}}, {"identity": "promises", "kind": "remoteSourceControl", "location": "https://github.com/google/promises.git", "state": {"revision": "540318ecedd63d883069ae7f1ed811a2df00b6ac", "version": "2.4.0"}}, {"identity": "sdwebimage", "kind": "remoteSourceControl", "location": "https://github.com/SDWebImage/SDWebImage.git", "state": {"revision": "b62cb63bf4ed1f04c961a56c9c6c9d5ab8524ec6", "version": "5.21.1"}}, {"identity": "sdwebimageswiftui", "kind": "remoteSourceControl", "location": "https://github.com/SDWebImage/SDWebImageSwiftUI.git", "state": {"revision": "451c6dfd5ecec2cf626d1d9ca81c2d4a60355172", "version": "3.1.3"}}, {"identity": "sdwebimagewebpcoder", "kind": "remoteSourceControl", "location": "https://github.com/SDWebImage/SDWebImageWebPCoder.git", "state": {"revision": "f534cfe830a7807ecc3d0332127a502426cfa067", "version": "0.14.6"}}, {"identity": "starscream", "kind": "remoteSourceControl", "location": "https://github.com/daltoniam/Starscream.git", "state": {"revision": "c6bfd1af48efcc9a9ad203665db12375ba6b145a", "version": "4.0.8"}}, {"identity": "swift-cmark", "kind": "remoteSourceControl", "location": "https://github.com/swiftlang/swift-cmark.git", "state": {"revision": "b022b08312decdc46585e0b3440d97f6f22ef703", "version": "0.6.0"}}, {"identity": "swift-html-entities", "kind": "remoteSourceControl", "location": "https://github.com/Kitura/swift-html-entities", "state": {"revision": "d8ca73197f59ce260c71bd6d7f6eb8bbdccf508b", "version": "4.0.1"}}, {"identity": "swift-markdown", "kind": "remoteSourceControl", "location": "https://github.com/swiftlang/swift-markdown.git", "state": {"revision": "ea79e83c8744d2b50b0dc2d5bbd1e857e1253bf9", "version": "0.6.0"}}, {"identity": "swift-mermaid", "kind": "remoteSourceControl", "location": "https://github.com/zxss702/Swift-Mermaid.git", "state": {"branch": "main", "revision": "29c19c7c8437c8212226fd69d2d90a73a15ed955"}}, {"identity": "swiftdraw", "kind": "remoteSourceControl", "location": "https://github.com/swhitty/SwiftDraw", "state": {"revision": "a19594794cdcdee5135caad3bc119096c50c92c2", "version": "0.22.0"}}], "version": 3}