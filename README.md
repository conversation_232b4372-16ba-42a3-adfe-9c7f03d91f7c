## GrofyAI

## 最低系统要求
- **iOS 16.0+** 

---

## 启动项目新方式

> [!IMPORTANT]
> 项目同时使用两种依赖管理工具：**CocoaPods** 和 **SPM** (Swift Package Manager)，所以第一次启动稍微有点不一样。（第一次成功启动后会在 xcode 最近文件列表中存在该方式，后续打开就不用管了）

原因是 [Azure-Samples/cognitive-services-speech-sdk](https://github.com/Azure-Samples/cognitive-services-speech-sdk/tree/master/quickstart/swift/ios/text-to-speech)目前仅支持通过CocoaPods管理依赖。

### 1. 准备工作 (只需一次)

在开始前，请确保电脑本地安装了 **CocoaPods**。用 [Homebrew](https://brew.sh/) 来安装最方便。

如果本地没安装 Homebrew，在终端执行以下命令：
```bash
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
```

然后，通过 Homebrew 安装 cocoapods：
```bash
brew install cocoapods
```

### 2. 开始配置

1.  **安装 Pod 依赖 (最关键的一步！)**
    这个命令会下载所有需要的 Pod 库，并生成一个 `.xcworkspace` 文件。在项目根路径执行
    ```bash
    pod install
    ```

2.  **打开项目**

    > 运行 `pod install` 之后，**首次启动项目要从访达（Finder）中进入项目路径，找到自动生成的 `GrofyAI.xcworkspace` 文件，双击打开项目**。
    > `.xcworkspace` 文件同时包含了主项目和 Pods 依赖，若直接打开 `.xcodeproj` 会提示找不到`MicrosoftCognitiveServicesSpeech`依赖（通过 Pods 管理）因为只有 SPM 依赖。

    双击 `GrofyAI.xcworkspace` 文件

    具体文件  

    ![alt text](/docs/image.png)
---

## 项目结构简介

```
GrofyAI/
├── API/                    # 请求端点信息
├── App/                    # 应用核心文件
│   ├── ContentView.swift   # 主界面
│   └── grofyaiApp.swift    # 应用入口
├── Common/                 # 通用资源
├── Components/             # 可复用UI组件
├── Controllers/            # 业务逻辑控制器
├── Extensions/             # 自定义拓展集
├── Models/                 # 数据模型
├── Services/               # 网络请求核心处理
├── Types/                  # 请求参数/响应 类型
└── Views/                  # 界面视图
```
1. **功能归类**：新功能放入对应目录
2. **清晰命名**：使用驼峰命名法，名称应表明用途
3. **模块独立**：减少模块间依赖
4. **组件复用**：优先使用已有组件
5. **明确依赖**：清晰标注依赖关系

## 技术栈

- 网络请求：[Alamofire](https://github.com/Alamofire/Alamofire)  
- 异步处理：[RxSwift](https://github.com/ReactiveX/RxSwift)  
- 路由导航：[FlowStacks](https://github.com/johnpatrickmorgan/FlowStacks)
- 图像显示(带缓存)：[Kingfisher](https://github.com/onevcat/Kingfisher)  
- 弹窗通知：[Popups](https://github.com/Mijick/Popups)  
- Websockets: [Starscream](https://github.com/daltoniam/Starscream)  