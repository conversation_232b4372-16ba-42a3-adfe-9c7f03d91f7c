data: {"code": 200, "type": "status", "event": "on_tool_start", "node": "deepsearch"}

data: {"code": 200, "type": "tool_deep_search", "content": [{"title": "Weather in current location", "url": "https://www.weatherapi.com/"}]}

data: {"code": 200, "type": "text", "content": "今天"}

data: {"code": 200, "type": "text", "content": "天"}

data: {"code": 200, "type": "text", "content": "气"}

data: {"code": 200, "type": "text", "content": "晴"}

data: {"code": 200, "type": "text", "content": "朗"}

data: {"code": 200, "type": "text", "content": "，"}

data: {"code": 200, "type": "text", "content": "温度"}

data: {"code": 200, "type": "text", "content": "大"}

data: {"code": 200, "type": "text", "content": "约"}

data: {"code": 200, "type": "text", "content": "为"}

data: {"code": 200, "type": "text", "content": "24"}

data: {"code": 200, "type": "text", "content": "摄"}

data: {"code": 200, "type": "text", "content": "氏"}

data: {"code": 200, "type": "text", "content": "度"}

data: {"code": 200, "type": "text", "content": "，"}

data: {"code": 200, "type": "text", "content": "风"}

data: {"code": 200, "type": "text", "content": "速"}

data: {"code": 200, "type": "text", "content": "为"}

data: {"code": 200, "type": "text", "content": "15"}

data: {"code": 200, "type": "text", "content": "."}

data: {"code": 200, "type": "text", "content": "5"}

data: {"code": 200, "type": "text", "content": "公里"}

data: {"code": 200, "type": "text", "content": "/"}

data: {"code": 200, "type": "text", "content": "小时"}

data: {"code": 200, "type": "text", "content": "，"}

data: {"code": 200, "type": "text", "content": "湿"}

data: {"code": 200, "type": "text", "content": "度"}

data: {"code": 200, "type": "text", "content": "78"}

data: {"code": 200, "type": "text", "content": "%"}

data: {"code": 200, "type": "text", "content": "。"}

data: {"code": 200, "type": "text", "content": "适"}

data: {"code": 200, "type": "text", "content": "合"}

data: {"code": 200, "type": "text", "content": "外"}

data: {"code": 200, "type": "text", "content": "出"}

data: {"code": 200, "type": "text", "content": "活动"}

data: {"code": 200, "type": "text", "content": "，"}

data: {"code": 200, "type": "text", "content": "享"}

data: {"code": 200, "type": "text", "content": "受"}

data: {"code": 200, "type": "text", "content": "清"}

data: {"code": 200, "type": "text", "content": "新的"}

data: {"code": 200, "type": "text", "content": "空"}

data: {"code": 200, "type": "text", "content": "气"}

data: {"code": 200, "type": "text", "content": "。"}
