data: {"code": 200, "type": "reasoning_text", "content": "让"}

data: {"code": 200, "type": "reasoning_text", "content": "用户"}

data: {"code": 200, "type": "reasoning_text", "content": "觉得"}

data: {"code": 200, "type": "reasoning_text", "content": "自然"}

data: {"code": 200, "type": "reasoning_text", "content": "易懂"}

data: {"code": 200, "type": "reasoning_text", "content": "。\n"}

data: {"code": 200, "type": "text", "content": "\n\n"}

data: {"code": 200, "type": "text", "content": "我是"}

data: {"code": 200, "type": "text", "content": "由"}

data: {"code": 200, "type": "text", "content": "Open"}

data: {"code": 200, "type": "text", "content": "AI"}

data: {"code": 200, "type": "text", "content": "开发"}

data: {"code": 200, "type": "text", "content": "的人工"}

data: {"code": 200, "type": "text", "content": "智能"}

data: {"code": 200, "type": "text", "content": "助手"}

data: {"code": 200, "type": "text", "content": "，"}

data: {"code": 200, "type": "text", "content": "通过"}

data: {"code": 200, "type": "text", "content": "语言"}

data: {"code": 200, "type": "text", "content": "模型"}

data: {"code": 200, "type": "text", "content": "技术"}

data: {"code": 200, "type": "text", "content": "帮助"}

data: {"code": 200, "type": "text", "content": "用户"}

data: {"code": 200, "type": "text", "content": "解答"}

data: {"code": 200, "type": "text", "content": "问题和"}

data: {"code": 200, "type": "text", "content": "获取"}

data: {"code": 200, "type": "text", "content": "信息"}

data: {"code": 200, "type": "text", "content": "，"}

data: {"code": 200, "type": "text", "content": "就像"}

data: {"code": 200, "type": "text", "content": "现在"}

data: {"code": 200, "type": "text", "content": "这样"}

data: {"code": 200, "type": "text", "content": "与"}

data: {"code": 200, "type": "text", "content": "您"}

data: {"code": 200, "type": "text", "content": "对话"}

data: {"code": 200, "type": "text", "content": "。"}
