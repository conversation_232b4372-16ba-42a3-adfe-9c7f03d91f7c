//
//  Credits.swift
//  GrofyAI
//
//  Created by kissy on 2025/7/2.
//

import Foundation

@MainActor
class CreditsController: ObservableObject {
    private let videoAiService = LLMService()
    private let creditsManager = CreditsManager()
    
    var rawConsumeList: [CreditsConsumeRes] = []
    
    private var canLoadMore: Bool = true
    
    private var current: Int = 1
    @Published var size: String = "50"
    
    @Published var groupedCredits: [String: [CreditsConsumeRes]] = [:]
    @Published var isLoading: Bool = false
    @Published var errorMessage: String?
    
    // 使用静态 lazy var 来创建日期格式化器，避免重复创建，提高性能
    private static let dateFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd"
        formatter.timeZone = TimeZone.current // 确保使用用户当前时区
        return formatter
    }()
    
    /// 下拉刷新数据，总是从第一页开始
    func refreshData() async {
        // 防止在已在加载时重复刷新
        guard !isLoading else { return }
        
        current = 1
        canLoadMore = true // 重置加载更多状态
        await fetchCreditsConsumeList(isRefreshing: true)
    }
    
    /// 加载下一页数据
        func loadMoreData() async {
            // 如果正在加载或没有更多数据了，则不执行
            guard !isLoading && canLoadMore else { return }
            
            current += 1
            await fetchCreditsConsumeList(isRefreshing: false)
        }
    
    
    func getCreditsItemsData() async{
        do {
            // 发送请求
            let creditsItems = try await videoAiService.creditsItemsData()
            creditsManager.saveItems(items: creditsItems)
        } catch {
            print(error)
        }
    }
    
    //获取积分消耗列表
    private func fetchCreditsConsumeList(isRefreshing: Bool) async {
        self.isLoading = true
        self.errorMessage = nil // 清除旧的错误信息
        
        // 使用 defer 确保 isLoading 总是会被设置回 false，即使发生错误
        defer { self.isLoading = false }
        
        let request = CreditsConsumeReq(
            size: self.size,
            current: String(self.current)
        )
        
        do {
            let newItems = try await videoAiService.creditsConsumeList(req: request)
            
            // 如果返回的数据为空，说明没有更多了
            if newItems.isEmpty {
                canLoadMore = false
                // 如果是第一页就没数据，则清空列表
                if isRefreshing {
                    rawConsumeList.removeAll()
                }
            } else {
                // 根据是刷新还是加载更多来更新原始列表
                if isRefreshing {
                    rawConsumeList = newItems
                } else {
                    rawConsumeList.append(contentsOf: newItems)
                }
            }
            
            // 更新用于 UI 展示的分组数据
            updateGroupedCredits()
            
        } catch {
            // 在捕获到错误时，如果是加载更多失败，需要将页码减回去
            if !isRefreshing {
                current -= 1
            }
            self.errorMessage = error.localizedDescription
            print("Failed to fetch credits consume list: \(error)")
        }
    }
    
    private static let apiDateFormatter: DateFormatter = {
            let formatter = DateFormatter()
            formatter.dateFormat = "yyyy-MM-dd HH:mm:ss" // <-- 替换成你实际的格式
            formatter.locale = Locale(identifier: "en_US_POSIX") // 保证解析不受用户地区设置影响
            formatter.timeZone = TimeZone(secondsFromGMT: 0) // 如果是 UTC 时间，最好指定时区
            return formatter
        }()

        /// 分组器：用于将 Date 对象格式化为 "yyyy-MM-dd" 的字符串，用作分组的 key。
        private static let groupingDateFormatter: DateFormatter = {
            let formatter = DateFormatter()
            formatter.dateFormat = "yyyy-MM-dd"
            return formatter
        }()

    
    /// 将原始列表数据转换为按日期分组的字典
//    private func updateGroupedCredits() {
//        // 使用 Swift 的 Dictionary(grouping:by:) 初始化器，非常高效和简洁
//        self.groupedCredits = Dictionary(grouping: self.rawConsumeList) { item in
//            // 将 createDate (Unix 时间戳) 转换为 "yyyy-MM-dd" 格式的字符串
//            guard let timestamp = item.createDate else {
//                return "Unknown Date" // 为没有日期的项目提供一个默认分组
//            }
//            let date = Date(timeIntervalSince1970: TimeInterval(timestamp))
//            return Self.dateFormatter.string(from: date)
//        }
//        
//        // （可选）如果希望每个日期分组内的项目也按时间倒序排列
//        for (date, items) in self.groupedCredits {
//            self.groupedCredits[date] = items.sorted {
//                ($0.createDate ?? 0) > ($1.createDate ?? 0)
//            }
//        }
//    }
    
    private func updateGroupedCredits() {
        // --- 修改点 2: 更新分组逻辑 ---
        self.groupedCredits = Dictionary(grouping: self.rawConsumeList) { item in
            // 1. 确保日期字符串存在
            guard let dateString = item.createDate else {
                return "Unknown Date"
            }
            
            // 2. 尝试将字符串解析为 Date 对象
            guard let date = Self.apiDateFormatter.date(from: dateString) else {
                // 如果解析失败，也归入一个特殊的分组，便于调试
                return "Invalid Date Format"
            }
            
            // 3. 将 Date 对象格式化为分组键
            return Self.groupingDateFormatter.string(from: date)
        }
        
        // --- 修改点 3: 更新排序逻辑 ---
        for (date, items) in self.groupedCredits {
            self.groupedCredits[date] = items.sorted { item1, item2 in
                // 为了排序，我们需要将两个日期字符串都转换为 Date 对象进行比较
                guard let dateString1 = item1.createDate, let date1 = Self.apiDateFormatter.date(from: dateString1) else {
                    return false // 如果 item1 的日期无效，让它排在后面
                }
                guard let dateString2 = item2.createDate, let date2 = Self.apiDateFormatter.date(from: dateString2) else {
                    return true // 如果 item2 的日期无效，让 item1 排在前面
                }
                
                // 按时间倒序排列
                return date1 > date2
            }
        }
    }
}
