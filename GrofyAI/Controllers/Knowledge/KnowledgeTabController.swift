import Combine
import Foundation

// MARK: - 知识库主页视图模型

@MainActor
class KnowledgeTabController: ObservableObject {
    // MARK: - 共享实例

    static let shared = KnowledgeTabController()

    @Published var categories: [KnowledgeCategory] = []
    @Published var isLoading = false
    @Published var errorMessage: String?

    @Published var refreshController = EnhancedRefreshController()

    private let fileService = FileService()
    private var cancellables = Set<AnyCancellable>()
    private var lastLoadTime: Date = .distantPast
    private var currentLoadTask: Task<Void, Never>?
    private var isInitialLoad = true
    private var loadAttemptCount = 0
    private var loadingStartTime: Date?
    private var lastSuccessfulLoadTime: Date?

    deinit {
        currentLoadTask?.cancel()
        cancellables.removeAll()
    }

    /// 智能加载知识库分类列表
    func loadKnowledgeCategoriesIfNeeded() async {
        if shouldSkipLoading() {
            return
        }

        await loadKnowledgeCategories()
    }

    /// 加载知识库分类列表
    func loadKnowledgeCategories() async {
        loadAttemptCount += 1

        let debounceInterval: TimeInterval = isInitialLoad ? 0.3 : 1.0
        let now = Date()

        if now.timeIntervalSince(lastLoadTime) < debounceInterval, !categories.isEmpty, !isInitialLoad {
            return
        }

        if isLoading {
            if isInitialLoad {
                currentLoadTask?.cancel()
            } else {
                return
            }
        } else {
            currentLoadTask?.cancel()
        }

        lastLoadTime = now
        loadingStartTime = now
        isLoading = true
        errorMessage = nil

        // 创建新的加载任务
        currentLoadTask = Task { [weak self] in
            guard let self else { return }

            if isInitialLoad {
                try? await Task.sleep(nanoseconds: 100_000_000) // 0.1秒
            }

            // 再次检查取消状态
            if Task.isCancelled {
                await MainActor.run { [weak self] in
                    self?.isLoading = false
                }
                return
            }

            do {
                let loadedCategories = try await fileService.getKnowledgeCategories()

                // 检查任务是否被取消
                if Task.isCancelled {
                    await MainActor.run { [weak self] in
                        self?.isLoading = false
                    }
                    return
                }

                await ensureMinimumLoadingTime()

                await MainActor.run { [weak self] in
                    guard let self else { return }
                    categories = loadedCategories
                    isLoading = false
                    isInitialLoad = false
                    lastSuccessfulLoadTime = Date()
                }
            } catch {
                if Task.isCancelled {
                    await MainActor.run { [weak self] in
                        self?.isLoading = false
                    }
                    return
                }

                await MainActor.run { [weak self] in
                    guard let self else { return }
                    isLoading = false

                    let errorMsg = if let businessError = error as? BusinessError {
                        businessError.message
                    } else {
                        error.localizedDescription
                    }

                    errorMessage = errorMsg
                    print("❌ KnowledgeTabController: 加载知识库分类失败 - \(errorMsg)")

                    // 如果是初始加载失败且是认证错误，不进行重试（等待登录成功通知）
                    if isInitialLoad, loadAttemptCount <= 2 {
                        let isAuthError = errorMsg.contains("401") ||
                            errorMsg.contains("unauthorized") ||
                            errorMsg.contains("missing")

                        if !isAuthError {
                            Task {
                                try? await Task.sleep(nanoseconds: 500_000_000) // 0.5秒后重试
                                await self.loadKnowledgeCategories()
                            }
                        }
                    }
                }
            }
        }

        await currentLoadTask?.value
    }

    /// 刷新知识库分类列表
    func refreshCategories() async {
        // 使用增强刷新控制器执行刷新
        refreshController.performRefresh { [weak self] in
            try await self?.performCategoriesRefresh()
        }
    }

    /// 执行实际的分类刷新操作
    func performCategoriesRefresh() async throws {
        currentLoadTask?.cancel()
        lastLoadTime = .distantPast
        lastSuccessfulLoadTime = nil
        isInitialLoad = false
        loadAttemptCount = 0

        await MainActor.run { [weak self] in
            self?.isLoading = true
            self?.errorMessage = nil
        }

        do {
            let loadedCategories = try await fileService.getKnowledgeCategories()

            await MainActor.run { [weak self] in
                guard let self else { return }
                categories = loadedCategories
                isLoading = false
                isInitialLoad = false
                lastSuccessfulLoadTime = Date()
            }
        } catch {
            await MainActor.run { [weak self] in
                guard let self else { return }
                isLoading = false

                let errorMsg = if let businessError = error as? BusinessError {
                    businessError.message
                } else {
                    error.localizedDescription
                }

                errorMessage = errorMsg
            }
            throw error
        }
    }

    /// 清除错误信息
    func clearError() {
        errorMessage = nil
    }

    /// 手动重试加载
    func retryLoad() async {
        currentLoadTask?.cancel()
        lastLoadTime = .distantPast
        await loadKnowledgeCategories()
    }

    /// 检查是否应该跳过加载
    private func shouldSkipLoading() -> Bool {
        if !categories.isEmpty, !isInitialLoad {
            return true
        }

        if isLoading {
            return true
        }

        if let lastLoad = lastSuccessfulLoadTime,
           Date().timeIntervalSince(lastLoad) < 300, // 5分钟
           !categories.isEmpty
        {
            return true
        }

        return false
    }

    private func ensureMinimumLoadingTime() async {
        guard let startTime = loadingStartTime else { return }

        let elapsed = Date().timeIntervalSince(startTime)
        let minimumLoadingTime: TimeInterval = 0.8 // 最小显示0.8秒

        if elapsed < minimumLoadingTime {
            let remainingTime = minimumLoadingTime - elapsed
            try? await Task.sleep(nanoseconds: UInt64(remainingTime * 1_000_000_000))
        }
    }

    /// 创建知识库分类
    /// - Parameters:
    ///   - title: 分类标题
    ///   - favicon: 分类图标（可选）
    ///   - content: 分类内容/简介（可选）
    func createCategory(
        title: String,
        favicon: String? = nil,
        content: String? = nil
    ) async throws {
        do {
            try await fileService.createKnowledgeCategory(
                title: title,
                favicon: favicon,
                content: content
            )
        } catch {
            await MainActor.run {
                self.errorMessage = error.localizedDescription
            }
            throw error
        }
    }

    /// 更新知识库分类
    /// - Parameters:
    ///   - id: 分类ID
    ///   - title: 分类标题
    ///   - favicon: 分类图标（可选）
    ///   - content: 分类内容/简介（可选）
    func updateCategory(
        id: Int,
        title: String,
        favicon: String? = nil,
        content: String? = nil
    ) async throws {
        do {
            try await fileService.updateKnowledgeCategory(
                id: id,
                title: title,
                favicon: favicon,
                content: content
            )

            // 更新成功，不在此处刷新数据
            // 数据刷新由通知机制统一处理，避免重复API调用

        } catch {
            await MainActor.run {
                self.errorMessage = error.localizedDescription
            }
            throw error
        }
    }

    /// 删除知识库分类
    /// - Parameter ids: 要删除的分类ID数组
    func deleteCategories(ids: [Int]) async throws {
        do {
            try await fileService.deleteKnowledgeCategories(ids: ids)

            // 删除成功，不在此处刷新数据
            // 数据刷新由调用方处理，避免重复API调用

        } catch {
            await MainActor.run {
                self.errorMessage = error.localizedDescription
            }
            throw error
        }
    }
}

// MARK: - 扩展方法

extension KnowledgeTabController {
    /// 根据ID获取知识库分类
    /// - Parameter id: 分类ID
    /// - Returns: 对应的知识库分类，如果不存在则返回nil
    func getCategory(by id: Int) -> KnowledgeCategory? {
        return categories.first { $0.id == id }
    }

    /// 检查是否有数据
    var hasData: Bool {
        return !categories.isEmpty
    }

    /// 检查是否应该显示空状态
    var shouldShowEmptyState: Bool {
        return !isLoading && !hasData && errorMessage == nil
    }

    /// 检查是否应该显示错误状态
    var shouldShowErrorState: Bool {
        return !isLoading && errorMessage != nil
    }
}
