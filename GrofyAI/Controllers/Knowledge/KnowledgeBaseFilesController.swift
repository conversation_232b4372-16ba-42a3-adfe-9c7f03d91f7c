import Alamofire
import Combine
import SwiftUI

/// 文件移动通知的用户信息
struct KnowledgeFileMoveInfo {
    let fileId: Int
    let sourceCategoryId: Int?
    let targetCategoryId: Int
    let file: KnowledgeFile
}

@MainActor
class KnowledgeBaseFilesController: ObservableObject {
    // MARK: - 个人知识库单例

    /// 个人知识库专用单例实例（categoryId = 0）
    static let personalInstance: KnowledgeBaseFilesController = {
        let instance = KnowledgeBaseFilesController(categoryId: 0)
        instance.setupPersonalInstanceObservers()
        return instance
    }()

    private nonisolated(unsafe) static var allInstances: NSHashTable<KnowledgeBaseFilesController> = NSHashTable
        .weakObjects()
    private nonisolated static let instancesQueue = DispatchQueue(
        label: "KnowledgeBaseFilesController.instances",
        attributes: .concurrent
    )

    @Published var files: [KnowledgeFile] = []
    @Published var isLoading = false
    @Published var isLoadingMore = false
    @Published var canLoadMore = true
    @Published var errorMessage: String?

    // MARK: - 选择模式相关

    @Published var isSelectionMode = false
    @Published var selectedItems: Set<Int> = []

    // MARK: - 删除状态管理

    @Published var isDeletingFiles = false

    /// 是否有删除操作正在进行
    var isDeleteInProgress: Bool {
        isDeletingFiles
    }

    // MARK: - 文件状态轮询管理

    @Published var isPollingFileStatus = false
    private var fileStatusPollingTask: Task<Void, Never>?

    // MARK: - 移动状态管理

    @Published var isMovingFile = false
    @Published var showCategorySelection = false
    @Published var fileToMove: KnowledgeFile?
    @Published var sourceCategory: KnowledgeCategory?

    /// 是否有移动操作正在进行
    var isMoveInProgress: Bool {
        isMovingFile
    }

    @Published var refreshController = EnhancedRefreshController()

    @Published var selectedFileType: MessageFile.FileCategory?
    @Published var selectedOrderBy: KnowledgeOrderBy = .createDate
    @Published var searchTitle = ""

    private var currentPage = 1
    private let pageSize = 10
    private let fileService = FileService()
    let categoryId: Int?
    private var currentLoadType: RefreshType = .initial

    // Task管理
    private var currentRefreshTask: Task<Void, Never>?
    private var currentLoadMoreTask: Task<Void, Never>?
    private var currentInitialTask: Task<Void, Never>?
    private var lastRefreshTime: Date = .distantPast

    init(categoryId: Int? = nil) {
        self.categoryId = categoryId

        addToInstancesRegistry()

        Task { @MainActor in
            self.setupFileMoveObserver()
        }
    }

    private nonisolated func addToInstancesRegistry() {
        Self.instancesQueue.async(flags: .barrier) { [weak self] in
            guard let self else { return }
            Self.allInstances.add(self)
        }
    }

    deinit {
        currentRefreshTask?.cancel()
        currentLoadMoreTask?.cancel()
        currentInitialTask?.cancel()
        fileStatusPollingTask?.cancel()

        currentRefreshTask = nil
        currentLoadMoreTask = nil
        currentInitialTask = nil
        fileStatusPollingTask = nil

        NotificationCenter.default.removeObserver(self)
    }

    /// 加载文件列表
    func loadFiles(loadType: RefreshType = .loadMore) {
        if loadType == .loadMore {
            guard !isLoadingMore, !isLoading, canLoadMore else { return }
            isLoadingMore = true
        } else {
            guard !isLoading else { return }
            isLoading = true
        }

        errorMessage = nil
        currentLoadType = loadType

        let task = Task { [weak self] in
            guard let self else { return }

            if Task.isCancelled {
                await MainActor.run { [weak self] in
                    self?.isLoading = false
                    self?.isLoadingMore = false
                }
                return
            }

            do {
                let response = try await fileService.getKnowledgeFiles(
                    categoryId: categoryId,
                    page: currentPage,
                    size: pageSize,
                    type: selectedFileType?.rawValue,
                    orderBy: selectedOrderBy.rawValue,
                    title: searchTitle.isEmpty ? nil : searchTitle
                )

                if Task.isCancelled {
                    await MainActor.run { [weak self] in
                        self?.isLoading = false
                        self?.isLoadingMore = false
                    }
                    return
                }

                await MainActor.run { [weak self] in
                    guard let self else { return }

                    if currentLoadType == .refresh {
                        files = response.files
                    } else {
                        files.append(contentsOf: response.files)
                    }

                    if currentLoadType == .loadMore, !response.files.isEmpty {
                        currentPage += 1
                    }
                    canLoadMore = response.files.count >= pageSize
                    isLoading = false
                    isLoadingMore = false
                }

            } catch {
                await MainActor.run { [weak self] in
                    guard let self else { return }

                    isLoading = false
                    isLoadingMore = false

                    if isCancellationError(error) {
                        return
                    }

                    errorMessage = if let businessError = error as? BusinessError {
                        businessError.message
                    } else {
                        error.localizedDescription
                    }
                    if let errorMessage {
                        ToastManager.shared.showError(errorMessage)
                    }
                }

                print("❌ 详细错误信息：\(error)")
            }
        }

        // 根据加载类型保存任务引用
        switch loadType {
        case .refresh:
            currentRefreshTask = task
        case .loadMore:
            currentLoadMoreTask = task
        case .initial:
            currentInitialTask = task
        }
    }

    /// 刷新文件列表
    func refreshFiles() async {
        refreshController.performRefresh { [weak self] in
            try await self?.performFilesRefresh()
        }
    }

    /// 执行实际的文件刷新操作
    func performFilesRefresh() async throws {
        let now = Date()
        if now.timeIntervalSince(lastRefreshTime) < 0.5 {
            print("KnowledgeBaseFilesController: 刷新请求过于频繁，忽略")
            return
        }

        guard !isLoading else {
            print("KnowledgeBaseFilesController: 正在加载中，忽略刷新请求")
            throw RefreshError.alreadyLoading
        }

        currentRefreshTask?.cancel()

        await MainActor.run { [weak self] in
            self?.isLoading = true
            self?.errorMessage = nil
        }

        currentPage = 1
        canLoadMore = true
        lastRefreshTime = now

        // 执行刷新操作
        do {
            let response = try await fileService.getKnowledgeFiles(
                categoryId: categoryId,
                page: currentPage,
                size: pageSize,
                type: selectedFileType?.rawValue,
                orderBy: selectedOrderBy.rawValue,
                title: searchTitle.isEmpty ? nil : searchTitle
            )

            await MainActor.run { [weak self] in
                guard let self else { return }

                files = response.files

                if !response.files.isEmpty {
                    currentPage = 2
                } else {
                    currentPage = 1
                }
                canLoadMore = response.files.count >= pageSize
                isLoading = false
            }

        } catch {
            await MainActor.run { [weak self] in
                self?.isLoading = false
            }
            throw error
        }
    }

    /// 清除错误信息
    func clearError() {
        errorMessage = nil
    }

    // MARK: - 文件状态轮询方法

    /// 开始文件状态轮询
    func startFileStatusPolling() {
        // 防止重复启动
        guard fileStatusPollingTask == nil else {
            return
        }

        // 检查是否有需要轮询的文件
        let processingFiles = files.filter { !$0.isReady || !$0.isVectorReady }
        guard !processingFiles.isEmpty else {
            return
        }

        isPollingFileStatus = true

        fileStatusPollingTask = Task { [weak self] in
            defer {
                Task { @MainActor in
                    self?.isPollingFileStatus = false
                }
            }

            do {
                while !Task.isCancelled {
                    // 检查是否还有处理中的文件
                    let currentProcessingFiles = await MainActor.run {
                        self?.files.filter { !$0.isReady || !$0.isVectorReady } ?? []
                    }

                    // 如果没有处理中的文件，停止轮询
                    if currentProcessingFiles.isEmpty {
                        break
                    }

                    // 静默刷新文件状态
                    try await self?.performSilentRefresh()

                    // 等待2秒再进行下一次轮询
                    try await Task.sleep(for: .seconds(2))
                }
            } catch is CancellationError {
                print("KnowledgeBaseFilesController: 轮询任务被正常取消")
            } catch {
                print("KnowledgeBaseFilesController: 轮询过程中发生错误: \(error.localizedDescription)")
            }
        }
    }

    /// 静默刷新文件状态（不显示加载指示器）
    private func performSilentRefresh() async throws {
        guard let categoryId else { return }

        let currentFileCount = await MainActor.run { files.count }

        let response = try await fileService.getKnowledgeFiles(
            categoryId: categoryId,
            page: 1,
            size: max(currentFileCount, pageSize),
            type: selectedFileType?.rawValue,
            orderBy: selectedOrderBy.rawValue,
            title: searchTitle.isEmpty ? nil : searchTitle
        )

        await MainActor.run { [weak self] in
            guard let self else { return }

            var updatedFiles = files
            var hasChanges = false

            for updatedFile in response.files {
                if let index = updatedFiles.firstIndex(where: { $0.id == updatedFile.id }) {
                    let oldFile = updatedFiles[index]

                    if oldFile.fileStatus != updatedFile.fileStatus || oldFile.vectorStatus != updatedFile
                        .vectorStatus
                    {
                        hasChanges = true
                    }

                    updatedFiles[index] = updatedFile
                }
            }

            if hasChanges {
                files = updatedFiles
            }
        }
    }

    /// 停止文件状态轮询
    func stopFileStatusPolling() {
        fileStatusPollingTask?.cancel()
        fileStatusPollingTask = nil
        isPollingFileStatus = false
    }

    /// 更新文件标签
    func updateFileTags(fileId: Int, tags: [String]) async throws {
        do {
            try await fileService.updateKnowledgeFileTags(fileId: fileId, tags: tags)

            await MainActor.run { [weak self] in
                guard let self else { return }

                if let index = files.firstIndex(where: { $0.id == fileId }) {
                    let updatedFile = KnowledgeFile(
                        id: files[index].id,
                        categoryId: files[index].categoryId,
                        fileId: files[index].fileId,
                        title: files[index].title,
                        type: files[index].type,
                        link: files[index].link,
                        size: files[index].size,
                        tags: tags,
                        fileStatus: files[index].fileStatus,
                        vectorStatus: files[index].vectorStatus,
                        createDate: files[index].createDate,
                        threadId: files[index].threadId
                    )
                    files[index] = updatedFile
                }
            }

        } catch {
            await MainActor.run { [weak self] in
                let errorMsg = if let businessError = error as? BusinessError {
                    businessError.message
                } else {
                    error.localizedDescription
                }
                self?.errorMessage = errorMsg
            }
            throw error
        }
    }

    /// 重置筛选条件
    func resetFilters() async {
        selectedFileType = nil
        selectedOrderBy = .createDate
        searchTitle = ""
        await refreshFiles()
    }
}

// MARK: - 扩展方法

extension KnowledgeBaseFilesController {
    /// 检查是否有数据
    var hasData: Bool {
        return !files.isEmpty
    }

    /// 检查是否应该显示空状态
    var shouldShowEmptyState: Bool {
        return !isLoading && !hasData && errorMessage == nil
    }

    /// 检查是否应该显示错误状态
    var shouldShowErrorState: Bool {
        return !isLoading && errorMessage != nil
    }

    /// 获取文件总数
    var fileCount: Int {
        return files.count
    }

    /// 检查是否有应用筛选条件
    var hasActiveFilters: Bool {
        return selectedFileType != nil || selectedOrderBy != .createDate || !searchTitle.isEmpty
    }

    /// 根据文件类型分组
    var filesByType: [MessageFile.FileCategory: [KnowledgeFile]] {
        return Dictionary(grouping: files) { $0.fileCategory }
    }

    /// 获取所有文件类型
    var availableFileTypes: [MessageFile.FileCategory] {
        return Array(Set(files.map(\.fileCategory))).sorted { $0.rawValue < $1.rawValue }
    }

    /// 重置视图状态
    func resetViewState() {
        isSelectionMode = false
        selectedItems.removeAll()
        isDeletingFiles = false
    }

    // MARK: - 选择模式方法

    /// 切换选择模式
    func toggleSelectionMode() {
        isSelectionMode.toggle()
        if !isSelectionMode {
            selectedItems.removeAll()
        }
    }

    /// 切换单个文件的选择状态
    func toggleSelection(for file: KnowledgeFile) {
        if selectedItems.contains(file.id) {
            selectedItems.remove(file.id)
        } else {
            selectedItems.insert(file.id)
        }
    }

    /// 全选/取消全选
    func toggleSelectAll() {
        if selectedItems.count == files.count {
            selectedItems.removeAll()
        } else {
            selectedItems = Set(files.map(\.id))
        }
    }

    /// 删除选中的文件
    func deleteSelectedFiles() {
        let selectedFileIds = Array(selectedItems)
        guard !selectedFileIds.isEmpty else { return }

        isDeletingFiles = true

        Task { [weak self] in
            guard let self else { return }

            do {
                try await fileService.deleteKnowledgeFiles(ids: selectedFileIds)

                await MainActor.run { [weak self] in
                    guard let self else { return }

                    files.removeAll { selectedFileIds.contains($0.id) }

                    isDeletingFiles = false
                    isSelectionMode = false
                    selectedItems.removeAll()

                    ToastManager.shared.showSuccess("已删除 \(selectedFileIds.count) 个文件")
                }

            } catch {
                await MainActor.run { [weak self] in
                    guard let self else { return }

                    isDeletingFiles = false

                    if !isCancellationError(error) {
                        let errorMessage = if let businessError = error as? BusinessError {
                            businessError.message
                        } else {
                            error.localizedDescription
                        }
                        ToastManager.shared.showError("删除失败：\(errorMessage)")
                    }
                }
            }
        }
    }

    /// 检查是否为取消错误
    private func isCancellationError(_ error: Error) -> Bool {
        // 检查Task取消
        if Task.isCancelled {
            return true
        }

        // 检查URLError取消
        if let urlError = error as? URLError, urlError.code == .cancelled {
            return true
        }

        // 检查AFError取消
        if let afError = error as? AFError {
            switch afError {
            case .requestAdaptationFailed(let error):
                return isCancellationError(error)
            case .requestRetryFailed(let retryError, _):
                return isCancellationError(retryError)
            case .sessionTaskFailed(let error):
                return isCancellationError(error)
            default:
                break
            }
        }

        // 检查错误描述中的取消关键词
        let errorDescription = error.localizedDescription.lowercased()
        return errorDescription.contains("cancelled") ||
            errorDescription.contains("canceled") ||
            errorDescription.contains("operation was cancelled")
    }

    // MARK: - 移动文件相关方法

    /// 开始移动文件流程
    func startMoveFile(_ file: KnowledgeFile) async {
        fileToMove = file

        await KnowledgeTabController.shared.loadKnowledgeCategoriesIfNeeded()
        setSourceCategory(for: file)

        showCategorySelection = true
    }

    /// 设置源知识库信息
    private func setSourceCategory(for file: KnowledgeFile) {
        if let fileCategoryId = file.categoryId {
            if fileCategoryId == 0 {
                sourceCategory = KnowledgeCategory.personalKnowledgeBase
            } else {
                sourceCategory = KnowledgeTabController.shared.categories.first { $0.id == fileCategoryId }
            }
        } else {
            sourceCategory = KnowledgeCategory.personalKnowledgeBase
        }
    }

    /// 取消移动文件
    func cancelMoveFile() {
        fileToMove = nil
        sourceCategory = nil
        showCategorySelection = false
    }

    /// 执行文件移动
    /// - Parameters:
    ///   - file: 要移动的文件
    ///   - targetCategory: 目标知识库分类
    func moveFile(_ file: KnowledgeFile, to targetCategory: KnowledgeCategory) {
        guard !isMovingFile else { return }

        isMovingFile = true
        let sourceCategoryId = file.categoryId

        Task { [weak self] in
            guard let self else { return }

            do {
                try await fileService.moveKnowledgeFile(
                    fileId: file.id,
                    targetCategoryId: targetCategory.id
                )

                await MainActor.run { [weak self] in
                    guard let self else { return }

                    let updatedFile = KnowledgeFile(
                        id: file.id,
                        categoryId: targetCategory.id,
                        fileId: file.fileId,
                        title: file.title,
                        type: file.type,
                        link: file.link,
                        size: file.size,
                        tags: file.tags,
                        fileStatus: file.fileStatus,
                        vectorStatus: file.vectorStatus,
                        createDate: file.createDate,
                        threadId: file.threadId
                    )

                    let moveInfo = KnowledgeFileMoveInfo(
                        fileId: file.id,
                        sourceCategoryId: sourceCategoryId,
                        targetCategoryId: targetCategory.id,
                        file: updatedFile
                    )

                    NotificationCenter.default.post(
                        name: .knowledgeFileMovedNotification,
                        object: nil,
                        userInfo: ["moveInfo": moveInfo]
                    )

                    files.removeAll { $0.id == file.id }

                    isMovingFile = false
                    fileToMove = nil
                    sourceCategory = nil
                    showCategorySelection = false

                    ToastManager.shared.showSuccess("文件已移动到「\(targetCategory.title)」")
                }

            } catch {
                await MainActor.run { [weak self] in
                    guard let self else { return }

                    isMovingFile = false

                    if !isCancellationError(error) {
                        let errorMessage = if let businessError = error as? BusinessError {
                            businessError.message
                        } else {
                            error.localizedDescription
                        }
                        ToastManager.shared.showError("移动失败：\(errorMessage)")
                    }
                }
            }
        }
    }

    // MARK: - 个人实例专用方法

    private func setupPersonalInstanceObservers() {
        guard categoryId == 0 else { return }

        NotificationCenter.default.addObserver(
            forName: .userAuthenticationReady,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            DispatchQueue.main.async { [weak self] in
                guard let self else { return }
                loadFiles(loadType: .initial)
            }
        }
    }

    /// 设置文件移动通知观察者
    private func setupFileMoveObserver() {
        guard Thread.isMainThread else {
            DispatchQueue.main.async { [weak self] in
                self?.setupFileMoveObserver()
            }
            return
        }

        NotificationCenter.default.addObserver(
            forName: .knowledgeFileMovedNotification,
            object: nil,
            queue: .main
        ) { [weak self] notification in
            guard let self,
                  let userInfo = notification.userInfo,
                  let moveInfo = userInfo["moveInfo"] as? KnowledgeFileMoveInfo
            else {
                return
            }

            Task { @MainActor in
                self.handleFileMoveNotification(moveInfo)
            }
        }
    }

    /// 处理文件移动通知
    private func handleFileMoveNotification(_ moveInfo: KnowledgeFileMoveInfo) {
        let targetCategoryId = moveInfo.targetCategoryId
        let sourceCategoryId = moveInfo.sourceCategoryId
        let updatedFile = moveInfo.file

        if let currentCategoryId = categoryId, currentCategoryId == targetCategoryId {
            if !files.contains(where: { $0.id == updatedFile.id }) {
                files.insert(updatedFile, at: 0)
            }
        }

        if let currentCategoryId = categoryId,
           let sourceCategoryId,
           currentCategoryId == sourceCategoryId
        {
            files.removeAll { $0.id == updatedFile.id }
        }
    }
}
