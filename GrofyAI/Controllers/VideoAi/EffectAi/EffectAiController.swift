//
//  Untitled.swift
//  GrofyAI
//
//  Created by kissy on 2025/5/1.
//

import SwiftUI
import Combine

enum InputValidationError: LocalizedError {
    case invalidInputs
    
    var errorDescription: String? {
        switch self {
        case .invalidInputs:
            return "请提供所有必需的图片。"
        }
    }
}

@MainActor
class EffectAiController: ObservableObject {
    
    private let videoAiService = VideoAiService()
    
    // 输入状态
    @Published var threadId: String = ""
    @Published var type: EffectVideoAi.EffectType = .babyFace {
        didSet {
            syncContentTypeAndResetInputs()
            // 🔥 核心逻辑：当 'type' 改变后，
            // 立即根据 'type' 的 `requiredContentType` 更新 'sho' 的值。
            // 这样做可以确保两个状态始终同步。
            
            let required = type.requiredContentType
            if show != required {
                self.image = nil
                self.images_right = nil
                self.images_left = nil
                show = required
            }
        }
    }
    @Published var image: String? = nil
    @Published var images: [String]? = nil
    @Published var images_left: String? = nil
    @Published var images_right: String? = nil
    
    
    @Published var show: EffectVideoAi.ContentType = .single
    
    
    // MARK: - 状态管理
    @Published  var isLoading: Bool = false
    @Published var showError: Bool = false
    @Published  var errorMessage: String?
    //MARK: 轮询 id
    @Published var transactionID: Int?
    
    
    func setThreadId(){
        self.threadId = "thd-" + UUID().uuidString
    }
    
    /// 重置所有状态到初始值
    func resetControllerState() {
        self.threadId = ""
        resetInputs()
        self.isLoading = false
        self.showError = false
        self.errorMessage = nil
        self.transactionID = nil
        self.type = .babyFace // 重置回默认类型
    }
    
    private func syncContentTypeAndResetInputs() {
        let requiredContentType = type.requiredContentType
        if show != requiredContentType {
            show = requiredContentType
            resetInputs()
        }
    }
    
    private func resetInputs() {
        self.image = nil
        self.images_left = nil
        self.images_right = nil
    }
    
    /// 用户点击提交按钮时调用
    func onSubmit() {
        guard !isLoading else { return }
        guard let request = buildRequest() else {
            handle(error: InputValidationError.invalidInputs)
            return
        }
        
        Task {
            await performSubmission(with: request)
        }
    }
    
    private func buildRequest() -> Effect_video_VideoReq? {
        switch show {
        case .single:
            guard let image = self.image else { return nil }
            return Effect_video_VideoReq(
                threadId: self.threadId,
                type: self.type.rawValue,
                image: image,
                images: []
            )
        case .multiple:
            guard let left = self.images_left, let right = self.images_right else { return nil }
            return Effect_video_VideoReq(
                threadId: self.threadId,
                type: self.type.rawValue,
                image: nil,
                images: [left, right]
            )
        }
    }
    
    /// 执行异步提交任务并更新UI状态
    private func performSubmission(with request: Effect_video_VideoReq) async {
        self.transactionID = nil
        self.isLoading = true
        self.errorMessage = nil
        self.showError = false
        
        defer { self.isLoading = false }
        
        do {
            let id = try await videoAiService.effectVideo(req: request)
            self.transactionID = id
        } catch {
            handle(error: error)
        }
    }
    
    private func handle(error: Error) {
        if let localizedError = error as? LocalizedError, let description = localizedError.errorDescription {
            self.errorMessage = description
        } else {
            self.errorMessage = error.localizedDescription
        }
        self.showError = true
    }
    
}
