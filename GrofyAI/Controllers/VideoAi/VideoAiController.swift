//
//  VideoAiController.swift
//  GrofyAI
//
//  Created by kissy on 2025/6/24.
//

import SwiftUI

@MainActor
class VideoAiController: ObservableObject {
    // 选中模型
    @Published var selectedModel: VideoAiModel? = .wanx
    // 内容宽度
    @Published var contentWidth: CGFloat = 0
    // 滚动视图代理
    @Published var proxy: ScrollViewProxy?
    // 提示词强度
    @Published var imagePromptStrength: Double = 0.5
    
    // 处理滑动
    func handleSwipe(_ value: DragGesture.Value) {
        let translation = value.translation.width
        let velocity = value.velocity.width
        
        let screenWidth = UIScreen.main.bounds.width
        let threshold = screenWidth * 0.25
        
        if abs(translation) > threshold || abs(velocity) > 800 {
            let direction: Int = translation < 0 ? 1 : -1
            let selectedIndex = VideoAiModel.allCases.firstIndex(of: selectedModel ?? .wanx) ?? 0
            let newIndex = max(0, min(VideoAiModel.allCases.count - 1, selectedIndex + direction))
            selectedModel = VideoAiModel.allCases[newIndex]
            if 1 < newIndex, newIndex < VideoAiModel.allCases.count - 1 {
                select(index: newIndex)
            }
        }
    }
    // 选择模型
    func select(index: Int) {
        withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
            scrollTo(index: index)
        }
    }
    // 滚动到指定模型
    func scrollTo(index: Int) {
        proxy?.scrollTo(index, anchor: .center)
    }
}
