//
//  Untitled3.swift
//  GrofyAI
//
//  Created by kissy on 2025/5/1.
//

import SwiftUI

@MainActor
class PixverseImageToVideoController: ObservableObject {
    
    
    private let videoAiService = VideoAiService()
    private let creditsManager = CreditsManager()

    // 表单数据
    @Published var threadId: String = ""
    @Published var prompt: String = ""
    @Published var negative_prompt: String  = ""
    @Published var img_id: Int = 0
    @Published var img_url: String = ""
    @Published var duration:PixverseVideoAi.I2V.Duration = .five {
        didSet {
            if motion_mode == .fast,duration == .eight {
                motion_mode = .normal
            }
            consumeCredits()
        }
    }
    @Published var model: PixverseVideoAi.I2V.Model = .pixverse_v3_5 {
        didSet {
            consumeCredits()
        }
    }
    @Published var motion_mode: PixverseVideoAi.I2V.MotionMode? = .normal {
        didSet {
            consumeCredits()
        }
    }
    @Published var quality: PixverseVideoAi.I2V.Quality = .quality_540p {
        didSet {
            // 当 model 改变时，根据新的 model 设置一个合适的默认 quality
            setDefaultResolutionForCurrentQuality()
            setDefaultMotionModeForCurrentQuality()
            consumeCredits()
        }
    }
    @Published var seed: Int? = nil
    @Published var style: PixverseVideoAi.I2V.Style? = nil
    
    //轮询 id
    @Published var transactionID: Int?
    
    //积分消耗
    @Published var credits: Int? = 0
    
    // MARK: - 状态管理
    @Published  var isLoading: Bool = false
    @Published var showError: Bool = false
    @Published  var errorMessage: String?
    
    
    func setThreadId(){
        self.threadId = "thd-" + UUID().uuidString
    }
    
    private func setDefaultResolutionForCurrentQuality() {
        switch quality {
        case .quality_540p, .quality_720p:
            duration = duration
        case .quality_1080p:
            duration = .five
        }
    }
    
    private func setDefaultMotionModeForCurrentQuality() {
        switch quality {
        case .quality_540p, .quality_720p:
            motion_mode = motion_mode
        case .quality_1080p:
            motion_mode = .normal
        }
    }
    
    //MARK: - 初始化
    init() {
        setDefaultResolutionForCurrentQuality()
        setDefaultMotionModeForCurrentQuality()
        consumeCredits()
    }
    
    //MARK: - 获取消耗积分
    func consumeCredits(){
        let creditsItemKey = [
            self.model.rawValue,
            self.motion_mode?.rawValue ?? "",
            String(self.duration.rawValue),
            self.quality.rawValue
        ].joined(separator: "&")
        
        let creditsItem =  creditsManager.getItem(byUnionId: creditsItemKey)
        
        self.credits = creditsItem?.credit
    }
    
    // MARK: - 提交
    func onSubmit() {
        guard !isLoading else { return }
        // 重置之前的事务ID，以防用户快速连续点击
        transactionID = nil
        
        Task {
            isLoading = true
            defer { isLoading = false }
            
            do {
                print("pix_ID:",img_id)
                print("pix_URL:",img_url)
                let request = Pixverse_i2v_VideoReq(
                    threadId: self.threadId,
                    img_id: self.img_id,
                    prompt: self.prompt,
                    negative_prompt: self.negative_prompt,
                    duration: self.duration.rawValue,
                    model: self.model.rawValue,
                    motion_mode: self.motion_mode?.rawValue,
                    quality: self.quality.rawValue,
                    seed: self.seed,
                    style: self.style?.rawValue
                )
                // 发送请求
                let id = try await videoAiService.pixverse_i2vVideo(req: request)
                self.transactionID = id
            } catch {
                handle(error: error)
            }
            
            isLoading = false
        }
        
    }
    
    // MARK: - 错误处理
    private func handle(error: Error) {
        if let networkError = error as? NetworkError {
            errorMessage = networkError.localizedDescription
        } else {
            errorMessage = error.localizedDescription
        }
        showError = true
    }
    
    // MARK: - 重置（可选）
    func resetToDefaults() {
        self.threadId = ""
        self.prompt = ""
        self.negative_prompt = ""
        self.img_id = 0
        self.img_url = ""
        self.duration = .five
        self.model = .pixverse_v3_5
        self.motion_mode = .normal
        self.quality = .quality_540p
        self.seed = nil
        self.style = nil
        
        
        self.isLoading = false
        self.showError = false
        self.errorMessage = ""
        self.transactionID = nil
    }
}

