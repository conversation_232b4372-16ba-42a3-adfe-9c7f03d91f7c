//
//  Untitled.swift
//  GrofyAI
//
//  Created by kissy on 2025/5/1.
//

import SwiftUI

@MainActor
class RunwayImageToVideoController: ObservableObject {
    private let videoAiService = VideoAiService()
    private let creditsManager = CreditsManager()

    // 表单数据
    @Published var threadId: String = ""
    @Published var lastImage: String = ""  //"https://cdn.pixabay.com/photo/2018/09/03/11/51/pictures-3651039_1280.png"
    @Published var firstImage: String =  "" //https://cdn.pixabay.com/photo/2018/09/03/11/51/pictures-3651039_1280.png"
    @Published var model: RunwayVideoAi.Model = .runway_gen4_turbo {
        didSet {
            setDefaultRatioForCurrentModel()
            consumeCredits()
        }
    }
    @Published var promptText: String = ""
    @Published var ratio: RunwayVideoAi.Ratio = .ratio_960_960
    @Published var duration: RunwayVideoAi.Duration? = .five {
        didSet {
            consumeCredits()
        }
    }
    @Published var seed: Int? = nil
    
    //轮询 id
    @Published var transactionID: Int?
    
    //积分消耗
    @Published var credits: Int? = 0
    
    // MARK: - 状态管理
    @Published  var isLoading: Bool = false
    @Published var showError: Bool = false
    @Published  var errorMessage: String?

    
    func setThreadId(){
        self.threadId = "thd-" + UUID().uuidString
    }
    
    private func setDefaultRatioForCurrentModel() {
        switch model {
        case .runway_gen3_turbo:
            ratio = .ratio_1280_768
        case .runway_gen4_turbo:
            ratio = .ratio_960_960
        }
    }
    
    // MARK: - 初始化
    init() {
        // 在初始化时也调用一次，确保初始状态一致
        setDefaultRatioForCurrentModel()
        consumeCredits()
    }
    
    //MARK: - 获取消耗积分
    func consumeCredits(){
        let creditsItemKey = [
            self.model.rawValue,
            String(self.duration?.rawValue ?? 0) 
        ].joined(separator: "&")
        
        let creditsItem =  creditsManager.getItem(byUnionId: creditsItemKey)
        
        self.credits = creditsItem?.credit
    }
    
    // MARK: - 提交
    func onSubmit() {
        guard !isLoading else { return }
        // 重置之前的事务ID，以防用户快速连续点击
        transactionID = nil
        
        Task {
            isLoading = true
            defer { isLoading = false }
            
            do {
                let request = Runway_i2v_VideoReq(
                    threadId: self.threadId,
                    firstImage: self.firstImage,
                    lastImage: self.lastImage,
                    model: self.model.rawValue,
                    ratio: self.ratio.rawValue,
                    promptText: self.promptText,
                    seed: self.seed,
                    duration: self.duration?.rawValue
                )
                // 发送请求
                let id = try await videoAiService.runway_i2vVideo(req: request)
                self.transactionID = id
            } catch {
                handle(error: error)
            }
            
            isLoading = false
        }
        
    }
    
    // MARK: - 错误处理
    private func handle(error: Error) {
        if let networkError = error as? NetworkError {
            errorMessage = networkError.localizedDescription
        } else {
            errorMessage = error.localizedDescription
        }
        showError = true
    }
    
    // MARK: - 重置（可选）
    func resetToDefaults() {
        self.threadId = ""
        self.lastImage = ""
        self.firstImage = ""
        self.model = .runway_gen4_turbo
        self.promptText = ""
        self.ratio = .ratio_960_960
        self.duration = .five
        self.seed = nil
        
        
        self.isLoading = false
        self.showError = false
        self.errorMessage = ""
        self.transactionID = nil
    }
    
}
