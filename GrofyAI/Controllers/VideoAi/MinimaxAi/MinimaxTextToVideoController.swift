//
//  Untitled 2.swift
//  GrofyAI
//
//  Created by kissy on 2025/5/1.
//

import SwiftUI

@MainActor
class MinimaxTextToVideoController: ObservableObject {
  
    
    private let videoAiService = VideoAiService()
    private let creditsManager = CreditsManager()

    // 表单数据
    @Published var threadId: String = ""
    @Published var model: MinimaxVideoAi.T2V.Model = .minimax_t2v_01 {
        didSet {
            consumeCredits()
        }
    }
    @Published var prompt: String = ""
    @Published var prompt_optimizer:Bool? = false

    //轮询 id
    @Published var transactionID: Int?
    
    //积分消耗
    @Published var credits: Int? = 0
    
    // MARK: - 状态管理
    @Published  var isLoading: Bool = false
    @Published var showError: Bool = false
    @Published  var errorMessage: String?

    
    func setThreadId(){
        self.threadId = "thd-" + UUID().uuidString
    }
    
    init () {
        consumeCredits()
    }
    
    //MARK: - 获取消耗积分
    func consumeCredits(){
        let creditsItemKey = [
            self.model.rawValue,
        ].joined(separator: "&")
        
        let creditsItem =  creditsManager.getItem(byUnionId: creditsItemKey)
        
        self.credits = creditsItem?.credit
    }
    
    // MARK: - 提交
    func onSubmit() {
        guard !isLoading else { return }
        // 重置之前的事务ID，以防用户快速连续点击
        transactionID = nil
        
        Task {
            isLoading = true
            defer { isLoading = false }
            
            do {
                let request = Minimax_t2v_VideoReq(
                    threadId: self.threadId,
                    model: self.model.rawValue,
                    prompt: self.prompt,
                    prompt_optimizer: self.prompt_optimizer
                )
                // 发送请求
                let id = try await videoAiService.minimax_t2vVideo(req: request)
                self.transactionID = id
            } catch {
                handle(error: error)
            }
            
            isLoading = false
        }
        
    }
    
    // MARK: - 错误处理
    private func handle(error: Error) {
        if let networkError = error as? NetworkError {
            errorMessage = networkError.localizedDescription
        } else {
            errorMessage = error.localizedDescription
        }
        showError = true
    }
    
    // MARK: - 重置（可选）
    func resetToDefaults() {
        self.threadId = ""
        self.model = .minimax_t2v_01
        self.prompt = ""
        self.prompt_optimizer = nil
        
        
        self.isLoading = false
        self.showError = false
        self.errorMessage = ""
        self.transactionID = nil
    }
    
}

