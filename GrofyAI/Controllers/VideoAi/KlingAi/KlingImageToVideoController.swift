//
//  Untitled.swift
//  GrofyAI
//
//  Created by kissy on 2025/5/1.
//

import SwiftUI
// MARK: - 错误类型
enum I2VError: Error {
    case invalidImage
    case requestFailed
}


@MainActor
class KlingImageToVideoController: ObservableObject {
    private let videoAiService = VideoAiService()
    private let creditsManager = CreditsManager()

    // 表单数据
    @Published var threadId: String = ""
    @Published var model_name: KlingVideoAi.I2V.Model = .kling_v1 {
        didSet {
            consumeCredits()
        }
    }
    @Published var prompt: String = ""
    @Published var negative_prompt: String = ""
    @Published var cfg_scale: Double? = nil
    @Published var mode: KlingVideoAi.I2V.Mode? = .std {
        didSet {
            consumeCredits()
        }
    }
    @Published var type: KlingVideoAi.I2V.CameraMovementType? = nil
    @Published var aspect_ratio: KlingVideoAi.I2V.AspectRatio? = .ratio_1_1
    @Published var duration: KlingVideoAi.I2V.Duration? = .five {
        didSet {
            consumeCredits()
        }
    }
    @Published var callback_url: String? = nil
    @Published var config: KlingConfig? = nil
    @Published var image: String = ""
    @Published var image_tail: String = ""
    
    @Published var cameraConfigOption: CameraConfigOption = .none

    //轮询 id
    @Published var transactionID: Int?
    
    //积分消耗
    @Published var credits: Int? = 0
    
    // MARK: - 状态管理
    @Published  var isLoading: Bool = false
    @Published var showError: Bool = false
    @Published  var errorMessage: String?
    
    func setThreadId(){
        self.threadId = "thd-" + UUID().uuidString
    }
    
    init() {
        consumeCredits()
    }
    
    //MARK: - 获取消耗积分
    func consumeCredits(){
        let creditsItemKey = [
            self.model_name.rawValue,
            self.mode?.rawValue ?? "",
            String(self.duration?.rawValue ?? 0)
        ].joined(separator: "&")
        
        let creditsItem =  creditsManager.getItem(byUnionId: creditsItemKey)
        
        self.credits = creditsItem?.credit
    }
    
    
    // MARK: - 提交
    func onSubmit() {
        guard !isLoading else { return }
        // 重置之前的事务ID，以防用户快速连续点击
        transactionID = nil
        checkFormData()
        
        Task {
            isLoading = true
            defer { isLoading = false }
            
            do {
                let request = Kling_i2v_VideoReq(
                    threadId: self.threadId,
                    model_name: self.model_name.rawValue,
                    prompt: self.prompt,
                    negative_prompt: self.negative_prompt,
                    cfg_scale: self.cfg_scale,
                    mode: self.mode?.rawValue,
                    type: self.type?.rawValue,
                    config: self.config,
                    aspect_ratio: self.aspect_ratio?.rawValue,
                    duration: self.duration?.rawValue,
                    callback_url: self.callback_url,
                    image: self.image,
                    image_tail: self.image_tail
                )
                // 发送请求
                let id = try await videoAiService.kling_i2vVideo(req: request)
                self.transactionID = id
            } catch {
                handle(error: error)
            }
            
            isLoading = false
        }
        
    }
    
    //MARK: - 校验
    private func checkFormData () {
        if (self.model_name == .kling_v1 && self.duration == .ten) || ((self.model_name == .kling_v1_6 && self.mode != .std )){
            image = ""
            image_tail = ""
        }
    }
    
    // MARK: - 错误处理
    private func handle(error: Error) {
        if let networkError = error as? NetworkError {
            errorMessage = networkError.localizedDescription
        } else {
            errorMessage = error.localizedDescription
        }
        showError = true
    }
    
    // MARK: - 重置（可选）
    func resetToDefaults() {
        self.threadId = ""
        self.model_name = .kling_v1
        self.prompt = ""
        self.negative_prompt = ""
        self.cfg_scale = nil
        self.mode = .std
        self.type = .simple
        self.aspect_ratio = .ratio_1_1
        self.duration = .five
        self.callback_url = nil
        self.config = nil
        self.image = ""
        self.image_tail = ""
        self.cameraConfigOption = .none
        
        self.isLoading = false
        self.showError = false
        self.errorMessage = ""
        self.transactionID = nil
    }
    
}
