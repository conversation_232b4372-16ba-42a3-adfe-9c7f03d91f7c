//
//  Untitled.swift
//  GrofyAI
//
//  Created by kissy on 2025/5/1.
//
import SwiftUI
import Combine

@MainActor
class WanxImageToVideoController: ObservableObject {
    private let videoAiService = VideoAiService()
    private let creditsManager = CreditsManager()

    
    // 表单数据
    @Published var threadId: String = ""
    @Published var model: WanxVideoAi.I2V.Model = .wanx_i2v_model2_1_turbo {
        didSet {
            setDefaultResolutionForCurrentModel()
            setDefaultDurationForCurrentModel()
            consumeCredits()
        }
    }
    @Published var prompt: String = ""
    @Published var img_url: String = ""
    @Published var resolution: WanxVideoAi.I2V.Resolution = .resolution_480p
    @Published var prompt_extend: Bool? = false
    @Published var duration: WanxVideoAi.I2V.Duration? = .five {
        didSet {
            consumeCredits()
        }
    }
    @Published var seed: Int? = nil
    
    //轮询 id
    @Published var transactionID: Int?
    
    //积分消耗
    @Published var credits: Int? = 0
    // State
    // MARK: - 状态管理
    @Published  var isLoading: Bool = false
    @Published var showError: Bool = false
    @Published  var errorMessage: String?

    
    func setThreadId(){
        self.threadId = "thd-" + UUID().uuidString
    }
    
    private func setDefaultResolutionForCurrentModel() {
        switch model {
        case .wanx_i2v_model2_1_plus:
            // 如果模型是 DALL-E 3，默认质量设置为 "标准"
            resolution = .resolution_720p
        case .wanx_i2v_model2_1_turbo:
            // 如果模型是 GPT Image 1，默认质量设置为 "高"
            resolution = .resolution_480p
        }
    }
    
    private func setDefaultDurationForCurrentModel() {
        switch model {
        case .wanx_i2v_model2_1_plus:
            // 如果模型是 DALL-E 3，默认质量设置为 "标准"
            duration = .five
        case .wanx_i2v_model2_1_turbo:
            // 如果模型是 GPT Image 1，默认质量设置为 "高"
            duration = .three
        }
    }
    
    //MARK: - 初始化
    init() {
        setDefaultResolutionForCurrentModel()
        setDefaultDurationForCurrentModel()
        consumeCredits()
    }
    
    //MARK: - 获取消耗积分
    func consumeCredits(){
        let creditsItemKey = [
            self.model.rawValue,
            String(self.duration?.rawValue ?? 0) 
        ].joined(separator: "&")
        
        let creditsItem =  creditsManager.getItem(byUnionId: creditsItemKey)
        
        self.credits = creditsItem?.credit
    }
    
    
    // MARK: - 提交
    func onSubmit() {
        guard !isLoading else { return }
        // 重置之前的事务ID，以防用户快速连续点击
        transactionID = nil
        
        Task {
            isLoading = true
            defer { isLoading = false }
            
            do {
                let request = Wanx_i2v_VideoReq(
                    threadId: self.threadId,
                    model: self.model.rawValue,
                    prompt: self.prompt,
                    img_url: self.img_url,
                    resolution: self.resolution.rawValue,
                    prompt_extend: self.prompt_extend,
                    duration: self.duration?.rawValue,
                    seed: self.seed
                )
                // 发送请求
                let id = try await videoAiService.wanx_i2vVideo(req: request)
                self.transactionID = id
            } catch {
                handle(error: error)
            }
            
            isLoading = false
        }
        
    }
    
    // MARK: - 错误处理
    private func handle(error: Error) {
        if let networkError = error as? NetworkError {
            errorMessage = networkError.localizedDescription
        } else {
            errorMessage = error.localizedDescription
        }
        showError = true
    }
    
    // MARK: - 重置（可选）
    func resetToDefaults() {
        self.threadId = ""
        self.model = .wanx_i2v_model2_1_turbo
        self.prompt = ""
        self.img_url = ""
        self.resolution = .resolution_480p
        self.prompt_extend = nil
        self.duration = .five
        self.seed = nil
        
        self.isLoading = false
        self.showError = false
        self.errorMessage = ""
        self.transactionID = nil
    }
}
