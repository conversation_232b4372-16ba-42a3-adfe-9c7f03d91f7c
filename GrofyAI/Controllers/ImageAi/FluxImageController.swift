//
//  FluxImageController.swift
//  GrofyAI
//
//  Created by kissy on 2025/4/28.
//
import SwiftUI
import PhotosUI

@MainActor
class FluxImageController: ObservableObject {
    private let imageAiService = ImageAiService()
    private let creditsManager = CreditsManager()
    // MARK: - 输入参数
    @Published var threadId: String = ""
    @Published var model: FluxImageAi.Model = .flux_dev {
        didSet {
            consumeCredits()
        }
    }
    @Published var prompt: String = ""
    @Published var image_url: String? = nil
    @Published var prompt_upsampling: Bool = false
    @Published var seed: Int? = nil
    @Published var aspect_ratio: FluxImageAi.AspectRatio = .ratio1_1
    @Published var safety_tolerance: Int? = 0
    @Published var output_format: FluxImageAi.OutputFormat = .jpeg
    @Published var raw: Bool? = false
    @Published var image_prompt_strength: Double? = nil

    // MARK: - 状态管理
    @Published var isLoading: Bool = false
    @Published var showError: Bool = false
    @Published var errorMessage: String = ""
    @Published var transactionID: Int?
//    @Published var isSliderEditing: Bool = false    

    //积分消耗
    @Published var credits: Int? = 0
    
    func setThreadId(){
        self.threadId = "thd-" + UUID().uuidString
    }
    
    init() {
        consumeCredits()
    }
    
    //MARK: - 获取消耗积分
    func consumeCredits(){
        let creditsItemKey = [
            self.model.rawValue,
        ].joined(separator: "&")
        
        let creditsItem =  creditsManager.getItem(byUnionId: creditsItemKey)
        
        self.credits = creditsItem?.credit
    }
    
    // MARK: - 生成图像
    func onSubmit() {
        guard !isLoading else { return }
        self.transactionID = nil
        checkFormData()
        Task {
            isLoading = true
            defer { isLoading = false }

            do {
                // 构建请求体
                let request = FluxImageReq(
                    threadId: threadId,
                    model: model.rawValue,
                    prompt: prompt,
                    image_url: image_url,
                    prompt_upsampling: prompt_upsampling,
                    seed: seed,
                    aspect_ratio: aspect_ratio.rawValue,
                    safety_tolerance: safety_tolerance,
                    output_format: output_format.rawValue,
                    raw: raw,
                    image_prompt_strength: image_prompt_strength
                )
                //请求参数缓存到本地
//                FormDataStorage.saveRequest(request: request)
                // 发送请求
                let id = try await imageAiService.fluxImage(req: request)
                self.transactionID = id
            } catch {
                handle(error: error)
            }
        }
    }
    
    //加载本地缓存请求参数
    func loadFormData() {
        // 尝试加载 FluxImageReq 类型的数据
        if let savedRequest = FormDataStorage.loadRequest(as: FluxImageReq.self) {
            // 将加载到的数据赋值给 ViewModel 的属性
            self.prompt = savedRequest.prompt
            self.image_url = savedRequest.image_url
            self.prompt_upsampling = savedRequest.prompt_upsampling
            self.seed = savedRequest.seed
            self.aspect_ratio = FluxImageAi.AspectRatio(rawValue:savedRequest.aspect_ratio) ?? .ratio1_1
            self.safety_tolerance = savedRequest.safety_tolerance
            self.output_format = FluxImageAi.OutputFormat(rawValue: savedRequest.output_format) ?? .jpeg
            self.raw = savedRequest.raw
            self.image_prompt_strength = savedRequest.image_prompt_strength
        }
    }
    
    //MARK: - 校验
    private func checkFormData () {
        if self.model != .flux_pro_1_1_ultra {
            self.raw = nil
            self.image_prompt_strength = nil
        }
    }
    
    
    // MARK: - 错误处理
    private func handle(error: Error) {
        if let networkError = error as? NetworkError {
            errorMessage = networkError.localizedDescription
        } else {
            errorMessage = error.localizedDescription
        }
        showError = true
    }

    // MARK: - 重置（可选）
    func resetToDefaults() {
        self.prompt = ""
        self.image_url = nil
        self.prompt_upsampling = false
        self.seed = nil
        self.aspect_ratio = .ratio1_1
        self.safety_tolerance = nil
        self.output_format = .jpeg
        self.raw = nil
        self.image_prompt_strength = nil
        
        self.transactionID = nil
        self.errorMessage = ""
        self.showError = false
        self.isLoading = false
    }
    
    
}
