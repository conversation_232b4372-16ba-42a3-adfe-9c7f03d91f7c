//
//  IdeogramImageController.swift
//  GrofyAI
//
//  Created by kissy on 2025/4/28.
//

import SwiftUI

// MARK: - 状态管理模块
@MainActor
class IdeogramImageController: ObservableObject {
    // 服务依赖
    private let imageAiService = ImageAiService()
    private let creditsManager = CreditsManager()
    // 输入参数
    @Published var threadId: String = ""
    @Published var prompt:String = ""
    @Published var seed:Int? = nil
    @Published var aspect_ratio: IdeogramImageAi.AspectRatio? = .aspect1_1
    @Published var model: IdeogramImageAi.Model = .ideogram_v3 {
        didSet {
            consumeCredits()
        }
    }
    @Published var speed: IdeogramImageAi.Speed = .quality {
        didSet {
            consumeCredits()
        }
    }
    @Published var magic_prompt = IdeogramImageAi.MagicPromptOption.auto
    @Published var style_type: IdeogramImageAi.StyleType? = .auto
    @Published var negative_prompt:String? = ""
    @Published var image_Url:String? = nil
    
    
    // 界面状态
    @Published var isLoading = false
    @Published var showError = false
    @Published var errorMessage = ""
    @Published var transactionID: Int?
    
    //积分消耗
    @Published var credits: Int? = 0
    
    func setThreadId(){
        self.threadId = "thd-" + UUID().uuidString
    }
    
    init() {
        consumeCredits()
    }
    
    //MARK: - 获取消耗积分
    func consumeCredits(){
        let creditsItemKey = [
            self.model.rawValue,
            self.speed.rawValue
        ].joined(separator: "&")
        
        let creditsItem =  creditsManager.getItem(byUnionId: creditsItemKey)
        
        self.credits = creditsItem?.credit
    }
    
    func onSubmit() {
        guard !isLoading else { return }
        self.transactionID = nil
        Task {
            isLoading = true
            defer { isLoading = false }

            do {
                // 构建请求体
                var requestParameters: IdeogramImageReq {
                    IdeogramImageReq(
                        threadId: threadId,
                        prompt: prompt,
                        seed: seed,
                        aspect_ratio: aspect_ratio?.rawValue,
                        model: model.rawValue,
                        speed: speed.rawValue,
                        magic_prompt: magic_prompt.rawValue,
                        style_type: style_type?.rawValue,
                        negative_prompt: negative_prompt,
                        image_Url:image_Url
                    )
                }
                // 发送请求
                let id = try await imageAiService.ideogramImage(req: requestParameters)
                transactionID = id
            } catch {
                handle(error: error)
            }
        }
    }
    
    // MARK: - 错误处理
    private func handle(error: Error) {
        if let networkError = error as? NetworkError {
            errorMessage = networkError.localizedDescription
        } else {
            errorMessage = error.localizedDescription
        }
        showError = true
    }
    
    func resetToDefaults() {
        self.threadId = ""
        self.prompt = ""
        self.seed = nil
        self.aspect_ratio = .aspect1_1
        self.model = .ideogram_v3
        self.magic_prompt = .auto
        self.style_type = .auto
        self.negative_prompt = ""
        self.image_Url = nil
        
        self.isLoading = false
        self.showError = false
        self.errorMessage = ""
        self.transactionID = nil
    }
    
}

