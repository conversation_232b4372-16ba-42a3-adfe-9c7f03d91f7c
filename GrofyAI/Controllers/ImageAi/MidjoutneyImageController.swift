//
//  MidjoutneyImageController.swift
//  GrofyAI
//
//  Created by kissy on 2025/4/28.
//
import SwiftUI

@MainActor
class MidjourneyImageController: ObservableObject {
    
    private let service = ImageAiService()
    private let creditsManager = CreditsManager()
    
    @Published var threadId: String = ""
    @Published var prompt: String = ""
    @Published var base64Array: [String]? = []
    @Published var aspectRatio: MidjourneyImageAi.AspectRatio? = .ratio1_1
    @Published var style: MidjourneyImageAi.Style? = .posterStyle
    @Published var modes: MidjourneyImageAi.Mode? = .fast {
        didSet {
            consumeCredits()
        }
    }
    @Published var model: String = "MJ_V7"  //默认值不修改
    
    
    @Published var isLoading: Bool = false
    @Published var transactionID: Int? = nil
    @Published var errorMessage: String? = ""
    @Published var showError = false
    
    //积分消耗
    @Published var credits: Int? = 0
    
    
    
//    private let viewModel: MidjourneyImageViewModel
//    
//    init(viewModel: MidjourneyImageViewModel = MidjourneyImageViewModel()) {
//        self.viewModel = viewModel
//    }
    
    func setThreadId(){
        self.threadId = "thd-" + UUID().uuidString
    }
    
    
    init() {
        consumeCredits()
    }
    
    //MARK: - 获取消耗积分
    func consumeCredits(){
        let creditsItemKey = [
            self.model,
            self.modes?.rawValue ?? ""
        ].joined(separator: "&")
        
        let creditsItem =  creditsManager.getItem(byUnionId: creditsItemKey)
        
        self.credits = creditsItem?.credit
    }
    
    func onSubmit() {
        guard !isLoading else { return }
        self.transactionID = nil
        Task {
            //前置条件
//            guard !prompt.isEmpty else { return }

            isLoading = true
            defer {isLoading = false}

            do {
                let request = MidjourneyImageReq(
                    threadId: threadId,
                    prompt: prompt,
                    base64Array: base64Array,
                    aspectRatio: aspectRatio?.rawValue,
                    style: style?.rawValue,
                    modes: modes?.rawValue
                )
                
                let id = try await service.midjourneyImage(req: request)
                self.transactionID = id
            } catch {
                handle(error: error)
            }
            
            isLoading = false
        }
    }
    
    @MainActor
    func resetToDefaults() {
        Task {
            self.threadId = ""
            self.prompt = ""
            self.base64Array = []
            self.aspectRatio = .ratio1_1
            self.style = .posterStyle
            self.modes = .fast
            
            self.transactionID = nil
            self.errorMessage = ""
            self.isLoading = false
            self.showError = false
        }
    }
    
    private func handle(error: Error) {
        if let networkError = error as? NetworkError {
            errorMessage = networkError.localizedDescription
        } else {
            errorMessage = error.localizedDescription
        }
        showError = true
    }
}

// MARK: - ViewModel
class MidjourneyImageViewModel {
    private let service = ImageAiService()
    
    func submitImageRequest(request: MidjourneyImageReq) async throws -> Int {
        try await service.midjourneyImage(req: request)
    }
}
