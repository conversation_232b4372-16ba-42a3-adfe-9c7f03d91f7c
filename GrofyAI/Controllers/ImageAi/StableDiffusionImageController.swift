//
//  StableDiffusionImageController.swift
//  GrofyAI
//
//  Created by kissy on 2025/4/28.
//
import SwiftUI
import PhotosUI

@MainActor
class StableDiffusionImageController: ObservableObject {
    
    let service = ImageAiService()
    private let creditsManager = CreditsManager()
    
    @Published var threadId: String = ""
    @Published var prompt: String = ""
    @Published var mode: StableDiffusion3_5ImageAi.Mode = .text_to_image
    @Published var imageUrl: String? = nil
    @Published var strength: Int? = nil
    @Published var aspect_ratio: StableDiffusion3_5ImageAi.AspectRatio? = .ratio1_1
    @Published var model: StableDiffusion3_5ImageAi.Model? = .sd3_5_large {
        didSet {
            consumeCredits()
        }
    }
    @Published var output_format: StableDiffusion3_5ImageAi.OutputFormat? = .jpeg
    @Published var style_preset: StableDiffusion3_5ImageAi.StylePreset? = .style3DModel
    @Published var negative_prompt: String? = ""
    
    @Published var isLoading = false
    @Published var showError = false
    @Published var errorMessage = ""
    @Published var transactionID: Int?
    
    //积分消耗
    @Published var credits: Int? = 0
    
    
    func setThreadId(){
        self.threadId = "thd-" + UUID().uuidString
    }
    
    init() {
        consumeCredits()
    }
    
    //MARK: - 获取消耗积分
    func consumeCredits(){
        let creditsItemKey = [
            self.model?.rawValue ?? ""
        ].joined(separator: "&")
        
        let creditsItem =  creditsManager.getItem(byUnionId: creditsItemKey)
        
        self.credits = creditsItem?.credit
    }
    
    func onSubmit() {
        guard !isLoading else { return }
        self.transactionID = nil
        checkFormData()
        Task{
            
            isLoading = true
            defer { isLoading = false }
            
            do {
                let req = StableDiffusion3_5ImageReq(
                    threadId: threadId,
                    prompt: prompt,
                    mode: mode.rawValue,
                    imageUrl: imageUrl,
                    strength: strength,
                    aspect_ratio: aspect_ratio?.rawValue,
                    model: model?.rawValue,
                    output_format: output_format?.rawValue,
                    style_preset: style_preset?.rawValue,
                    negative_prompt: negative_prompt
                )
                
                let id = try await service.stableDiffusion_3_5Image(req: req)
                self.transactionID = id

            } catch {
                handle(error: error)
            }
        }
    }
    
    //MARK: - 校验
    private func checkFormData () {
        if self.mode == .text_to_image {
            self.imageUrl = nil
            self.strength = nil
        }
        if self.mode == .image_to_image {
            self.aspect_ratio = nil
            if self.imageUrl == nil || self.strength == nil {
                showError = true
                errorMessage = "请填入全部的必填项"
            }
        }
    }
    
    private func handle(error: Error) {
        if let networkError = error as? NetworkError {
            errorMessage = networkError.localizedDescription
        } else {
            errorMessage = error.localizedDescription
        }
        showError = true
    }
    
    func resetToDefaults(){
        self.threadId = ""
        self.prompt = ""
        self.mode = .text_to_image
        self.imageUrl = nil
        self.strength = nil
        self.aspect_ratio = .ratio1_1
        self.model = .sd3_5_large
        self.output_format = .jpeg
        self.style_preset = .style3DModel
        self.negative_prompt = ""
        
        self.isLoading = false
        self.showError = false
        self.errorMessage = ""
        self.transactionID = nil
    }
}
