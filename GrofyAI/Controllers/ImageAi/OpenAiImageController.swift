//
//  DalleImageController.swift
//  GrofyAI
//
//  Created by kissy on 2025/4/28.
//

import SwiftUI
import PhotosUI

@MainActor
class OpenAiImageController: ObservableObject {
    
    private let imageAiService = ImageAiService()
    private let creditsManager = CreditsManager()
    
    // MARK: - 输入参数
    @Published var threadId: String = ""
    @Published  var prompt: String = ""
    @Published  var background: OpenAiImageAi.Background? = nil
    @Published  var model: OpenAiImageAi.Model = .openAi_dall_e_3 {
        didSet {
            // 当 model 改变时，根据新的 model 设置一个合适的默认 quality
            setDefaultQualityForCurrentModel()
            consumeCredits()
        }
    }
    
    @Published  var size: OpenAiImageAi.Size = .ratio1_1 {
        didSet {
            consumeCredits()
        }
    }
    @Published  var quality: OpenAiImageAi.Quality = .standanrd {
        didSet {
            consumeCredits()
        }
    }
    @Published  var style: OpenAiImageAi.Style? = .natural
    
   
    
    // MARK: - 状态管理
    @Published  var isLoading: Bool = false
    @Published var showError: Bool = false
    @Published  var errorMessage: String?
    
    //MARK: 轮询 id
    @Published var transactionID: Int?
    
    //积分消耗
    @Published var credits: Int? = 0
    
    func setThreadId(){
        self.threadId = "thd-" + UUID().uuidString
    }
    
    private func setDefaultQualityForCurrentModel() {
        switch model {
        case .openAi_dall_e_3:
            // 如果模型是 DALL-E 3，默认质量设置为 "标准"
            quality = .standanrd
            size = .ratio1_1
            style = .natural
            
        case .openAI_gpt_image_1:
            // 如果模型是 GPT Image 1，默认质量设置为 "高"
            quality = .medium
            size = .ratio1_1
            style = nil
        }
    }
    
    init() {
        // 在初始化时也调用一次，确保初始状态一致
        setDefaultQualityForCurrentModel()
        consumeCredits()
    }

    //MARK: - 获取消耗积分
    func consumeCredits(){
        let creditsItemKey = [
            self.model.rawValue,
            self.quality.rawValue,
            self.size.rawValue
        ].joined(separator: "&")
        
        let creditsItem =  creditsManager.getItem(byUnionId: creditsItemKey)
        
        self.credits = creditsItem?.credit
    }
    
    
    // MARK: - 提交
    
    func onSubmit() {
        guard !isLoading else { return }
        // 重置之前的事务ID，以防用户快速连续点击
        transactionID = nil
        
        Task {
            isLoading = true
            defer { isLoading = false }
            
            do {
                let request = OpenAiImageReq(
                    threadId: threadId,
                    prompt: prompt,
                    background: self.background?.rawValue,
                    model: self.model.rawValue,
                    size: size.rawValue,
                    quality: quality.rawValue,
                    style: style?.rawValue
                )
                // 发送请求
                let id = try await imageAiService.openAiImage(req: request)
                self.transactionID = id
            } catch {
                handle(error: error)
            }
            
            isLoading = false
        }
        
    }
    
    // MARK: - 错误处理
    private func handle(error: Error) {
        if let networkError = error as? NetworkError {
            errorMessage = networkError.localizedDescription
        } else {
            errorMessage = error.localizedDescription
        }
        showError = true
    }
    
    // MARK: - 重置（可选）
    func resetToDefaults() {
        self.threadId = ""
        self.prompt = ""
        self.size = .ratio1_1
        self.quality = .standanrd
        self.style = .natural
        
        self.isLoading = false
        self.showError = false
        self.errorMessage = ""
        self.transactionID = nil
    }
    
    
}
