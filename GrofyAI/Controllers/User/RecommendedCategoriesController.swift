//
//  User.swift
//  GrofyAI
//
//  Created by kissy on 2025/7/14.
//
import SwiftUI
import Combine
import Foundation

@MainActor
class RecommendedCategoriesController: ObservableObject {
    
    // MARK: - 配置项 (初始化时设定)
        let agentType: AgentType
        
        // MARK: - Published 状态
        @Published var categoryList: [RecommendedCategoriesListRes] = []
        @Published var categoryId: String? = nil
        @Published var selectedArtwork: ArtWorksCell? = nil
    
        
        // 数据缓存和加载状态
        @Published var categoryDataCache: [String: [ArtWorksCell]] = [:]
        @Published var isLoading: [String: Bool] = [:] // 按 categoryId 跟踪加载状态
        
        // 错误处理
        @Published var showError: Bool = false
        @Published var errorMessage: String?
        
        // MARK: - 私有属性
        private let size: String = "10"
        private var current: String = "1" // 注意：可以考虑这个是否也应该按分类管理
        
        private var cancellables = Set<AnyCancellable>()
        private let userArtWork = UserService()
        
        // MARK: - 初始化
        // Controller 现在通过其特定的 agentType 进行初始化
        init(agentType: AgentType) {
            self.agentType = agentType
            
        }
    
    // MARK: - 数据获取
    
    func loadData() async {
        setupCategoryIdListener()
        
        // 创建后立即获取初始分类列表
        fetchCategories()
    }
    
    
    private func setupCategoryIdListener() {
        $categoryId
            .compactMap { $0 }
        // 确保 categoryList 不为空
            .filter { [weak self] _ in !(self?.categoryList.isEmpty ?? true) }
            .sink { [weak self] newCategoryId in
                self?.fetchDetailsIfNeeded(for: newCategoryId)
            }
            .store(in: &cancellables)
    }
    
    func fetchCategories() {
        Task {
            do {
                // 发送请求
                let request = RecommendedCategoriesListReq(
                    type: self.agentType.rawValue,
                    tenantId: AppConfig.Auth.tenantId
                )
                let fetchedCategories = try await userArtWork.userRecommendedCategoriesList(req: request)
                
                self.categoryList = fetchedCategories
                
                if let firstCategory = fetchedCategories.first {
                    self.categoryId = String(firstCategory.id)
                }
            } catch {
                handle(error: error)
            }
        }
        
    }
    
    
    func fetchDetailsIfNeeded(for categoryId: String) {
        // 如果正在加载，或者已经有数据了，就不再重复请求
        guard isLoading[categoryId] != true, categoryDataCache[categoryId] == nil else {
            return
        }
        
        print("Fetching data for category ID: \(categoryId)...")
        self.isLoading[categoryId] = true
        
        
        Task {
            do {
                let request = RecommendedCategoriesDetailReq(
                    size: self.size,
                    current: self.current,
                    agentType: self.agentType.rawValue,
                    categoryId: categoryId
                )
                // 发送请求
                let CategoriesDetails = try await userArtWork.userRecommendedCategoriesDetail(req: request)
//                self.categoryDetailList = CategoriesDetails
                
                self.categoryDataCache[categoryId] = CategoriesDetails
                self.isLoading[categoryId] = false
                
            } catch {
                handle(error: error)
            }
        }
    }
    
    private func handle(error: Error) {
        if let networkError = error as? NetworkError {
            errorMessage = networkError.localizedDescription
        } else {
            errorMessage = error.localizedDescription
        }
    }
    
}
