//
//  UserArtWorksPageController.swift
//  GrofyAI
//
//  Created by kissy on 2025/6/20.
//
import Foundation
import Combine

// MARK: - View-Specific Model



@MainActor
class UserArtWorksPageController: ObservableObject {
    
    @Published var size: String = "50"
    @Published var current: String = "1"{
        didSet {
            // 如果 current 的值不再是 "1"，说明用户翻页了，必须停止轮询
            if current != "1" {
                print("当前页不再是第一页，停止轮询。")
                stopPolling()
            }
        }
    }
    @Published var agentType: AgentType = .image
    
    @Published var results: UserArtWorksPageRes?
    @Published private(set) var artWorkSection: [ArtWorkSection] = []
    
    @Published var isLoading: Bool = false // 初始值设为 false，在请求开始时设为 true
    @Published var isPolling: Bool = false // 新增一个状态来表示是否正在轮询
    @Published var onViewPolling: Bool = true
    @Published var errorMessage: String?
   
    
    private let userArtWork = UserService()
    private var cancellables = Set<AnyCancellable>()
   
    // 用于控制轮询任务的 Task 句柄
    private var pollingTask: Task<Void, Never>?
        
    
    /// 用户触发的主要数据获取方法 (例如：下拉刷新或初次加载)
    @MainActor
    func refreshData() async {
        // 下拉刷新总是重置到第一页
        self.current = "1"
        
        // 在开始新的用户请求时，总是先停止之前的轮询
        stopPolling()
        
        // 不再需要 Task { } 包装，因为方法本身就是 async
        self.isLoading = true
        await fetchAndDecidePolling()
        self.isLoading = false
    }

    // 修改 generateImage 以复用此逻辑
    func generateImage() {
        Task {
            await refreshData()
        }
    }
    
    //MARK: - 删除作品
    func deleteItems (ids:Set<ArtWorksCell.ID>) async {
        do {
            let res = try await userArtWork.userDeleteArtWorks(ids: ids)
            if res.code == 200 {
                results = nil
                await refreshData()
            }
        }catch {
            handle(error: error)
        }
        
    }

    //MARK: - 获取数据并根据结果决定是否开始或停止轮询
    private func fetchAndDecidePolling() async {
        self.errorMessage = nil // 清除旧的错误信息
        
        do {
            let request = UserArtWorksPageReq(
                size: size,
                current: current,
                agentType: agentType.rawValue
            )
            let data = try await userArtWork.userArtWorksPage(req: request)
                        
            // 如果 self.results 已经有数据 (即不是第一次加载)，则进行智能合并
            updateResults(with: data)
            
//            self.results = data
            
            // 分析结果
            let flatArtworks = data.values.flatMap { $0 }
            let hasRunningTasks = !flatArtworks.filter { $0.status == UserArtWorkRes.Status.running }.isEmpty
            
            print("数据获取成功。是否有正在运行的任务: \(hasRunningTasks)，当前页: \(self.current)")
            print(hasRunningTasks,data)
            
            // 决策逻辑：只有在第一页且有正在运行的任务时，才启动或继续轮询
            if self.current == "1" && hasRunningTasks && onViewPolling {
                startPolling()
            } else {
                // 任何其他情况都应停止轮询（例如，任务都完成了，或不在第一页）
                stopPolling()
            }
            
        } catch {
            // 如果在获取数据时出错，也应该停止轮询
            print("获取数据时出错，停止轮询。")
            handle(error: error)
            stopPolling()
        }
    }
    
    //MARK: -  启动轮询循环
    private func startPolling() {
        // 如果已经在轮询，则不重复启动
        guard pollingTask == nil else {
            print("轮询任务已在运行中。")
            return
        }
        
        print("检测到正在运行的任务，开始轮询...")
        isPolling = true
        
        pollingTask = Task {
            // 只要任务没有被取消，就一直循环
            while !Task.isCancelled {
                do {
                    // 设置轮询间隔，例如 5 秒
                    try await Task.sleep(for: .seconds(10))
                    
                    // 在睡眠之后，再次检查任务是否已被取消
                    // 这很重要，因为在睡眠期间，用户可能已经翻页或离开了视图
                    guard !Task.isCancelled else { break }
                    
                    print("轮询：再次获取数据...")
                    // 在轮询中获取数据，但不再显示全屏加载动画(isLoading=false)
                    await fetchAndDecidePolling()
                    
                } catch {
                    // Task.sleep 在任务被取消时会抛出 CancellationError
                    // 我们捕获它并正常退出循环
                    print("轮询任务被取消或中断。")
                    break
                }
            }
            // 循环结束后，确保状态被重置
            // 使用 @MainActor 确保 UI 更新在主线程
            await MainActor.run {
                self.isPolling = false
            }
            print("轮询循环已结束。")
        }
    }
    // MARK: - 错误处理
    private func handle(error: Error) {
        if let networkError = error as? NetworkError {
            errorMessage = networkError.localizedDescription
        } else {
            errorMessage = error.localizedDescription
        }
    }
    
    //MARK: - 停止轮询
    func stopPolling() {
        if pollingTask != nil {
            print("正在取消轮询任务...")
            pollingTask?.cancel()
            pollingTask = nil
            // 更新 UI 状态
            isPolling = false
        }
    }
    
    //MARK: - updateResults
    private func updateResults(with newCollection: UserArtWorksPageRes) {
        // 如果是首次加载或翻页，则直接替换
        if self.results == nil || self.current != "1" {
            self.results = newCollection
        } else {
            // 否则进行智能合并
            merge(newCollection: newCollection)
        }
        
        // 在数据更新后，安全地调用转换方法
        if let currentResults = self.results {
           transformDataForDisplay(from: currentResults)
        }
    }

    //MARK: - 合并新旧数据
    private func merge(newCollection: UserArtWorksPageRes) {
        // 确保当前有数据可以合并，否则直接用新数据替换
        guard var currentResults = self.results else {
            self.results = newCollection
            return
        }

        // 遍历新的数据集合 (按日期)
        for (date, newArtworksForDay) in newCollection {
            
            // 检查当前结果中是否已有这一天的数据
            if let currentArtworksForDay = currentResults[date] {
                // 如果有，则开始智能合并这一天的数据
                
                // 为了高效查找，将当前这一天的数组转换为一个以 id 为键的字典
                // [ArtWorksCell] -> [Int: ArtWorksCell]
                var currentArtworksDict = Dictionary(uniqueKeysWithValues: currentArtworksForDay.map { ($0.id, $0) })
                
                var hasChanges = false
                
                // 遍历当天获取到的新艺术品
                for newArtwork in newArtworksForDay {
                    // 检查这个新艺术品是否已经存在于当前数据中
                    if let existingArtwork = currentArtworksDict[newArtwork.id] {
                        // --- 核心逻辑在这里 ---
                        // 如果状态是 "running"，或者新旧数据不相等，就执行覆盖
                        //                        if newArtwork.status == "running" || newArtwork != existingArtwork {
                        if newArtwork != existingArtwork {
                            currentArtworksDict[newArtwork.id] = newArtwork
                            hasChanges = true
                        }
                    } else {
                        // 如果不存在，说明是全新的任务，直接添加到字典中
                        currentArtworksDict[newArtwork.id] = newArtwork
                        hasChanges = true
                    }
                }

                // 如果发生了变化，才更新数据源
                if hasChanges {
                    // 将更新后的字典转换回数组，并保持一个稳定的排序（例如按更新日期倒序）
                    let updatedArtworksForDay = Array(currentArtworksDict.values).sorted { $0.updateDate > $1.updateDate }
                    currentResults[date] = updatedArtworksForDay
                }

            } else {
                // 如果是全新的一天的数据，直接添加
                // 同样，最好也进行一次排序
                currentResults[date] = newArtworksForDay.sorted { $0.updateDate > $1.updateDate }
            }
        }
        
        // 用合并后的结果更新 @Published 属性，这将触发 UI 刷新
        self.results = currentResults
    }
    
    
    
    func transformDataForDisplay(from dictionary: UserArtWorksPageRes) {
        
        // 创建日期格式化工具
        let inputFormatter = DateFormatter()
        inputFormatter.dateFormat = "yyyy-MM-dd"
        
        let dateOutputFormatter = DateFormatter()
//        dateOutputFormatter.dateFormat = "yyyy年M月d日"
        dateOutputFormatter.dateFormat = "MM-dd,yyyy"
        
        let weekdayOutputFormatter = DateFormatter()
        weekdayOutputFormatter.dateFormat = "EEEE" // EEEE 会输出 "星期五"
        
        // 将字典转换为 ArtWorkSection 数组
        let sections = results!.compactMap { (dateString, artworks) -> ArtWorkSection? in
            // 将 "2025-06-20" 这样的字符串转换为 Date 对象
            guard let date = inputFormatter.date(from: dateString) else {
                return nil // 如果日期格式不對，则忽略这一组数据
            }
            
            // 创建并返回一个分区对象
            return ArtWorkSection(
                id: date,
                dateText: dateOutputFormatter.string(from: date),
                weekdayText: weekdayOutputFormatter.string(from: date),
                artworks: artworks
            )
        }
        
        // 按照日期从新到旧（降序）排序
        artWorkSection = sections.sorted { $0.id > $1.id }
    }

}
