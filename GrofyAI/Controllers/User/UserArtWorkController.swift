//
//  UserArtWorkController.swift
//  GrofyAI
//
//  Created by kissy on 2025/6/20.
//
import Photos
import Foundation


enum PhotoLibraryError: Error, LocalizedError {
    case accessDenied
    case accessNotDetermined
    case unknown
    
    var errorDescription: String? {
        switch self {
        case .accessDenied:
            return "相册访问被拒绝。请在系统设置中允许本应用访问相册。"
        case .accessNotDetermined:
            return "无法确定相册权限状态。"
        case .unknown:
            return "发生了未知的相册错误。"
        }
    }
}


@MainActor
class UserArtWorkViewModel: ObservableObject {
    
    @Published var pollingResult: UserArtWorkRes?
    @Published var isLoading: Bool = true
    @Published var errorMessage: String?
    
    let transactionID: Int?
    private let userArtWork = UserService()
    let fileService = FileService()
    
    // 用于控制轮询任务的 Task 句柄
    private var pollingTask: Task<Void, Never>?
    
    init(transactionID: Int?) {
        self.transactionID = transactionID
    }
    
    func startPolling() {
        
        if transactionID == nil { return }
        // 防止重复启动
        guard pollingTask == nil || transactionID == nil else { return }
        
        isLoading = true
        
        pollingTask = Task {
            defer {
                // 确保任务结束时 isLoading 为 false
                self.isLoading = false
            }
            
            do {
                // 只要任务没有被取消，就一直循环
                while !Task.isCancelled {
                    let result = try await userArtWork.userArtWork(req: UserArtWorkReq(id: transactionID!))
                    self.pollingResult = result
                    
                    
                    // 如果状态是成功或失败，就停止轮询
                    if result.status == .success || result.status == .failed {
                        print("轮询结束，状态: \(result.status!)")
                        break
                    }
                    
                    // 等待2秒再进行下一次轮询
                    try await Task.sleep(for: .seconds(2))
                }
            } catch {
                // 捕获轮询过程中的错误
                self.errorMessage = error.localizedDescription
                print("轮询过程中发生错误: \(error.localizedDescription)")
            }
        }
    }
    
    
    func stopPolling() {
        pollingTask?.cancel()
        pollingTask = nil
    }
    
    
    func download() async  {
        do {
            ToastManager.shared.showInfo("正在下载")
            guard let url = pollingResult?.links?.first else { return }
            print("开始准备下载文件: \(url)")
            let req = DownloadReq(url: url)
            
            // 1. 调用服务，下载文件并获取本地临时文件的 URL
            // FileService 内部处理了下载和写入临时文件的逻辑
            let data = try await fileService.download(req: req)
            let localVideoURL = FileManager.default.temporaryDirectory.appendingPathComponent(UUID().uuidString).appendingPathExtension("mp4")
            try data.write(to: localVideoURL)
            
            print("文件临时保存在: \(localVideoURL.path)")
            
            //将本地的视频文件保存到系统相册
            try await saveVideoToPhotos(from: localVideoURL)
            
            //清理临时文件
            try? FileManager.default.removeItem(at: localVideoURL)
            print("临时文件已清理。")
            ToastManager.shared.showSuccess("下载成功")
        }catch {
            // 捕获整个过程中任何地方抛出的错误
            errorMessage = "操作失败: \(error.localizedDescription)"
            print("错误详情: \(error)")
            ToastManager.shared.showError("下载失败")
        }
    }
    
    private func saveVideoToPhotos(from videoURL: URL) async throws {
        // 请求“仅添加”权限
        let status = await PHPhotoLibrary.requestAuthorization(for: .addOnly)
        
        switch status {
        case .authorized, .limited:
            // 用户已授权，可以执行保存
            try await PHPhotoLibrary.shared().performChanges {
                // 创建一个从文件 URL 添加视频的请求
                PHAssetChangeRequest.creationRequestForAssetFromVideo(atFileURL: videoURL)
            }
            print("PHPhotoLibrary.performChanges 已成功执行。")
            
        case .denied, .restricted:
            // 用户拒绝了权限
            throw PhotoLibraryError.accessDenied
            
        case .notDetermined:
            // 这种情况理论上在 await 之后不会发生，但为了完整性加上
            throw PhotoLibraryError.accessNotDetermined
            
        @unknown default:
            throw PhotoLibraryError.unknown
        }
    }
}
