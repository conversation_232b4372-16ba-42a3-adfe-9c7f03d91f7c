//
//  ImageRecognitionChatController.swift
//  GrofyAI
//
//  Created by kissy on 2025/7/8.
//

import SwiftUI
import Combine

// MARK: - 图片识别聊天控制器

enum ChatMode_graphId:String {
    case imageRecognition = "vision_graph"
}

@MainActor
class ImageRecognitionChatController: ObservableObject {
    
    // MARK: - 服务的依赖注入
    private let chatService: ChatService
    private let fileService: FileService
    private let threadManager = ThreadManager.shared
    
    private let streamingManager = StreamingContentManager()
    private let messageManager = MessageDataManager()
    
    // MARK: - 任务管理
    private var currentStreamTask: Task<Void, Never>?
    private var uploadTask: Task<Void, Never>?
    private var currentStreamingMessageId: UUID?
    
    ///输入框内容
    @Published var inputText = ""
    
    //MARK: -状态
    @Published var isLoading = false
    @Published var loadingType: ChatLoadingType = .none
    @Published var errorMessage = ""
    @Published var showError = false
    var uploadLoading = false
    
    // MARK: - 聊天核心状态
    /// 消息列表
    @Published private(set) var stableMessages: [ChatMessageModel] = []

    /// 当前流式消息（独立管理，高频更新）
    @Published private(set) var streamingMessage: ChatMessageModel?

    /// 流式内容缓冲区（用于增量更新）
    @Published private(set) var streamingContent = ""


    // MARK: - 图片识别专用状态
        
    /// 用户新选择的、尚未上传的图片
    @Published var newPickedImages: [UIImage] = []
    
    ///记录处理后的图片
    var handlePickedIamges: [(UIImage, String)] = []
    
    /// 当前选择的识别类型
    @Published var selectedVisionType: VisionType = .vision
    
    /// 已上传并用于当前对话的图片文件信息 (用于构建消息体)
    @Published var uploadedImageFiles: [UploadImagesRes] = []
    
    /// 一个信号，通知UI在消息发送后清空已上传的图片预览
    @Published var shouldClearUploadedImages = false
    

    let chatMode_graphId: ChatMode_graphId = .imageRecognition
    
    // MARK: - 流式和重试状态
    private var isRetryingMessage = false
    private var retryingMessageId: UUID?
    private var retryingVariantIndex: Int?
    
    // MARK: - 初始化和销毁
    init(
        chatService: ChatService = ChatService(),
        fileService: FileService = FileService()
    ) {
        self.chatService = chatService
        self.fileService = fileService
    }
    
    /// 当 Controller 被销毁时，取消所有正在进行的任务
    /// 停止所有正在进行的任务。
    deinit {
        uploadTask?.cancel()
        currentStreamTask?.cancel()
        
        uploadTask = nil
        currentStreamTask = nil
    }

    /// 获取当前重试的消息ID（仅用于UI）
    var currentRetryingMessageId: UUID? {
        return isRetryingMessage ? retryingMessageId : nil
    }
    
    // MARK: - 公共方法
    
    // 发送消息（核心入口）
    /// 此方法会先检查是否有新图片需要上传，完成后再发送聊天请求。
    // MARK: sendMessage
    func sendMessage() {
        // 设置加载状态
        isLoading = true
        loadingType = .streaming
        
        streamingMessage = createStreamingMessage()
        currentStreamingMessageId = streamingMessage?.id
        streamingContent = ""

        if !uploadedImageFiles.isEmpty {
            // **情况1: 发送图片**
            // 文本内容强制为空字符串，符合业务规则。
            let textForThisMessage = ""
            let imagesForThisMessage = uploadedImageFiles

            // 清空暂存区和输入框
            self.uploadedImageFiles.removeAll()
            self.inputText = ""
            
            // 发送网络请求
            Task {
                await sendImageRecognitionRequest(
                    message: textForThisMessage,
                    stagedFiles: imagesForThisMessage,
                    isRetry: false,
                    isFirst: true
                )
            }
        } else {
            // **情况2: 发送纯文本消息**
            let trimmedInput = inputText.trimmingCharacters(in: .whitespacesAndNewlines)
            
            // 必须有文本内容才能发送
            guard !trimmedInput.isEmpty else { return }

            // 添加用户消息（只有文本，没有图片）
            addUserMessage(content: trimmedInput, images: []) // 传入空数组
            
            // 清空输入框
            self.inputText = ""
            
            // 发送网络请求
            Task {
                await sendImageRecognitionRequest(
                    message: trimmedInput,
                    stagedFiles: [], // 传入空数组
                    isRetry: false,
                    isFirst: false
                )
            }
        }
    }
    
    /// 获取重试期间的变体信息
    func getRetryVariantInfo() -> (current: Int, total: Int)? {
        guard isRetryingMessage,
              let retryingId = retryingMessageId,
              let message = findMessage(by: retryingId)
        else {
            return nil
        }

        let existingVariantsCount = message.variants?.count ?? 1
        let totalCount = existingVariantsCount + 1

        return (current: totalCount - 1, total: totalCount)
    }
    
    /// 获取消息变体信息
    func getMessageVariantInfo(messageId: UUID) -> (current: Int, total: Int)? {
        guard let message = findMessage(by: messageId),
              let variants = message.variants
        else {
            return nil
        }

        return (current: message.currentVariantIndex, total: variants.count)
    }
    
    /// 切换消息变体（支持正常和重试场景）
    func switchMessageVariant(messageId: UUID, variantIndex: Int) {
        guard let messageIndex = findMessageIndex(by: messageId),
              let variants = stableMessages[messageIndex].variants,
              variantIndex < variants.count
        else {
            return
        }

        let selectedVariant = variants[variantIndex]
        let updatedMessage = createMessageFromVariant(
            originalMessage: stableMessages[messageIndex],
            variant: selectedVariant,
            variantIndex: variantIndex
        )

        stableMessages[messageIndex] = updatedMessage

        // 如果是重试场景，记录用户选择
        if isRetryingMessage, retryingMessageId == messageId {
            retryingVariantIndex = variantIndex
        }
    }
    

    /// 检查是否正在重试指定消息
    func isRetryingMessage(messageId: UUID) -> Bool {
        return isRetryingMessage && retryingMessageId == messageId
    }
    /// 检查是否正在重试任何消息
    func isCurrentlyRetrying() -> Bool {
        return isRetryingMessage
    }
    
    /// 重置当前会话线程
    func resetCurrentThread(clearSession: Bool = true) {
        threadManager.clearCurrentSession()
    }
    
    /// 复制消息内容
    /// - Parameter message: 要复制的消息
    func copyMessage(_ message: ChatMessageModel) {
        UIPasteboard.general.string = message.content
        ToastManager.shared.showSuccess("消息已复制")
    }
    
    //重新生成
    func retryMessage(messageId: UUID) {
        // 1. 检查是否已在加载中，防止重复触发
        guard !isLoading else {
            print("ChatController: 正在处理另一项请求，请稍后重试。")
            return
        }
        
        print("ImageRecognitionChatController: 准备重试消息 \(messageId)")
        
        // 2. 在消息列表中找到要重试的AI消息
        guard let messageIndex = findMessageIndex(by: messageId),
              !stableMessages[messageIndex].isUser
        else { return }
        
        //重试状态
        // 5. 设置加载和重试状态，为新的流式请求做准备
        isRetryingMessage = true
        errorMessage = ""
        showError = false
        
        setupRetryState(for: stableMessages[messageIndex])
        
        if let userMessageIndex = findPreviousUserMessage(before: messageIndex) {
            let userMessage = stableMessages[userMessageIndex]
            print("将针对用户消息进行重试。文本: '\(userMessage.content)', 图片数: \(userMessage.imageAttachments.count)")
            
            
            startRetryRequest(for: userMessage)
        }else{
            print("❌ ImageRecognitionChatController: 未能找到与要重试的AI消息对应的用户消息。")
            return
        }
    }
    
    //MARK: - loadHistoryChat
    /// 加载历史对话
    func loadHistoryChat(threadId: String) {
        print("ChatController: 开始加载历史对话 - threadId: \(threadId), chatMode: \(chatMode_graphId)")
        
        stopStreaming()
        
        isLoading = true
        loadingType = .loadingHistory
        //重置重试状态
        isRetryingMessage = false
        //重置重试消息 ID
        retryingMessageId = nil
        //清空消息列表
        stableMessages.removeAll()
        //清除当前消息
        streamingMessage = nil
        //清除AI返回的文本
        streamingContent = ""
        //重置当前流式消息 ID
        currentStreamingMessageId = nil
        
        errorMessage = ""
        showError = false
        
        // 清空缓存
        messageManager.clearCache()

        Task {
            await performLoadHistoryChat(threadId: threadId)
        }
    }
    
    //MARK: - stopStreaming
    /// 停止当前流式响应。
    /// 这会取消正在进行的网络任务，并重置相关的加载状态。
    func stopStreaming() {
        guard currentStreamTask != nil || isLoading else {
            return
        }
        
        print("ChatController: 停止流式响应")
        
        currentStreamTask?.cancel()
        uploadTask?.cancel()
        
        currentStreamTask = nil
        uploadTask = nil
        
        isLoading = false
        loadingType = .none
        
        // 尝试断开SSE连接
        Task {
            await EventSourceAdapter.shared.disconnect()
            
            let isDisconnected = EventSourceAdapter.shared.isFullyDisconnected
            
            if !isDisconnected {
                print("ChatController: SSE连接可能未完全断开")
                print(EventSourceAdapter.shared.debugConnectionInfo)
            } else {
                print("ChatController: SSE连接已完全断开")
            }
        }
    }

    func claerPickedImages (){
        print("清除以选中的照片")
        newPickedImages.removeAll()
        handlePickedIamges.removeAll()
    }
    
    //MARK: - clearAllState
    /// 清理整个聊天界面和状态。
    func clearAllState() {
        stopStreaming()
        stableMessages.removeAll()
        resetCurrentThread()
        
        inputText = ""
        uploadedImageFiles.removeAll()
        threadManager.clearCurrentSession()
        resetLoadingState()
        claerPickedImages()
        
        errorMessage = ""
        showError = false
        isRetryingMessage = false
        retryingMessageId = nil
        print("所有状态已清理。")
    }
    
    //MARK: uploadInitialImages
    /// 在初始化时自动调用的上传方法。
    func uploadInitialImages() {
        guard uploadTask == nil else { return }
        guard !newPickedImages.isEmpty else {return}
        
        isLoading = true
        uploadLoading  = true
        loadingType = .uploading
        
        let UserMessageId = UUID()
        
        uploadTask = Task {
            do {
                print("自动上传 \(newPickedImages.count) 张初始图片...",threadManager.getCurrentThreadId())
                for image in newPickedImages {
                    do {
                        // 调用你提供的公共压缩方法
                        let processed = try compressionUIImage(originalImage: image, name: nil)
                        handlePickedIamges.append((processed.image, processed.fileName))
                    } catch {
                        // 如果单张图片处理失败，打印日志并跳过
                        print("⚠️ 警告：一张图片处理失败，已跳过。错误: \(error.localizedDescription)")
                    }
                }
                
                // 如果所有图片都处理失败了
                guard !handlePickedIamges.isEmpty else {
                    // 抛出一个错误，会在下面的 catch 块中被捕获
                    throw NSError(domain: "ImageProcessingError", code: -2, userInfo: [NSLocalizedDescriptionKey: "所有图片都处理失败，无法上传。"])
                }
                
                handleUserFirestMessage( id:UserMessageId,images: [], localImages: handlePickedIamges)
                
                // 检查任务是否在耗时的图片处理后被取消了
                try Task.checkCancellation()
                
                let results = try await fileService.uploadImages(
                    images: handlePickedIamges,
                    threadId: threadManager.getCurrentThreadId(),
                    format: .jpeg(compressionQuality: 0.8)
                )
                try Task.checkCancellation()
                print("✅ \(results.count) 张图片自动上传成功。")
                handleUserFirestMessage( id:UserMessageId, images: results, localImages: handlePickedIamges)
                
                self.uploadedImageFiles.append(contentsOf: results)
            } catch is CancellationError {
                print("图片上传任务被取消。")
            } catch {
                print("❌ 图片上传失败: \(error.localizedDescription)")
                self.errorMessage = "图片上传失败: \(error.localizedDescription)"
                self.showError = true
            }
            
            resetLoadingState()
            self.uploadTask = nil
            
            //上传完毕后 发送请求 进行识别
            sendMessage()
        }
    }
    
    // MARK: - 私有核心逻辑
    
    
    
    private func findMessage(by id: UUID) -> ChatMessageModel? {
        if let index = findMessageIndex(by: id) {
            return stableMessages[index]
        }
        return nil
    }
    
    private func findMessageIndex(by id: UUID) -> Int? {
        return messageManager.findMessageIndex(by: id, in: stableMessages)
    }
    
    ///找寻重试消息的前一条用户消息
    private func findPreviousUserMessage(before index: Int) -> Int? {
        for i in stride(from: index - 1, through: 0, by: -1) {
            if stableMessages[i].isUser {
                return i
            }
        }
        return nil
    }
    
    
    private func handleUserFirestMessage(id:UUID,images: [UploadImagesRes],localImages:[(UIImage,String)]) {
        //为空，添加本地图片数据
        if images.isEmpty {
            let messageImages = localImages.map { serverImage -> MessageImage in
                // 如果找到了，matchedLocalImage 就是对应的 UIImage；如果没找到，它就是 nil。
                // 这正是 MessageImage 初始化器所期望的。
                return MessageImage(
                    fileId: id.uuidString,
                    fileName: serverImage.1,
                    fileUrl: "",
                    localImage: serverImage.0 // <-- 将查找到的图片或 nil 传进去
                )
            }
            
            let userMessage = ChatMessageModel(id: id, content: "", isUser: true, images: messageImages.isEmpty ? nil : messageImages)
            
            if !stableMessages.contains(where: { $0.id == id }) {
                stableMessages.append(userMessage)
            }
        } else {
            //修改对应数据
            //根据 fileid == id &&images中的originalName 与 fileName 匹配进行修改 ChatMessageModel 与 MessageImage
            guard let messageIndex = stableMessages.firstIndex(where: { $0.id == id }) else {
                print("⚠️ 警告：无法找到要更新的消息，ID: \(id.uuidString)")
                return
            }
            
            // 2. 为了更高效地匹配，将服务器返回的 images 数组转换成一个字典
            //    Key: originalName, Value: UploadImagesRes
            let serverImageMap = Dictionary(uniqueKeysWithValues: images.map { ($0.originalName, $0) })
            
            // 3. 获取旧的消息实例
            var messageToUpdate = stableMessages[messageIndex]
            
            // 4. 更新消息中的图片数组
            if var existingImages = messageToUpdate.images {
                // 遍历每一张需要更新的图片
                for i in 0..<existingImages.count {
                    let oldImage = existingImages[i]
                    
                    // 在字典中查找匹配的服务器数据
                    if let serverData = serverImageMap[oldImage.fileName] {
                        // 如果找到了，用服务器数据更新这张图片的信息
                        // 因为 MessageImage 是 struct，我们创建一个新实例来替换
                        existingImages[i] = MessageImage(
                            fileId: String(serverData.id), // 使用服务器返回的真实 fileId
                            fileName: serverData.originalName,
                            fileUrl: serverData.url, // 使用服务器返回的真实 url
                            localImage: oldImage.localImage // 保留本地预览图
                        )
                    }
                }
                // 将更新后的图片数组赋值回消息模型
                messageToUpdate.images = existingImages
            }
            // 5. 用更新后的消息模型替换掉数组中的旧模型
            stableMessages[messageIndex] = messageToUpdate
        }
    }

    //MARK: - createMessageFromVariant
    //创建消息变体
    private func createMessageFromVariant(
        originalMessage: ChatMessageModel,
        variant: MessageVariantModel,
        variantIndex: Int
    ) -> ChatMessageModel {
        return ChatMessageModel(
            id: originalMessage.id,
            content: variant.content,
            isUser: originalMessage.isUser,
            type: originalMessage.type,
            modelId: variant.modelId,
            searchResults: variant.searchResults,
            files: variant.files,
            images: variant.images,
            reasoningContent: variant.thinkingContent,
            toolCallStatus: originalMessage.toolCallStatus,
            isToolActive: originalMessage.isToolActive,
            variants: originalMessage.variants,
            currentVariantIndex: variantIndex,
            chatMode: originalMessage.chatMode
        )
    }
    
    /// 执行历史对话加载
    // MARK: performLoadHistoryChat
    private func performLoadHistoryChat(threadId: String) async {
        do {
            let historyItems = try await chatService.getHistoryDetail(threadId: threadId, graphId: chatMode_graphId.rawValue)
            
            await MainActor.run {
                let chatMessages = convertHistoryItemsToChatMessages(historyItems)
                
                self.stableMessages = chatMessages
                self.streamingMessage = nil
                
                // 一次性重建索引缓存（历史消息加载完成后）
                self.messageManager.updateIndexCache(for: chatMessages)
                
                // 设置当前线程
                self.threadManager.setCurrentThread(threadId: threadId, isFirst: false)
                
                self.isLoading = false
                self.loadingType = .none
            }
            
        } catch {
            await MainActor.run {
                self.isLoading = false
                self.loadingType = .none
                let errorMsg = if let businessError = error as? BusinessError {
                    businessError.message
                } else {
                    error.localizedDescription
                }
                self.errorMessage = "加载历史对话失败: \(errorMsg)"
                self.showError = true
                
                print("❌ ChatController: 加载历史对话失败 - \(error)")
            }
        }
    }
    
    
    private func startRetryRequest(for message: ChatMessageModel) {
        isLoading = true
        loadingType = .streaming

        streamingMessage = createStreamingMessage()
        currentStreamingMessageId = streamingMessage?.id
        streamingContent = ""

        // 将 MessageImage 结构转换回请求所需的 UploadImagesRes 结构
        let filesToRetry = message.imageAttachments.map { imageAttachment in
            UploadImagesRes(
                id: Int(imageAttachment.fileId) ?? 0,
                fileId: imageAttachment.fileId,
                name: imageAttachment.fileName,
                originalName: imageAttachment.fileName,
                url: imageAttachment.fileUrl
            )
        }
        
        Task {
            await sendImageRecognitionRequest(
                message: message.content,
                stagedFiles: filesToRetry,
                isRetry: true, // 标记为重试请求
                isFirst: false
            )
        }
    }
    
    
    private func createStreamingMessage() -> ChatMessageModel {
        return ChatMessageModel(
            content: "",
            isUser: false
        )
    }

    private func convertHistoryItemsToChatMessages(_ historyItems: [HistoryDetailItem]) -> [ChatMessageModel] {
        var chatMessages: [ChatMessageModel] = []

        for item in historyItems {
            if let chatMessage = item.handleMessage() {
                chatMessages.append(chatMessage)
            }
        }

        return chatMessages
    }
    
    /// 设置当前线程ID（用于历史对话）
    private func setCurrentThread(threadId: String) {
        threadManager.setCurrentThread(threadId: threadId, isFirst: false)
    }
    
    
    /// 创建请求对象。
    private func createImageRecognitionRequest(message: String, stagedFiles: [UploadImagesRes], isRetry: Bool,isFirst: Bool) -> ImageRecognitionReq {
        let threadId = threadManager.getCurrentThreadId()
        
        return ImageRecognitionReq(
            thread_id: threadId,
            uploaded_images: stagedFiles.count == 0 ? nil : stagedFiles.map { file in
                UploadedImageInfo(
                    file_id: String(file.id),
                    file_name: file.name,
                    file_url: file.url
                )
            },
            vision_type: selectedVisionType,
            messages: message,
            is_first: isFirst
        )
    }
    
    /// 发送图片识别请求的内部实现。
    private func sendImageRecognitionRequest(message: String, stagedFiles: [UploadImagesRes], isRetry: Bool,isFirst:Bool) async {
        currentStreamTask?.cancel()
        
        if !threadManager.hasActiveSession() {
            threadManager.startNewSession()
        }
        
        let request = createImageRecognitionRequest(
            message: message,
            stagedFiles: stagedFiles,
            isRetry: isRetry,
            isFirst:isFirst
        )
        
        currentStreamTask = Task { @MainActor in
            defer { currentStreamTask = nil }
            
            let stream = chatService.startImageRecognitionChatStream(req: request)
            
            for await response in stream {
                if Task.isCancelled {
                    print("ImageRecognitionChatController: 任务被取消。")
                    await EventSourceAdapter.shared.disconnect()
                    break
                }
                print("结果:",response)
                handleImageRecognitionStreamResponse(response)
            }
            
            handleStreamCompleted()
        }
    }
    
    /// 用于处理传入流数据的顶层分发函数。
    private func handleImageRecognitionStreamResponse(_ response: Res<ImageRecognitionRes>) {
//        // 处理试用结束错误
//        if response.code == 10000 {
//            var content = ""
//            switch response.data {
//            case .text(_, let textContent):
//                content = textContent
//            default:
//                content = response.msg
//            }
//            handleTrialEndedError(content)
//            return
//        }
        
        // 1. 首先检查业务错误码
        guard response.code == 200 else {
            handleError(BusinessError(code: response.code, message: response.msg))
            currentStreamTask?.cancel()
            return
        }
        
        // 2. 处理成功返回的数据，根据 ImageRecognitionRes 的类型进行分发
        switch response.data {
        case .status(let statusResponse):
            // 将状态事件分发给状态处理器
            handleRecognitionStatus(statusResponse)
            
        case .text(let textResponse):
            // 将文本内容分发给内容处理器
            accumulateTextContent(textResponse.content ?? "")
        }
    }
    
    /// 处理图片识别过程中的状态事件。
    private func handleRecognitionStatus(_ status: ImageRecognitionStreamStatusRes) {
        print("ImageRecognitionChatController: 收到状态事件 - event: \(status.event ?? ""), node: \(status.node ?? "")")
        
        // 你可以根据不同的事件更新UI，比如显示一个更具体的状态文本
        switch status.event {
        case "on_chain_start":
            print("识别流程开始...")
            // 可以在这里更新 currentAIMessage 的内容为一个更友好的提示

        case "on_chain_end":
            print("识别流程结束。")
            // 流程结束，等待最终文本，一般无需操作
        default:
            print("未知的状态事件: \(status.event ?? "")")
        }
    }
    
    private func accumulateTextContent(_ content: String) {
        guard streamingMessage != nil else {
            ensureStreamingMessage()
            return
        }
        
        streamingManager.appendContent(content) { [weak self] bufferedContent in
            self?.updateStreamingMessageContent(bufferedContent)
        }
    }
    
    
    private func updateStreamingMessageContent(_ bufferedContent: String) {
        guard let currentMessage = streamingMessage else { return }

        let updatedContent = currentMessage.content + bufferedContent

        streamingContent = updatedContent

        streamingMessage = ChatMessageModel(
            id: currentMessage.id,
            content: updatedContent,
            isUser: false,
            type: .text,
            searchResults: currentMessage.searchResults,
            files: currentMessage.files,
            images: currentMessage.images,
            reasoningContent: currentMessage.reasoningContent,
            variants: currentMessage.variants,
            currentVariantIndex: currentMessage.currentVariantIndex,
            chatMode: currentMessage.chatMode
        )
    }
    
    private func ensureStreamingMessage() {
        if streamingMessage == nil {
            streamingMessage = createStreamingMessage()
            currentStreamingMessageId = streamingMessage?.id
        }
    }
    
    /// 将用户消息添加到列表中。
    private func addUserMessage(content: String, images: [UploadImagesRes], localImages : [(UIImage,String)]? = nil ) {
                
        // --- 1. 创建一个从文件名到 UIImage 的查找字典 (核心优化点) ---
        // 这个字典让我们能够根据文件名快速找到对应的本地图片。
        let localImageMap: [String: UIImage] = Dictionary(
            uniqueKeysWithValues: (localImages ?? []).map { (image, name) in
                return (name, image)
            }
        )
        
        // --- 2. 遍历服务器返回的 images 数组，并创建 MessageImage 实例 ---
        let messageImages = images.map { serverImage -> MessageImage in
            // 从字典中查找与服务器文件名匹配的本地图片
            let matchedLocalImage = localImageMap[serverImage.originalName]
            
            // 如果找到了，matchedLocalImage 就是对应的 UIImage；如果没找到，它就是 nil。
            // 这正是 MessageImage 初始化器所期望的。
            return MessageImage(
                fileId: String(serverImage.id),
                fileName: serverImage.originalName,
                fileUrl: serverImage.url,
                localImage: matchedLocalImage // <-- 将查找到的图片或 nil 传进去
            )
        }
        
        let userMessage = ChatMessageModel(content: content, isUser: true, images: messageImages.isEmpty ? nil : messageImages)
        stableMessages.append(userMessage)
    }
    
    
    //MARK: - resetLoadingState
    /// 重置文件上传状态。
    private func resetLoadingState() {
        uploadLoading = false
        loadingType = .none
    }
    
    //设置重试状态
    private func setupRetryState(for message: ChatMessageModel) {
        isRetryingMessage = true
        retryingMessageId = message.id
        retryingVariantIndex = message.currentVariantIndex
        objectWillChange.send()
    }
    
    private func handleError(_ error: BusinessError) {
        // 强制刷新缓冲区确保最后部分内容不丢失
        streamingManager.forceFlush { [weak self] remainingContent in
            if !remainingContent.isEmpty {
                self?.updateStreamingMessageContent(remainingContent)
            }
        }

        isLoading = false
        loadingType = .none
        errorMessage = error.message
        showError = true

        // 清理流式状态
        streamingMessage = nil
        currentStreamingMessageId = nil
        streamingContent = ""
        streamingManager.reset()

        // 清理重试状态
        if isRetryingMessage {
            finishRetryProcess()
        }
    }
    
    
    private func finishRetryProcess() {
        isRetryingMessage = false
        retryingMessageId = nil
        retryingVariantIndex = nil
        objectWillChange.send()
    }
     
    //MARK: - handleStreamCompleted
    /// 流式响应结束后的处理。
    private func handleStreamCompleted() {
        print("ImageRecognitionChatController: 流处理流程已结束。")
        // 强制刷新缓冲区确保最后部分内容不丢失
        streamingManager.forceFlush { [weak self] remainingContent in
            if !remainingContent.isEmpty {
                self?.updateStreamingMessageContent(remainingContent)
            }
        }
        
        finishCurrentAIMessage()
        
        isLoading = false
        loadingType = .none
        
        threadManager.continueCurrentSession()
        resetLoadingState()
        
        if isRetryingMessage {
            isRetryingMessage = false
            retryingMessageId = nil
        }
    }
    
    /// 在流结束后，完成对当前AI消息的处理。
    private func finishCurrentAIMessage() {
        guard let streaming = streamingMessage else { return }
        
        let finalMessage = ChatMessageModel(
            id: streaming.id,
            content: streaming.content,
            isUser: false,
            type: streaming.type,
            modelId: streaming.modelId,
            searchResults: streaming.searchResults,
            files: streaming.files,
            images: streaming.images,
            reasoningContent: streaming.reasoningContent,
            toolCallStatus: streaming.toolCallStatus,
            isToolActive: streaming.isToolActive,
            variants: streaming.variants,
            currentVariantIndex: streaming.currentVariantIndex,
            chatMode: streaming.chatMode
        )
        
        let newIndex = stableMessages.count
        stableMessages.append(finalMessage)
        
        messageManager.addMessageToCache(message: finalMessage, at: newIndex)
        
        streamingMessage = nil
        currentStreamingMessageId = nil
        streamingContent = ""
    }
}
    
   
/// 流式内容管理器 - 负责高效的流式内容处理
private class StreamingContentManager: ObservableObject {
    private var contentBuffer = ""
    private var bufferTimer: Timer?
    private let bufferFlushInterval: TimeInterval = 0.05 // 50ms 缓冲
    private let maxBufferSize = 100 // 最大缓冲字符数

    func appendContent(_ content: String, completion: @escaping (String) -> Void) {
        contentBuffer += content

        // 达到缓冲阈值或定时器触发时刷新
        if contentBuffer.count >= maxBufferSize {
            flushBuffer(completion: completion)
        } else {
            scheduleBufferFlush(completion: completion)
        }
    }

    private func scheduleBufferFlush(completion: @escaping (String) -> Void) {
        bufferTimer?.invalidate()
        bufferTimer = Timer.scheduledTimer(withTimeInterval: bufferFlushInterval, repeats: false) { [weak self] _ in
            self?.flushBuffer(completion: completion)
        }
    }

    private func flushBuffer(completion: @escaping (String) -> Void) {
        guard !contentBuffer.isEmpty else { return }

        let content = contentBuffer
        contentBuffer = ""
        bufferTimer?.invalidate()
        bufferTimer = nil

        completion(content)
    }

    func reset() {
        contentBuffer = ""
        bufferTimer?.invalidate()
        bufferTimer = nil
    }

    func forceFlush(completion: @escaping (String) -> Void) {
        flushBuffer(completion: completion)
    }
}
    
/// 消息数据管理器 - 负责消息的高效存储和检索（性能优化版本）
private class MessageDataManager {
    private var messageIndexCache: [UUID: Int] = [:]
    private var lastCacheSize = 0

    /// 增量更新索引缓存
    func updateIndexCache(for messages: [ChatMessageModel]) {
        let currentSize = messages.count

        if currentSize == lastCacheSize, !messageIndexCache.isEmpty {
            return
        }

        if currentSize > lastCacheSize, lastCacheSize > 0 {
            for index in lastCacheSize..<currentSize {
                if index < messages.count {
                    messageIndexCache[messages[index].id] = index
                }
            }
        } else {
            messageIndexCache.removeAll(keepingCapacity: true)
            for (index, message) in messages.enumerated() {
                messageIndexCache[message.id] = index
            }
        }

        lastCacheSize = currentSize
    }

    /// 添加单个消息到缓存
    func addMessageToCache(message: ChatMessageModel, at index: Int) {
        messageIndexCache[message.id] = index
        lastCacheSize = max(lastCacheSize, index + 1)
    }

    /// 清空缓存
    func clearCache() {
        messageIndexCache.removeAll(keepingCapacity: true)
        lastCacheSize = 0
    }

    func findMessageIndex(by id: UUID, in messages: [ChatMessageModel]) -> Int? {
        if let cachedIndex = messageIndexCache[id],
           cachedIndex < messages.count,
           messages[cachedIndex].id == id
        {
            return cachedIndex
        }

        if let index = messages.firstIndex(where: { $0.id == id }) {
            messageIndexCache[id] = index
            return index
        }

        return nil
    }
}
