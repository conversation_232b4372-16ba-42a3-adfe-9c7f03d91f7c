//
//  ProblemFeedbackController.swift
//  GrofyAI
//
//  Created by kissy on 2025/7/22.
//

import Combine
import Foundation
import SwiftUI


enum ImageState {
    case local(UIImage)      // 刚选中，尚未上传
    case uploading(UIImage)  // 上传中，显示加载动画
    case uploaded(url: String, preview: UIImage) // 上传成功，使用Kingfisher加载，同时保留本地图用于预览
    case failed(UIImage)     // 上传失败，显示错误图标
    
    // 辅助属性，获取用于显示的底层UIImage
    var image: UIImage? {
        switch self {
        case .local(let img), .uploading(let img), .failed(let img):
            return img
        case .uploaded(_, let preview):
            return preview
        }
    }
    
    // 辅助属性，检查是否正在上传
    var isUploading: Bool {
        if case .uploading = self { return true }
        return false
    }
}

struct DisplayImage: Identifiable {
    let id: UUID = UUID() // 每张图片的唯一ID
    var state: ImageState
}

@MainActor
class ProblemFeedbackController: ObservableObject {
    
    // MARK: - Published 状态
    // 用于存储用户输入的联系邮箱
    @Published var contactEmail: String = ""
    // 用于存储用户输入的问题反馈内容
    @Published var feedbackText: String = ""
    // 用于存储用户上传后的图片地址
    @Published var displayImages: [DisplayImage] = []
    
    @Published var isSubmitting: Bool = false
    // 错误处理
    @Published var showError: Bool = false
    @Published var errorMessage: String? {
        didSet { showError = errorMessage != nil }
    }
    
    
    let fileService: FileService
    let problemFeedbackService: ProblemFeedbackService
    
    let maxImageCount = 5
    
    // MARK: - 初始化
    init(
        fileService: FileService = FileService(),
        problemFeedbackService: ProblemFeedbackService = ProblemFeedbackService()
    ) {
        self.fileService = fileService
        self.problemFeedbackService = problemFeedbackService
    }
    
    var isUploading: Bool {
        displayImages.contains { $0.state.isUploading }
    }
    
    var isDisabled: Bool {
        feedbackText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty || isUploading || isSubmitting
    }
    
    var canAddMoreImages: Bool {
        displayImages.count < maxImageCount
    }
    
    
    /// 当用户从相册选择图片后调用此方法
    func handleSelected(images: [UIImage]) {
        guard !images.isEmpty else { return }
        
        var newImagesToUpload: [(id: UUID, image: UIImage)] = []
        
        for image in images {
            // 为每张新图创建 'local' 状态的 DisplayImage 并添加到UI
            let displayImage = DisplayImage(state: .local(image))
            self.displayImages.append(displayImage)
            
            // 添加到临时列表，准备上传
            newImagesToUpload.append((id: displayImage.id, image: image))
        }
        
        uploadImages(imagesToUpload: newImagesToUpload)
    }
    // MARK: - 数据获取
    
    private func updateImageState(id: UUID, newState: ImageState) {
        if let index = displayImages.firstIndex(where: { $0.id == id }) {
            displayImages[index].state = newState
        }
    }
    /// 上传图片
    private func uploadImages(imagesToUpload: [(id: UUID, image: UIImage)]) {
        guard !imagesToUpload.isEmpty else { return }
        
        // 1. 在UI上，立即将所有图片标记为 'uploading'
        for (id, image) in imagesToUpload {
            updateImageState(id: id, newState: .uploading(image))
        }
        
        Task {
            do {
                // --- 图片并行处理阶段 ---
                // processedImages现在存储处理后的 UIImage 和原始 ID
                var processedImages = [(image: UIImage, id: UUID)]()
                
                print("🚀 开始并行处理 \(imagesToUpload.count) 张图片...")
                
                // 使用 TaskGroup 并行处理所有图片
                try await withThrowingTaskGroup(of: (image: UIImage, id: UUID)?.self) { group in
                    for (id, image) in imagesToUpload {
                        group.addTask {
                            do {
                                // 调用未修改的 compressionUIImage 函数
                                // 我们忽略它返回的 fileName，因为我们用 UUID 作为唯一标识
                                let processedResult = try compressionUIImage(originalImage: image, name: id.uuidString)
                                return (image: processedResult.image, id: id)
                            } catch {
                                print("❌ 图片处理失败: \(id), Error: \(error)")
                                // 在主线程更新失败状态
                                await MainActor.run {
                                    self.updateImageState(id: id, newState: .failed(image))
                                }
                                return nil
                            }
                        }
                    }
                    
                    // 收集处理结果
                    for try await result in group {
                        if let processed = result {
                            processedImages.append(processed)
                        }
                    }
                }
                
                // 如果没有图片处理成功，就不用继续上传了
                guard !processedImages.isEmpty else {
                    print("⚠️ 没有可上传的图片，流程终止。")
                    return
                }
                
                // 准备要发送给服务的数据。在这里从处理后的 UIImage 获取 Data
                // 注意：这里需要确定上传的格式，.png 或 .jpeg
                let imagesForService: [(imageInfo: UIImage, name: String)] = processedImages.map { processed in
                    return (imageInfo: processed.image, name: processed.id.uuidString)
                }
                
                // 再次检查，因为 .pngData() 可能失败
                guard !imagesForService.isEmpty else {
                    print("⚠️ 没有可上传的图片数据，流程终止。")
                    return
                }
                
                // 调用服务上传
                let uploadedResults = try await fileService.uploadImages(
                    images: imagesForService,
                    threadId: "0",
                    format: .jpeg(compressionQuality: 0.8) // 确保与上面的 .pngData() 一致
                )
                                
                // --- 处理上传结果阶段 ---
                var successfulUploadIDs = Set<UUID>()
                for result in uploadedResults {
                    let imageName = result.originalName.replacingOccurrences(of: ".png", with: "")
                    guard let id = UUID(uuidString: imageName) else { continue }
                    
                    // 找到原始的UIImage用作预览图
                    if let originalImage = imagesToUpload.first(where: { $0.id == id })?.image {
                        await MainActor.run {
                            updateImageState(id: id, newState: .uploaded(url: result.url, preview: originalImage))
                        }
                    }
                    successfulUploadIDs.insert(id)
                }
                
                // 将任何上传失败（即不在成功列表里）的图片标记为 'failed'
                // 我们需要从 `processedImages` 列表来判断，因为这是实际尝试上传的列表
                for (processedImage, id) in processedImages {
                    if !successfulUploadIDs.contains(id) {
                        await MainActor.run {
                            // 使用处理过的图片作为失败状态的预览图，或者用原始图也可以
                            updateImageState(id: id, newState: .failed(processedImage))
                        }
                    }
                }
                
            } catch {
                // 这个 catch 块捕获 TaskGroup 或 fileService.uploadImages 的严重错误
                print("❌ 上传流程发生严重错误: \(error)")
                for (id, image) in imagesToUpload {
                    if case .uploading = displayImages.first(where: { $0.id == id })?.state {
                        await MainActor.run {
                            updateImageState(id: id, newState: .failed(image))
                        }
                    }
                }
                await MainActor.run {
                    handle(error: error)
                }
            }
        }
    }
    
    /// 移除已上传的图片
    func removeImage(id: UUID) {
        displayImages.removeAll(where: { $0.id == id })
        // 在实际应用中，如果图片已上传，你可能还需要调用API从服务器上删除文件
    }
    
    /// 提交最终的反馈信息
    func onSubmit(completion: @escaping () -> Void) {
        guard !isSubmitting else { return }
        self.errorMessage = nil
        // 检查是否有图片正在上传
        guard !isUploading else {
            errorMessage = "请等待图片上传完成"
            return
        }

        // 【UX 改进】检查是否有上传失败的图片
        let hasFailedUploads = displayImages.contains { if case .failed = $0.state { return true } else { return false } }
        if hasFailedUploads {
            errorMessage = "有图片上传失败，请移除后重试"
            return
        }

        // 1. 设置提交状态
        isSubmitting = true
        
        // 提取所有成功上传的图片URL
        let finalImageUrls = displayImages.compactMap { displayImage -> String? in
            if case .uploaded(let url, _) = displayImage.state {
                return url
            }
            return nil
        }
        
        Task {
            // 2. 使用 defer 确保 isSubmitting 总能被重置，无论成功还是失败
            // defer 块中的代码会在 Task 作用域结束前执行
            defer {
                Task { @MainActor in
                    self.isSubmitting = false
                }
            }
            
            do {
                // 3. 将网络请求放在 do-catch 块中
                let req = ProblemFeedbackReq(
                    email: self.contactEmail,
                    content: self.feedbackText,
                    links: finalImageUrls
                )
                
                let res =  try await problemFeedbackService.problemFeedback(req: req)
                
                if res.code != 200 {
                    throw BusinessError(code: res.code, message: res.msg)
                }
                
                // 4. 成功后，回到主线程执行完成回调
                await MainActor.run {
                    completion()
                }
                
            } catch {
                // 5. 捕获任何错误，并在主线程上更新UI
                await MainActor.run {
                    self.handle(error: error) // 使用你已有的错误处理函数
                }
            }
        }
    }
    
    
    private func handle(error: Error) {
        if let networkError = error as? NetworkError {
            errorMessage = networkError.localizedDescription
        } else {
            errorMessage = error.localizedDescription
        }
    }
    
}
