import Foundation
import SwiftUI

// MARK: - 聊天设置管理器

@MainActor
final class ChatSettingsManager: ObservableObject {
    static let shared = ChatSettingsManager()

    @AppStorage("chat_enable_thinking") var enableThinking = false {
        didSet { objectWillChange.send() }
    }

    @AppStorage("chat_enable_networking") var enableNetworking = false {
        didSet { objectWillChange.send() }
    }

    var isThinkingEnabled: Bool {
        !enableNetworking
    }

    var isNetworkingEnabled: Bool {
        !enableThinking
    }

    func toggleThinking() {
        enableThinking.toggle()

        if enableThinking, enableNetworking {
            enableNetworking = false
        }
    }

    func toggleNetworking() {
        enableNetworking.toggle()

        if enableNetworking, enableThinking {
            enableThinking = false
        }
    }
}
