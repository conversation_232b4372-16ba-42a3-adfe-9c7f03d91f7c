import Alamofire
import Combine
import Foundation

// MARK: - 刷新错误类型

enum RefreshError: LocalizedError {
    case alreadyLoading
    case networkUnavailable

    var errorDescription: String? {
        switch self {
        case .alreadyLoading:
            return "正在加载中，请稍候"
        case .networkUnavailable:
            return "网络不可用，请检查网络连接"
        }
    }
}

// MARK: - 历史记录控制器

@MainActor
class HistoryController: ObservableObject {
    static let shared = HistoryController()

    @Published var historyGroups: [(date: String, items: [HistoryItem])] = []
    @Published var isLoading = false
    @Published var isLoadingMore = false
    @Published var hasMoreData = true

    // MARK: - 聊天模式筛选

    @Published var filterChatMode: ChatMode? = .agent

    // MARK: - 选择模式相关

    @Published var isSelectionMode = false
    @Published var selectedItems: Set<Int> = []

    // MARK: - 增强刷新控制器

    @Published var refreshController = EnhancedRefreshController()

    // MARK: - 删除状态管理

    @Published var isDeletingHistory = false
    @Published var isDeletingBatch = false

    /// 是否有任何删除操作正在进行
    var isAnyDeleteInProgress: Bool {
        isDeletingHistory || isDeletingBatch
    }

    // MARK: - 分页相关

    private var currentPage = 1
    private let initialPageSize = 20 // 初始加载条数
    private let loadMorePageSize = 10 // 滚动加载条数
    private var totalCount = 0
    private var currentLoadType: RefreshType = .initial

    // Task管理
    private var currentRefreshTask: Task<Void, Never>?
    private var currentLoadMoreTask: Task<Void, Never>?
    private var lastRefreshTime: Date = .distantPast

    /// 获取当前页面大小
    private var currentPageSize: Int {
        return currentPage == 1 ? initialPageSize : loadMorePageSize
    }

    /// 进入聊天的回调闭包
    /// 注意：使用时请确保使用 [weak self] 避免循环引用
    /// 示例：controller.onEnterChat = { [weak self] threadId in ... }
    var onEnterChat: ((String) -> Void)?

    private let chatService = ChatService()
    private let authStore = AuthStore.shared
    private var cancellables = Set<AnyCancellable>()

    deinit {
        // 取消所有Task
        currentRefreshTask?.cancel()
        currentLoadMoreTask?.cancel()

        // 清空Task引用
        currentRefreshTask = nil
        currentLoadMoreTask = nil

        // 取消所有Combine订阅
        cancellables.removeAll()

        // 清空回调闭包
        onEnterChat = nil
    }

    // MARK: - 公共方法

    /// 设置聊天模式筛选
    func setFilterChatMode(_ chatMode: ChatMode?) {
        guard filterChatMode != chatMode else { return }

        filterChatMode = chatMode

        Task {
            await loadHistory()
        }
    }

    /// 加载历史记录（每次都重新加载）
    func loadHistoryIfNeeded() async {
        await loadHistory()
    }

    /// 加载历史记录（首次加载）
    func loadHistory() async {
        guard !isLoading else { return }

        guard authStore.getAccessToken() != nil else {
            await MainActor.run {
                self.isLoading = false
            }
            return
        }

        currentPage = 1
        isLoading = true
        hasMoreData = true
        currentLoadType = .initial

        await performLoadHistory()
    }

    /// 加载更多历史记录
    func loadMoreHistory() {
        guard !isLoadingMore, !isLoading, hasMoreData else { return }

        currentLoadMoreTask?.cancel()

        isLoadingMore = true
        currentLoadType = .loadMore

        currentLoadMoreTask = Task { [weak self] in
            await self?.performLoadHistory()
        }
    }

    /// 刷新历史记录
    func refreshHistory() async {
        refreshController.performRefresh { [weak self] in
            try await self?.performRefreshOperation()
        }
    }

    /// 执行实际的刷新操作
    func performRefreshOperation() async throws {
        // 防抖机制：避免频繁刷新
        let now = Date()
        if now.timeIntervalSince(lastRefreshTime) < 0.5 {
            return
        }

        guard !isLoading else {
            throw RefreshError.alreadyLoading
        }

        // 取消之前的刷新任务
        currentRefreshTask?.cancel()

        // 重置分页参数
        currentPage = 1
        hasMoreData = true
        currentLoadType = .refresh
        lastRefreshTime = now

        isLoading = true

        // 执行刷新操作
        do {
            let response = try await chatService.getHistoryPage(
                current: currentPage,
                size: currentPageSize,
                graphId: filterChatMode?.graphId
            )

            await MainActor.run { [weak self] in
                guard let self else { return }

                let newGroups = response.groupedItems

                historyGroups = newGroups

                let returnedItemCount = newGroups.flatMap(\.items).count
                hasMoreData = returnedItemCount >= currentPageSize

                if returnedItemCount > 0 {
                    currentPage = 2
                } else {
                    currentPage = 1
                }

                isLoading = false
            }

        } catch {
            await MainActor.run { [weak self] in
                self?.isLoading = false
            }
            throw error
        }
    }

    /// 切换选择模式
    func toggleSelectionMode() {
        isSelectionMode.toggle()
        if !isSelectionMode {
            selectedItems.removeAll()
        }
    }

    /// 选择/取消选择项目
    func toggleSelection(for item: HistoryItem) {
        if selectedItems.contains(item.id) {
            selectedItems.remove(item.id)
        } else {
            selectedItems.insert(item.id)
        }
    }

    /// 全选/取消全选
    func toggleSelectAll() {
        let allItemIds = Set(historyGroups.flatMap { $0.items.map(\.id) })

        if selectedItems.count == allItemIds.count {
            selectedItems.removeAll()
        } else {
            selectedItems = allItemIds
        }
    }

    /// 删除单个历史记录
    func deleteHistory(item: HistoryItem) {
        isDeletingHistory = true

        Task { [weak self] in
            guard let self else { return }

            do {
                try await chatService.deleteHistories(threadIds: [item.threadId])

                await MainActor.run { [weak self] in
                    guard let self else { return }
                    removeItemFromLocal(id: item.id)
                    isDeletingHistory = false
                    ToastManager.shared.showSuccess("删除成功")
                }

            } catch {
                await MainActor.run { [weak self] in
                    self?.isDeletingHistory = false
                }
                await handleError(error)
            }
        }
    }

    /// 批量删除选中的历史记录
    func deleteSelectedHistories() {
        let selectedIds = Array(selectedItems)
        guard !selectedIds.isEmpty else { return }

        let selectedThreadIds = historyGroups
            .flatMap(\.items)
            .filter { selectedIds.contains($0.id) }
            .map(\.threadId)

        isDeletingBatch = true

        Task { [weak self] in
            guard let self else { return }

            do {
                try await chatService.deleteHistories(threadIds: selectedThreadIds)

                await MainActor.run { [weak self] in
                    guard let self else { return }

                    for id in selectedIds {
                        removeItemFromLocal(id: id)
                    }

                    isDeletingBatch = false
                    isSelectionMode = false
                    selectedItems.removeAll()

                    ToastManager.shared.showSuccess("已删除 \(selectedIds.count) 条记录")
                }

            } catch {
                await MainActor.run { [weak self] in
                    self?.isDeletingBatch = false
                    // 保持选择状态，允许用户重试
                }
                await handleError(error)
            }
        }
    }

    func enterChat(item: HistoryItem) {
        onEnterChat?(item.threadId)
    }

    func getKnowledgeId(for threadId: String) -> Int? {
        let allItems = historyGroups.flatMap(\.items)
        return allItems.first { $0.threadId == threadId }?.knowledgeId
    }

    /// 执行历史记录加载
    private func performLoadHistory() async {
        // 检查任务是否被取消
        if Task.isCancelled {
            await MainActor.run { [weak self] in
                self?.isLoading = false
                self?.isLoadingMore = false
            }
            return
        }

        do {
            let response = try await chatService.getHistoryPage(
                current: currentPage,
                size: currentPageSize,
                graphId: filterChatMode?.graphId
            )

            // 再次检查任务是否被取消
            if Task.isCancelled {
                await MainActor.run { [weak self] in
                    self?.isLoading = false
                    self?.isLoadingMore = false
                }
                return
            }

            await MainActor.run { [weak self] in
                guard let self else { return }

                let newGroups = response.groupedItems

                if currentLoadType == .loadMore {
                    mergeHistoryGroups(newGroups)
                } else {
                    historyGroups = newGroups
                }

                let returnedItemCount = newGroups.flatMap(\.items).count
                hasMoreData = returnedItemCount >= currentPageSize

                if currentLoadType == .loadMore, returnedItemCount > 0 {
                    currentPage += 1
                }

                isLoading = false
                isLoadingMore = false
            }

        } catch {
            await handleError(error)
        }
    }

    func clearCache() {
        historyGroups.removeAll()
    }

    func resetViewState() {
        isSelectionMode = false
        selectedItems.removeAll()
        isDeletingHistory = false
        isDeletingBatch = false
    }

    // MARK: - 私有方法

    /// 合并历史记录分组数据
    private func mergeHistoryGroups(_ newGroups: [(date: String, items: [HistoryItem])]) {
        var mergedGroups = historyGroups

        for newGroup in newGroups {
            if let existingIndex = mergedGroups.firstIndex(where: { $0.date == newGroup.date }) {
                // 该日期已存在，合并项目
                let existingItems = mergedGroups[existingIndex].items
                let mergedItems = (existingItems + newGroup.items)
                    .uniqued(by: { $0.id })
                    .sorted { $0.id > $1.id }

                mergedGroups[existingIndex] = (date: newGroup.date, items: mergedItems)
            } else {
                // 新日期，直接添加
                mergedGroups.append(newGroup)
            }
        }

        // 重新排序分组（按日期倒序）
        mergedGroups.sort { $0.date > $1.date }

        historyGroups = mergedGroups
    }

    /// 从本地数据中移除指定项目
    private func removeItemFromLocal(id: Int) {
        for (groupIndex, group) in historyGroups.enumerated() {
            if let itemIndex = group.items.firstIndex(where: { $0.id == id }) {
                var updatedItems = group.items
                updatedItems.remove(at: itemIndex)

                if updatedItems.isEmpty {
                    // 如果该日期下没有项目了，移除整个分组
                    historyGroups.remove(at: groupIndex)
                } else {
                    // 更新分组项目
                    historyGroups[groupIndex] = (date: group.date, items: updatedItems)
                }
                break
            }
        }

        selectedItems.remove(id)
    }

    /// 处理错误
    private func handleError(_ error: Error) async {
        await MainActor.run { [weak self] in
            guard let self else { return }

            isLoading = false
            isLoadingMore = false

            // 更精确地检查取消错误
            if isCancellationError(error) {
                return
            }

            let errorMessage: String = if let businessError = error as? BusinessError {
                businessError.message
            } else if let networkError = error as? NetworkError {
                networkError.localizedDescription
            } else {
                error.localizedDescription
            }

            ToastManager.shared.showError(errorMessage)
        }
    }

    /// 检查是否为取消错误
    private func isCancellationError(_ error: Error) -> Bool {
        // 检查Task取消
        if Task.isCancelled {
            return true
        }

        // 检查URLError取消
        if let urlError = error as? URLError, urlError.code == .cancelled {
            return true
        }

        // 检查AFError取消
        if let afError = error as? AFError {
            switch afError {
            case .requestAdaptationFailed(let error):
                return isCancellationError(error)
            case .requestRetryFailed(let retryError, _):
                return isCancellationError(retryError)
            case .sessionTaskFailed(let error):
                return isCancellationError(error)
            default:
                break
            }
        }

        // 检查错误描述中的取消关键词
        let errorDescription = error.localizedDescription.lowercased()
        return errorDescription.contains("cancelled") ||
            errorDescription.contains("canceled") ||
            errorDescription.contains("operation was cancelled")
    }
}
