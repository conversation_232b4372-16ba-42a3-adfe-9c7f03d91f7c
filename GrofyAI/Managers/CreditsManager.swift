//
//  C.swift
//  GrofyAI
//
//  Created by kissy on 2025/7/2.
//
import Foundation

final class CreditsManager {
    // 用于在 UserDefaults 中存储数据的键
    private let storageKey = "costCreationItems"
    
    /// 解析、转换并存储数据到 UserDefaults
    /// - Parameter jsonData: 从 API 接口获取的原始 JSON Data
    func saveItems( items: [CreditsItem]) {
        do {
            
            // 1将 [DataItem] 数组转换为 [String: DataItem] 字典
            // key 是 unionId, value 是 DataItem 对象本身
            let itemsDictionary = Dictionary(uniqueKeysWithValues: items.map { ($0.unionId, $0) })
            
            // 2. 将字典编码成 Data 以便存储
            let encoder = JSONEncoder()
            let dataToStore = try encoder.encode(itemsDictionary)
            
            // 3. 存储到 UserDefaults
            UserDefaults.standard.set(dataToStore, forKey: storageKey)
            
            print("✅ 数据成功保存到 UserDefaults！共 \(itemsDictionary.count) 条。")
            
        } catch {
            print("❌ 数据处理或存储失败: \(error)")
        }
    }
    
    
    /// 通过 unionId 从 UserDefaults 中查找并返回一个 CreditsItem
    /// - Parameter unionId: 要查找的项目的唯一 ID
    /// - Returns: 如果找到则返回 CreditsItem 对象，否则返回 nil
    func getItem(byUnionId unionId: String) -> CreditsItem? {
        // 1. 从 UserDefaults 中根据 key 读取存储的 Data
        guard let savedData = UserDefaults.standard.data(forKey: storageKey) else {
            print("⚠️ 在 UserDefaults 中未找到 key '\(storageKey)' 对应的数据。")
            return nil
        }
        
        do {
            // 2. 将 Data 解码回 [String: CreditsItem] 字典
            let decoder = JSONDecoder()
            let itemsDictionary = try decoder.decode([String: CreditsItem].self, from: savedData)
            
            // 3. 通过 unionId (作为字典的 key) 查找对应的项并返回
            //    字典的下标查询本身就会返回一个可选值，如果key不存在，结果就是 nil
            let item = itemsDictionary[unionId]
            
            if item == nil {
                print("ℹ️ 在已存储的数据中未找到 unionId 为 '\(unionId)' 的项。")
            }
            return item
            
        } catch {
            print("❌ 从 UserDefaults 解码数据失败: \(error)")
            return nil
        }
    }
}

