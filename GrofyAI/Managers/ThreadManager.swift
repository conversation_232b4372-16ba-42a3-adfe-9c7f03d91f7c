import Foundation
import SwiftUI

// MARK: - 会话线程管理器

/// 统一管理聊天会话的threadId和状态
/// 负责threadId的生成、持久化存储和会话状态管理
@MainActor
final class ThreadManager: ObservableObject {
    static let shared = ThreadManager()

    @Published private(set) var currentThreadId: String?
    @Published private(set) var isFirstMessage = true

    private let userDefaults = UserDefaults.standard

    private enum Keys {
        static let currentThreadId = "ThreadManager.currentThreadId"
        static let isFirstMessage = "ThreadManager.isFirstMessage"
    }

    // MARK: - 公共方法

    /// 开始新会话
    func startNewSession() {
        let newThreadId = generateThreadId()
        currentThreadId = newThreadId
        isFirstMessage = true

        print("ThreadManager: 开启新会话 - threadId: \(newThreadId)")
    }

    /// 继续当前会话
    func continueCurrentSession() {
        guard currentThreadId != nil else {
            startNewSession()
            return
        }

        isFirstMessage = false
    }

    /// 获取当前threadId
    /// 如果没有当前会话，自动创建新会话
    /// - Returns: 当前会话的threadId
    func getCurrentThreadId() -> String {
        if let threadId = currentThreadId {
            return threadId
        } else {
            startNewSession()
            return currentThreadId!
        }
    }

    /// 获取是否为首次消息
    /// - Returns: 是否为当前会话的首次消息
    func getIsFirstMessage() -> Bool {
        return isFirstMessage
    }

    /// 设置当前会话（用于历史对话）
    /// - Parameters:
    ///   - threadId: 要设置的线程ID
    ///   - isFirst: 是否为首次消息（历史对话通常为false）
    func setCurrentThread(threadId: String, isFirst: Bool = false) {
        currentThreadId = threadId
        isFirstMessage = isFirst

        print("ThreadManager: 设置threadId: \(threadId)")
    }

    /// 清除当前会话
    func clearCurrentSession() {
        guard currentThreadId != nil else {
            return
        }

        currentThreadId = nil
        isFirstMessage = true

        print("ThreadManager: 无 ThreadId")
    }

    /// 检查是否有活跃会话
    /// - Returns: 是否存在当前活跃的会话
    func hasActiveSession() -> Bool {
        return currentThreadId != nil
    }

    // MARK: - 私有方法

    /// 生成新的threadId
    /// 格式：thd-uuid
    /// - Returns: 新生成的threadId
    private func generateThreadId() -> String {
        let uuid = UUID().uuidString.lowercased()
        return "thd-\(uuid)"
    }
}
