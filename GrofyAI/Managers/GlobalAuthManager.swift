import Combine
import FlowStacks
import SwiftUI

struct AuthenticationRequiredModifier: ViewModifier {
    @EnvironmentObject private var authController: AuthenticationController
    @EnvironmentObject private var authStore: AuthStore
    @EnvironmentObject private var globalAuthManager: GlobalAuthManager

    let showLoadingPlaceholder: Bool
    let placeholderText: String
    let showNavigation: Bool
    let onBack: (() -> Void)?

    init(
        showLoadingPlaceholder: Bool = false,
        placeholderText: String = "请先登录以访问此功能",
        showNavigation: Bool = false,
        onBack: (() -> Void)? = nil
    ) {
        self.showLoadingPlaceholder = showLoadingPlaceholder
        self.placeholderText = placeholderText
        self.showNavigation = showNavigation
        self.onBack = onBack
    }

    func body(content: Content) -> some View {
        Group {
            if AuthStore.shared.hasValidToken() {
                content
            } else {
                if showLoadingPlaceholder {
                    authenticationPlaceholder
                } else {
                    content
                        .onAppear {
                            globalAuthManager.requestAuthentication()
                        }
                }
            }
        }
    }

    @ViewBuilder
    private var authenticationPlaceholder: some View {
        VStack(spacing: 0) {
            EmptyStateView(
                description: placeholderText,
                actionButton: .init(title: "立即登录", action: {
                    globalAuthManager.requestAuthentication()
                }),
                style: .fullScreen
            )
        }
        .background(DesignSystem.Colors.backgroundPage)
        .navigationBarBackButtonHidden(showNavigation)
        .navigationBarTitleDisplayMode(.inline)
        .toolbar(showNavigation ? .visible : .hidden, for: .navigationBar)
        .toolbar {
            if showNavigation {
                ToolbarItem(placement: .topBarLeading) {
                    BackButton(onBack: handleBackAction)
                }
                ToolbarItem(placement: .principal) {
                    Text("历史记录")
                        .font(.system(size: DesignSystem.FontSize.lg, weight: .medium))
                        .foregroundColor(DesignSystem.Colors.textPrimary)
                }
            }
        }
    }

    private func handleBackAction() {
        onBack?()
    }
}

/// 全局认证管理器
class GlobalAuthManager: ObservableObject {
    @Published var shouldShowAuthentication = false

    private let authStore = AuthStore.shared

    init() {
        setupNotificationObserver()
    }

    private func setupNotificationObserver() {
        NotificationCenter.default.addObserver(
            forName: NSNotification.Name("AuthenticationSuccess"),
            object: nil,
            queue: .main
        ) { [weak self] _ in
            self?.dismissAuthentication()
        }

        // 监听认证失效通知
        NotificationCenter.default.addObserver(
            forName: .authenticationRequired,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            self?.requestAuthentication()
        }
    }

    func dismissAuthentication() {
        shouldShowAuthentication = false
    }

    /// 请求显示认证界面
    func requestAuthentication() {
        if !AuthStore.shared.hasValidToken() {
            shouldShowAuthentication = true
        }
    }

    deinit {
        NotificationCenter.default.removeObserver(self)
    }
}

extension View {
    /// 未登录时自动显示登录界面
    func requireAuthentication(
        showPlaceholder: Bool = false,
        placeholderText: String = "请先登录以访问此功能",
        showNavigation: Bool = false,
        onBack: (() -> Void)? = nil
    ) -> some View {
        modifier(
            AuthenticationRequiredModifier(
                showLoadingPlaceholder: showPlaceholder,
                placeholderText: placeholderText,
                showNavigation: showNavigation,
                onBack: onBack
            )
        )
    }

    /// 未登录时显示占位符界面（主Tab页面使用，无导航）
    func requireAuthenticationWithPlaceholder(
        _ placeholderText: String = "请先登录以访问此功能"
    ) -> some View {
        requireAuthentication(
            showPlaceholder: true,
            placeholderText: placeholderText,
            showNavigation: false
        )
    }

    /// 未登录时显示占位符界面（子页面使用，带导航）
    func requireAuthenticationWithNavigation(
        _ placeholderText: String = "请先登录以访问此功能",
        onBack: @escaping () -> Void
    ) -> some View {
        requireAuthentication(
            showPlaceholder: true,
            placeholderText: placeholderText,
            showNavigation: true,
            onBack: onBack
        )
    }
}
