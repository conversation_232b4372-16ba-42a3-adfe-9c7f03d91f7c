import Foundation
import KeychainAccess
import UIKit

// MARK: - 应用配置管理

enum AppConfig {
    // MARK: - 环境配置

    /// 应用运行环境
    enum Environment {
        case development
        case production

        /// 当前运行环境
        /// 优先使用编译标志，如果不可用则通过其他方式检测开发环境
        static let current: Environment = {
            // 方法1: 检查编译标志
            #if DEBUG
            return .development
            #else
            // 方法2: 检查是否在模拟器中运行
            #if targetEnvironment(simulator)
            return .development
            #else
            // 方法3: 检查Bundle标识符是否包含开发标识
            if let bundleId = Bundle.main.bundleIdentifier,
               bundleId.contains(".debug") || bundleId.contains(".dev")
            {
                return .development
            }
            // 方法4: 检查是否连接了调试器
            if isDebuggerAttached() {
                return .development
            }
            // 默认为生产环境
            return .production
            #endif
            #endif
        }()

        /// 检查是否连接了调试器
        private static func isDebuggerAttached() -> Bool {
            var info = kinfo_proc()
            var mib: [Int32] = [CTL_KERN, KERN_PROC, KERN_PROC_PID, getpid()]
            var size = MemoryLayout<kinfo_proc>.stride
            let junk = sysctl(&mib, UInt32(mib.count), &info, &size, nil, 0)
            assert(junk == 0, "sysctl failed")
            return (info.kp_proc.p_flag & P_TRACED) != 0
        }

        /// 环境描述
        var description: String {
            switch self {
            case .development:
                return "Development"
            case .production:
                return "Production"
            }
        }

        /// 是否为开发环境
        var isDevelopment: Bool {
            return self == .development
        }

        /// 是否为生产环境
        var isProduction: Bool {
            return self == .production
        }
    }

    // MARK: - API配置

    /// API相关配置
    enum API {
        /// 开发环境API地址
        private static let developmentBaseURL = "http://apitest.chatbotsai.org"
//          private static let developmentBaseURL = "https://apirouter.ielee.com"

        /// 生产环境API地址
        private static let productionBaseURL = "http://apitest.chatbotsai.org"

        /// 备用API地址（用于注释记录）
        // private static let alternativeBaseURL = "https://apirouter.ielee.com"
        // private static let localBaseURL = "http://192.168.0.80:9090"

        /// 当前环境的API基础地址
        static var baseURL: String {
            switch Environment.current {
            case .development:
                return developmentBaseURL
            case .production:
                return productionBaseURL
            }
        }

        /// 请求超时时间（秒）
        static let timeoutInterval: TimeInterval = 30

        /// 最大重试次数
        static let maxRetryCount = 3
    }

    // MARK: - 应用配置

    /// 应用基本信息配置
    enum App {
        /// 应用包名
        static let packageName = "com.chatbottest.testapp"

        /// 应用显示名称
        static let displayName = "MoonvyAI"

        /// 应用版本信息
        static let version = "0.0.0"

        /// 最低支持iOS版本
        static let minimumIOSVersion = "16.0"
    }

    // MARK: - 认证配置

    /// 用户认证相关配置
    enum Auth {
        /// 默认登录邮箱
        static let defaultEmail = "<EMAIL>"

        /// 默认登录密码
        static let defaultPassword = "12345678"

        /// 备用用户设备码
        // static let alternativeUserCode = "57C27A59-1BDC-48E5-918A-3B5841625422"

        /// Keychain服务标识符
        static let keychainService = "org.grofyai"

        /// 租户ID
        static let tenantId = 10000001
    }

    // MARK: - 功能配置

    /// 功能开关配置
    enum Features {
        /// 是否启用调试日志
        /// 基于当前环境动态决定，开发环境启用，生产环境禁用
        static var enableDebugLogging: Bool {
            return Environment.current.isDevelopment
        }

        /// 是否启用网络日志
        /// 基于当前环境动态决定，开发环境启用，生产环境禁用
        static var enableNetworkLogging: Bool {
            return Environment.current.isDevelopment
        }
    }

    // MARK: - 存储配置

    /// 本地存储相关配置
    enum Storage {
        /// UserDefaults键前缀
        static let userDefaultsPrefix = "MoonvyAI."

        /// 缓存最大大小（MB）
        static let maxCacheSizeMB = 100

        /// 缓存过期时间（小时）
        static let cacheExpirationHours = 24
    }

    // MARK: - Web 配置

    enum Web {
        /// 使用条款
        static let termsOfServiceURL = "https://api.app.moonvy.ai/service.html"

        /// 隐私政策
        static let privacyPolicyURL = "https://api.app.moonvy.ai/privacy.html"
    }

    enum Store {
        /// 应用商店中应用 ID
        static let AppId = "6459478672" // 测试使用的豆包，正式需要替换
        /// 应用商店中应用链接
        static let AppUrl = "https://apps.apple.com/cn/app/id6459478672" // 测试使用的豆包，正式需要替换
    }

    // MARK: - 内购产品配置

    enum InAppPurchase {
        /// StoreKit 产品标识符
        enum ProductIdentifier {
            // Pro 会员
            static let proMonthly = "pro_monthly_subscription"
            static let proYearly = "pro_year_subscription"

            // Plus 会员
            static let plusMonthly = "plus_monthly_subscription"
            static let plusYearly = "plus_year_subscription"

            // Ultra 会员
            static let ultraMonthly = "ultra_monthly_subscription"
            static let ultraYearly = "ultra_year_subscription"
        }
    }
}

extension AppConfig {
    static var debugInfo: String {
        return """
        ==================== MoonvyAI 配置信息 [\(DateUtils.formatTimeOnly())] ====================
        环境信息:
          当前环境: \(Environment.current.description)
          是否开发环境: \(Environment.current.isDevelopment)

        API配置:
          基础地址: \(API.baseURL)
          超时时间: \(API.timeoutInterval)秒
          最大重试次数: \(API.maxRetryCount)

        应用配置:
          包名: \(App.packageName)
          显示名称: \(App.displayName)
          版本: \(App.version)

        认证配置:
          默认登录邮箱: \(Auth.defaultEmail)
          Keychain存储服务标识符: \(Auth.keychainService)

        功能开关:
          调试日志: \(Features.enableDebugLogging)
          网络日志: \(Features.enableNetworkLogging)

        存储配置:
          UserDefaults前缀: \(Storage.userDefaultsPrefix)

        Web配置:
          使用条款URL: \(Web.termsOfServiceURL)
          隐私政策URL: \(Web.privacyPolicyURL)
        """
    }
}

extension AppConfig {
    static func debugKeychain() {
        #if DEBUG
        guard Environment.current.isDevelopment else { return }

        let keychain = Keychain(service: Auth.keychainService)
        print("==================== Keychain 调试信息 [\(DateUtils.formatTimeOnly())] ====================")
        print("服务标识符: \(Auth.keychainService)")

        // 基本认证信息
        print("\n【认证信息】")
        print("accessToken: \((try? keychain.get("accessToken")) ?? "nil")")
        print("memberLevel: \((try? keychain.get("memberLevel")) ?? "nil")")
        print("unionId: \((try? keychain.get("unionId")) ?? "nil")")
        print("expireDate: \((try? keychain.get("expireDate")) ?? "nil")")

        // 积分信息
        print("\n【积分信息】")
        print("credits: \((try? keychain.get("credits")) ?? "nil")")
        print("premiumCredits: \((try? keychain.get("premiumCredits")) ?? "nil")")
        print("giftedCredits: \((try? keychain.get("giftedCredits")) ?? "nil")")
        print("giftedPremiumCredits: \((try? keychain.get("giftedPremiumCredits")) ?? "nil")")

        #endif
    }

    /// 调试模拟器设备信息（仅在开发环境生效）
    static func debugSimulatorInfo() {
        guard Environment.current.isDevelopment else { return }

        print("==================== 设备调试信息 [\(DateUtils.formatTimeOnly())] ====================")

        if let udid = ProcessInfo.processInfo.environment["SIMULATOR_UDID"] {
            print("模拟器 UDID: \(udid)")
            print("Keychain路径: ~/Library/Developer/CoreSimulator/Devices/\(udid)/data/Library/Keychains/")
        } else {
            print("运行环境: 真实设备")
        }

        print("设备型号: \(UIDevice.current.model)")
        print("设备名称: \(UIDevice.current.name)")
        print("系统版本: \(UIDevice.current.systemVersion)")
        print("应用Bundle ID: \(Bundle.main.bundleIdentifier ?? "Unknown")")
    }

    /// 调试AuthStore状态（仅在开发环境生效）
    static func debugAuthStoreState() {
        #if DEBUG
        guard Environment.current.isDevelopment else { return }

        let authStore = AuthStore.shared
        print("==================== AuthStore 状态 [\(DateUtils.formatTimeOnly())] ====================")
        print("当前用户信息:")
        print("  accessToken: \(authStore.user.accessToken?.prefix(20) ?? "nil")...")
        print("  memberLevel: \(authStore.user.memberLevel ?? "nil")")
        print("  unionId: \(authStore.user.unionId ?? "nil")")
        print("  credits: \(authStore.user.credits ?? 0)")
        print("  premiumCredits: \(authStore.user.premiumCredits ?? 0)")
        print("  giftedCredits: \(authStore.user.giftedCredits ?? 0)")
        print("  giftedPremiumCredits: \(authStore.user.giftedPremiumCredits ?? 0)")

        print("\n认证状态:")
        print("  是否VIP: \(authStore.isVip())")

        #endif
    }
}
