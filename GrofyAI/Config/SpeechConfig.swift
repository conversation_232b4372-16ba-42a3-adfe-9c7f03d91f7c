import Foundation

// MARK: - Azure语音服务配置

enum AzureSpeechConfig {
    // Azure 服务区域
    static let serviceRegion = "eastus"

    // Azure 服务端点
    static let endpoint = "https://eastus.api.cognitive.microsoft.com/"

    // 默认语音识别语言
    static let defaultRecognitionLanguage = "zh-CN"

    // 默认语音合成语言
    static let defaultSynthesisLanguage = "zh-CN"

    // 默认语音合成语音
    static let defaultVoice = "zh-CN-XiaoxiaoMultilingualNeural"
}

// MARK: - 支持的语音选项

enum SpeechVoice: String, CaseIterable {
    // 中文语音
    case xiaoxiaoMultilingual = "zh-CN-XiaoxiaoMultilingualNeural"
    case xiaochenDragonHD = "zh-CN-Xiaochen:DragonHDLatestNeural"
    case xiaoyuMultilingual = "zh-CN-XiaoyuMultilingualNeural"
    case yunxiMultilingual = "zh-CN-YunxiMultilingualNeural"

    // 英语语音 (美国)
    case avaMultilingual = "en-US-AvaMultilingualNeural"
    case andrewMultilingual = "en-US-AndrewMultilingualNeural"
    case jennyMultilingual = "en-US-JennyMultilingualNeural"
    case ryanMultilingual = "en-US-RyanMultilingualNeural"

    // 英语语音 (英国)
    case adaMultilingual = "en-GB-AdaMultilingualNeural"
    case ollieMultilingual = "en-GB-OllieMultilingualNeural"

    // 其他语言
    case remyMultilingual = "fr-FR-RemyMultilingualNeural"

    var displayName: String {
        switch self {
        case .xiaoxiaoMultilingual:
            return "晓晓 - 女声 - 温柔"
        case .xiaochenDragonHD:
            return "晓辰 - 女声 - 清晰"
        case .xiaoyuMultilingual:
            return "晓雨 - 女声 - 活泼"
        case .yunxiMultilingual:
            return "云希 - 男声 - 磁性"
        case .avaMultilingual:
            return "Ava - 女声 - 专业"
        case .andrewMultilingual:
            return "Andrew - 男声 - 沉稳"
        case .jennyMultilingual:
            return "Jenny - 女声 - 友好"
        case .ryanMultilingual:
            return "Ryan - 男声 - 年轻"
        case .adaMultilingual:
            return "Ada - 女声 - 英式"
        case .ollieMultilingual:
            return "Ollie - 男声 - 英式"
        case .remyMultilingual:
            return "Remy - 男声 - 法式"
        }
    }

    var language: String {
        switch self {
        case .xiaochenDragonHD, .xiaoxiaoMultilingual, .xiaoyuMultilingual, .yunxiMultilingual:
            return "zh-CN"
        case .adaMultilingual, .ollieMultilingual:
            return "en-GB"
        case .andrewMultilingual, .avaMultilingual, .jennyMultilingual, .ryanMultilingual:
            return "en-US"
        case .remyMultilingual:
            return "fr-FR"
        }
    }
}

// MARK: - 语音识别语言选项

enum SpeechRecognitionLanguage: String, CaseIterable {
    case chineseSimplified = "zh-CN"
    case englishUS = "en-US"
    case englishGB = "en-GB"
    case french = "fr-FR"
    case japanese = "ja-JP"
    case korean = "ko-KR"
    case spanish = "es-ES"
    case german = "de-DE"

    var displayName: String {
        switch self {
        case .chineseSimplified:
            return "中文（简体）"
        case .englishUS:
            return "英语（美国）"
        case .englishGB:
            return "英语（英国）"
        case .french:
            return "法语"
        case .japanese:
            return "日语"
        case .korean:
            return "韩语"
        case .spanish:
            return "西班牙语"
        case .german:
            return "德语"
        }
    }
}
