import SwiftUI

// MARK: - 应用设计系统常量定义

/// MoonvyAI应用设计系统，包含颜色、圆角、间距、边框等视觉设计规范
/// 提供统一的设计语言和可维护的UI常量定义
enum DesignSystem {
    // MARK: - 颜色系统

    /// 应用颜色系统
    /// 基于语义化命名和功能分类的统一颜色规范
    /// 支持浅色/深色模式适配和品牌色彩体系
    enum Colors {
        // MARK: - 主题色系统

        /// 品牌主色调 (#637DFE)
        /// 适用场景：主要按钮、选中状态、品牌标识
        /// 支持深色模式自动适配
        static let primary = Color("ColorBrandPrimary")

        /// 品牌渐变色 (#8339FF)
        /// 适用场景：渐变背景、特殊效果、高级功能标识
        /// 支持深色模式自动适配
        static let gradient = Color("ColorBrandGradient")

        /// 品牌浅蓝色 (#E7E7FB)
        /// 适用场景：选中背景、浅色强调、辅助装饰
        /// 支持深色模式自动适配
        static let lightBlue = Color("ColorBrandLightBlue")

        // MARK: - 文本色系统

        /// 主要文本色 (#25262B)
        /// 适用场景：标题、重要文本、主要内容
        /// 支持深色模式自动适配
        static let textPrimary = Color("ColorTextPrimary")

        /// 次要文本色 (#636467)
        /// 适用场景：正文内容、描述文本、一般信息
        /// 支持深色模式自动适配
        static let textSecondary = Color("ColorTextSecondary")

        /// 三级文本色 (#AAAAAC)
        /// 适用场景：辅助信息、次要描述、不重要文本
        /// 支持深色模式自动适配
        static let textTertiary = Color("ColorTextTertiary")

        /// 提示文本色 (#D7D7D7)
        /// 适用场景：占位符文本、禁用状态、极弱提示
        /// 支持深色模式自动适配
        static let textHint = Color("ColorTextHint")

        /// 提示文本色 (#F0F0FF)
        /// 适用场景: 模型Icon 的背景色
        /// 支持深色模式自动适配
        static let iconBackground = Color("ColorIconBackground")

        /// 白色/黑色自适应颜色
        /// 适用场景：需要纯白色或纯黑色的场景
        /// 支持深色模式自动适配（浅色主题显示白色，深色主题显示黑色）
        static let whiteBlack = Color("ColorWhiteBlack")

        // MARK: - 背景色系统

        /// 页面背景色 (#F6F6F6)
        /// 适用场景：主页面背景、大面积背景区域
        /// 支持深色模式自动适配
        static let backgroundPage = Color("ColorBackgroundPage")

        /// 卡片背景色 (#FFFFFF)
        /// 适用场景：卡片组件、弹窗背景、内容容器
        /// 支持深色模式自动适配
        static let backgroundCard = Color("ColorBackgroundCard")

        /// 输入框背景色 (#F5F5F5)
        /// 适用场景：文本输入框、表单控件、可编辑区域
        /// 支持深色模式自动适配
        static let backgroundInput = Color("ColorBackgroundInput")

        // MARK: - 边框色系统

        /// 边框线色 (#EAEAEA)
        /// 适用场景：卡片边框、分隔线、组件边界
        /// 支持深色模式自动适配
        static let border = Color("ColorBorderDefault")

        // MARK: - 语义化别名（向后兼容）

        /// 主色调别名 - 指向统一的主色调
        /// 推荐使用 primary 替代此别名
        static let adaptivePrimary = primary

        /// 文本色别名 - 指向统一的主要文本色
        /// 推荐使用 textPrimary 替代此别名
        static let adaptiveText = textPrimary

        /// 标题文本色别名 - 指向统一的主要文本色
        /// 推荐使用 textPrimary 替代此别名
        static let adaptiveTitleText = textPrimary

        /// 次要文本色别名 - 指向统一的次要文本色
        /// 推荐使用 textSecondary 替代此别名
        static let adaptiveSecondText = textSecondary

        /// 提示文本色别名 - 指向统一的提示文本色
        /// 推荐使用 textHint 替代此别名
        static let adaptiveHintText = textHint

        /// 背景色别名 - 指向统一的页面背景色
        /// 推荐使用 backgroundPage 替代此别名
        static let adaptiveBackground = backgroundPage

        /// 选中主色调别名 - 指向统一的浅蓝色
        /// 推荐使用 lightBlue 替代此别名
        static let adaptiveSelectedPrimary = lightBlue

        // MARK: - 状态色系统

        /// 成功状态色
        /// 适用场景：成功提示、完成状态、正确反馈
        static let success = Color.green

        /// 警告状态色
        /// 适用场景：警告提示、注意事项、重要提醒
        static let warning = Color.orange

        /// 错误状态色
        /// 适用场景：错误提示、失败状态、危险操作
        static let error = Color.red

        /// 信息状态色
        /// 适用场景：信息提示、中性反馈、一般通知
        static let info = Color.blue

        // MARK: - 语义化颜色

        /// 分隔线颜色
        /// 统一的分隔线和边框颜色
        static let separator = border

        /// 占位符颜色
        /// 统一的占位符和提示文本颜色
        static let placeholder = textHint

        /// 禁用状态颜色
        /// 适用场景：禁用按钮、不可交互元素
        static let disabled = Color.gray.opacity(0.3)

        /// 遮罩背景色
        /// 适用场景：弹窗遮罩、模态背景
        static let overlay = Color.black.opacity(0.3)

        /// 高亮背景色
        /// 适用场景：选中项背景、悬停状态
        static let highlight = lightBlue

        /// 会员购买界面背景色 (#17141A)
        static let membershipPurchaseBackground = Color(hex: "#17141A")
    }

    // MARK: - 圆角系统

    /// 3级圆角设计系统
    /// 基于组件功能重要性和视觉层次建立的统一圆角规范
    enum Rounded {
        /// 紧凑型组件圆角 (8pt)
        /// 适用场景：网格布局中的小卡片、紧凑型功能入口
        /// 使用组件：FeatureCard等
        static let sm: CGFloat = 8

        /// 标准组件圆角 (12pt)
        /// 适用场景：常规卡片组件、列表项、标准容器
        /// 使用组件：RecentActivityList、MembershipPromoBanner、选择器组件等
        static let md: CGFloat = 12

        /// 重要交互组件圆角 (16pt)
        /// 适用场景：核心交互元素、弹窗、主要输入框
        /// 使用组件：ChatInputBar、LLM模型选择器弹窗等
        static let lg: CGFloat = 16
    }

    // MARK: - 间距系统

    /// 标准间距系统
    /// 提供一致的组件间距和内边距规范
    enum Spacing {
        /// 极小间距 (4pt) - 用于紧密相关的元素
        static let xs: CGFloat = 4
        /// 小间距 (8pt) - 用于相关元素之间
        static let sm: CGFloat = 8
        /// 中等间距 (12pt) - 用于标准组件内边距
        static let md: CGFloat = 12
        /// 大间距 (16pt) - 用于组件外边距和重要分隔
        static let lg: CGFloat = 16
        /// 超大间距 (20pt) - 用于页面级别的间距
        static let xl: CGFloat = 20
        /// 巨大间距 (24pt) - 用于主要区块分隔
        static let xxl: CGFloat = 24
    }

    // MARK: - 边框系统

    /// 边框宽度系统
    /// 提供统一的边框和分隔线宽度规范
    enum BorderWidth {
        /// 细边框 (0.5pt) - 用于卡片边框和细分隔线
        static let thin: CGFloat = 0.5
        /// 标准边框 (1.0pt) - 用于按钮边框和标准分隔线
        static let regular: CGFloat = 1.0
        /// 粗边框 (2.0pt) - 用于强调边框和重要分隔
        static let thick: CGFloat = 2.0
    }

    // MARK: - 字体系统

    /// 统一字体系统
    enum Typography {
        // MARK: - 系统字体（推荐使用，支持Dynamic Type）

        /// 大标题字体 - 对应系统.title (~28pt)
        /// 适用场景：品牌标题、应用名称、页面主标题
        /// 使用组件：ChatBrandSection品牌标题
        /// 优势：自动支持Dynamic Type，与系统UI一致
        static let titleLarge: Font = .title

        /// 中标题字体 - 对应系统.title2 (~22pt)
        /// 适用场景：页面标题、重要区块标题
        /// 使用组件：页面级标题、重要区块标题
        /// 优势：自动支持Dynamic Type
        static let titleMedium: Font = .title2

        /// 小标题字体 - 对应系统.title3 (~20pt)
        /// 适用场景：弹窗标题、导航图标、重要交互元素
        /// 使用组件：LLMModelSelectionPopup标题、ChatTabNavigationBar图标
        /// 优势：自动支持Dynamic Type
        static let titleSmall: Font = .title3

        /// 突出显示字体 - 对应系统.headline (~17pt粗体)
        /// 适用场景：需要突出显示的标题、列表标题
        /// 使用组件：重要列表项标题、强调内容
        /// 优势：自动支持Dynamic Type，内置粗体效果
        static let headline: Font = .headline

        /// 标准正文字体 - 对应系统.body (~17pt)
        /// 适用场景：输入框文本、重要内容文本、导航元素
        /// 使用组件：ChatInputBar输入框、重要内容区域
        /// 优势：自动支持Dynamic Type，最佳可读性
        static let body: Font = .body

        /// 次要标题字体 - 对应系统.subheadline (~15pt)
        /// 适用场景：副标题、描述性标题
        /// 使用组件：品牌副标题、区块副标题
        /// 优势：自动支持Dynamic Type
        static let subheadline: Font = .subheadline

        /// 说明文字字体 - 对应系统.caption (~12pt)
        /// 适用场景：说明文字、图片说明、表格注释
        /// 使用组件：描述信息、辅助文本
        /// 优势：自动支持Dynamic Type
        static let caption: Font = .caption

        // MARK: - 固定大小字体（特殊需求使用）

        /// 卡片标题固定字体 (14pt) - 用于卡片标题、按钮文字
        /// 适用场景：FeatureCard标题、按钮文字、标准内容文本
        /// 使用组件：FeatureCard.title、ChatToolsHeader、按钮文字
        /// 说明：设计要求固定14pt，不随系统字体大小变化
        static let cardTitle: Font = .system(size: 14, weight: .medium)

        /// 卡片副标题固定字体 (10pt) - 用于小标签、副标题
        /// 适用场景：FeatureCard副标题、小型标签、辅助图标
        /// 使用组件：FeatureCard.subtitle、小型状态标签、图标标注
        /// 说明：设计要求固定10pt，确保在小空间内的可读性
        static let cardSubtitle: Font = .system(size: 10, weight: .regular)

        /// 导航标题固定字体 (16pt) - 用于导航栏标题
        /// 适用场景：模型选择器、导航标题
        /// 使用组件：ChatTabNavigationBar模型名称
        /// 说明：设计要求固定16pt，保持导航栏视觉一致性
        static let navigationTitle: Font = .headline

        /// 紧凑正文固定字体 (14pt) - 用于大部分正文内容
        /// 适用场景：标准内容文本、正文段落、一般信息展示、内容区域
        /// 使用组件：大部分正文内容区域、内容展示组件、文本密集型界面
        /// 说明：设计要求固定14pt/regular，比系统body(17pt)更紧凑，适合精致风格
        /// 设计理由：当前16pt和17pt字体对精致风格偏大，14pt提供更好的视觉密度
        static let content: Font = .system(size: 14, weight: .regular)
    }

    // MARK: - 字体大小常量（向后兼容）

    /// 字体大小常量系统
    /// 提供CGFloat类型的字体大小，用于特殊场景或向后兼容
    enum FontSize {
        /// 超小字体 (10pt)
        static let xs: CGFloat = 10
        /// 小字体 (12pt)
        static let sm: CGFloat = 12
        /// 中等字体 (14pt)
        static let md: CGFloat = 14
        /// 大字体 (16pt)
        static let lg: CGFloat = 16
        /// 超大字体 (18pt)
        static let xl: CGFloat = 18
        /// 巨大字体 (20pt)
        static let xxl: CGFloat = 20
        /// 品牌标题字体 (28pt)
        static let brand: CGFloat = 28
    }

    // MARK: - 字体权重系统

    /// 统一字体权重系统
    /// 提供语义化的字体权重定义，配合字体大小使用
    enum FontWeight {
        /// 常规权重 - 用于正文内容、描述文本
        /// 适用场景：正文内容、描述信息、辅助文字
        static let regular: Font.Weight = .regular

        /// 中等权重 - 用于标题、按钮、重要文本
        /// 适用场景：卡片标题、按钮文字、导航元素、图标
        static let medium: Font.Weight = .medium

        /// 半粗体权重 - 用于重要标题、强调文本
        /// 适用场景：页面标题、重要标签、强调内容
        static let semibold: Font.Weight = .semibold

        /// 粗体权重 - 用于品牌标题、最高级别强调
        /// 适用场景：品牌Logo标题、特殊强调文本
        static let bold: Font.Weight = .bold
    }
}

// MARK: - 扩展

extension View {
    // MARK: - 系统字体样式（推荐使用，支持Dynamic Type）

    /// 应用大标题字体样式 - 系统.title (~28pt)
    /// 自动支持Dynamic Type，适用于品牌标题、应用名称
    func titleLargeStyle() -> some View {
        font(DesignSystem.Typography.titleLarge)
    }

    /// 应用中标题字体样式 - 系统.title2 (~22pt)
    /// 自动支持Dynamic Type，适用于页面标题、重要区块标题
    func titleMediumStyle() -> some View {
        font(DesignSystem.Typography.titleMedium)
    }

    /// 应用小标题字体样式 - 系统.title3 (~20pt)
    /// 自动支持Dynamic Type，适用于弹窗标题、导航图标
    func titleSmallStyle() -> some View {
        font(DesignSystem.Typography.titleSmall)
    }

    /// 应用突出显示字体样式 - 系统.headline (~17pt粗体)
    /// 自动支持Dynamic Type，适用于需要突出显示的标题
    func headlineStyle() -> some View {
        font(DesignSystem.Typography.headline)
    }

    /// 应用标准正文字体样式 - 系统.body (~17pt)
    /// 自动支持Dynamic Type，适用于输入框文本、重要内容
    func bodyStyle() -> some View {
        font(DesignSystem.Typography.body)
    }

    /// 应用次要标题字体样式 - 系统.subheadline (~15pt)
    /// 自动支持Dynamic Type，适用于副标题、描述性标题
    func subheadlineStyle() -> some View {
        font(DesignSystem.Typography.subheadline)
    }

    /// 应用说明文字字体样式 - 系统.caption (~12pt)
    /// 自动支持Dynamic Type，适用于说明文字、描述信息
    func captionStyle() -> some View {
        font(DesignSystem.Typography.caption)
    }

    // MARK: - 固定大小字体样式（特殊需求使用）

    /// 应用卡片标题字体样式 - 固定14pt/medium
    /// 不支持Dynamic Type，确保设计一致性
    func cardTitleFixedStyle() -> some View {
        font(DesignSystem.Typography.cardTitle)
    }

    /// 应用卡片副标题字体样式 - 固定10pt/regular
    /// 不支持Dynamic Type，确保小空间内的可读性
    func cardSubtitleFixedStyle() -> some View {
        font(DesignSystem.Typography.cardSubtitle)
    }

    /// 应用导航标题字体样式 - 固定16pt/medium
    /// 不支持Dynamic Type，保持导航栏视觉一致性
    func navigationTitleFixedStyle() -> some View {
        font(DesignSystem.Typography.navigationTitle)
    }

    /// 应用紧凑正文字体样式 - 固定14pt/regular
    /// 不支持Dynamic Type，确保精致风格的一致性
    /// 适用于大部分正文内容、标准文本展示
    func bodyCompactFixedStyle() -> some View {
        font(DesignSystem.Typography.content)
    }

    // MARK: - 向后兼容的字体大小方法

    /// 应用超小字体样式 (10pt)
    /// - Parameter weight: 字体权重，默认为regular
    func fontXS(weight: Font.Weight = DesignSystem.FontWeight.regular) -> some View {
        font(.system(size: DesignSystem.FontSize.xs, weight: weight))
    }

    /// 应用小字体样式 (12pt)
    /// - Parameter weight: 字体权重，默认为regular
    func fontSM(weight: Font.Weight = DesignSystem.FontWeight.regular) -> some View {
        font(.system(size: DesignSystem.FontSize.sm, weight: weight))
    }

    /// 应用中等字体样式 (14pt)
    /// - Parameter weight: 字体权重，默认为medium
    func fontMD(weight: Font.Weight = DesignSystem.FontWeight.medium) -> some View {
        font(.system(size: DesignSystem.FontSize.md, weight: weight))
    }

    /// 应用大字体样式 (16pt)
    /// - Parameter weight: 字体权重，默认为medium
    func fontLG(weight: Font.Weight = DesignSystem.FontWeight.medium) -> some View {
        font(.system(size: DesignSystem.FontSize.lg, weight: weight))
    }

    /// 应用超大字体样式 (18pt)
    /// - Parameter weight: 字体权重，默认为semibold
    func fontXL(weight: Font.Weight = DesignSystem.FontWeight.semibold) -> some View {
        font(.system(size: DesignSystem.FontSize.xl, weight: weight))
    }

    /// 应用巨大字体样式 (20pt)
    /// - Parameter weight: 字体权重，默认为medium
    func fontXXL(weight: Font.Weight = DesignSystem.FontWeight.medium) -> some View {
        font(.system(size: DesignSystem.FontSize.xxl, weight: weight))
    }

    /// 应用品牌标题字体样式 (28pt)
    /// - Parameter weight: 字体权重，默认为bold
    func fontBrand(weight: Font.Weight = DesignSystem.FontWeight.bold) -> some View {
        font(.system(size: DesignSystem.FontSize.brand, weight: weight))
    }

    // MARK: - 语义化字体样式（推荐使用）

    /// 应用卡片标题字体样式
    /// 使用固定14pt/medium，确保卡片布局一致性
    /// 适用于FeatureCard等组件标题
    func cardTitleStyle() -> some View {
        cardTitleFixedStyle()
            .foregroundColor(DesignSystem.Colors.textPrimary)
    }

    /// 应用卡片副标题字体样式
    /// 使用固定10pt/regular，确保小空间内的可读性
    /// 适用于FeatureCard等组件副标题
    func cardSubtitleStyle() -> some View {
        cardSubtitleFixedStyle()
            .foregroundColor(DesignSystem.Colors.textTertiary)
    }

    /// 应用输入框字体样式
    /// 使用系统.body，支持Dynamic Type，提升可访问性
    /// 适用于ChatInputBar等输入组件
    func inputTextStyle() -> some View {
        bodyStyle()
            .foregroundColor(DesignSystem.Colors.textPrimary)
    }

    /// 应用导航标题字体样式
    /// 使用固定16pt/medium，保持导航栏视觉一致性
    /// 适用于导航栏标题、模型选择器
    func navigationTitleStyle() -> some View {
        navigationTitleFixedStyle()
            .foregroundColor(DesignSystem.Colors.textPrimary)
    }

    /// 应用弹窗标题字体样式
    /// 使用系统.title3，支持Dynamic Type
    /// 适用于弹窗和页面标题
    func popupTitleStyle() -> some View {
        titleSmallStyle()
            .foregroundColor(DesignSystem.Colors.textPrimary)
    }

    /// 应用描述文本字体样式
    /// 使用系统.caption，支持Dynamic Type
    /// 适用于描述信息和辅助文本
    func descriptionTextStyle() -> some View {
        captionStyle()
            .foregroundColor(DesignSystem.Colors.textSecondary)
    }

    /// 应用按钮文字字体样式
    /// 使用固定14pt/medium，确保按钮视觉一致性
    /// 适用于按钮文字
    func buttonTextStyle() -> some View {
        cardTitleFixedStyle()
    }

    /// 应用品牌标题字体样式
    /// 使用系统.title，支持Dynamic Type，最佳品牌展示效果
    /// 适用于应用品牌标题
    func brandTitleStyle() -> some View {
        titleLargeStyle()
    }

    /// 应用列表标题字体样式
    /// 使用系统.headline，支持Dynamic Type，突出显示效果
    /// 适用于列表项标题、重要内容标题
    func listTitleStyle() -> some View {
        headlineStyle()
            .foregroundColor(DesignSystem.Colors.textPrimary)
    }

    /// 应用副标题字体样式
    /// 使用系统.subheadline，支持Dynamic Type
    /// 适用于品牌副标题、区块副标题
    func subtitleStyle() -> some View {
        subheadlineStyle()
            .foregroundColor(DesignSystem.Colors.textSecondary)
    }

    /// 应用标准内容文本字体样式
    /// 使用固定14pt/regular，适合大部分正文内容和标准文本展示
    /// 比系统body更紧凑，适合精致风格的应用设计
    /// 适用于内容展示、正文段落、一般信息文本
    func contentTextStyle() -> some View {
        bodyCompactFixedStyle()
            .foregroundColor(DesignSystem.Colors.textPrimary)
    }

    // MARK: - 设计系统相关

    /// 应用紧凑型圆角 (8pt)
    /// 适用于网格布局中的小卡片和紧凑型功能入口
    func compactCornerRadius() -> some View {
        cornerRadius(DesignSystem.Rounded.sm)
    }

    /// 应用标准圆角 (12pt)
    /// 适用于常规卡片组件、列表项和标准容器
    func standardCornerRadius() -> some View {
        cornerRadius(DesignSystem.Rounded.md)
    }

    /// 应用重要组件圆角 (16pt)
    /// 适用于核心交互元素、弹窗和主要输入框
    func prominentCornerRadius() -> some View {
        cornerRadius(DesignSystem.Rounded.lg)
    }

    /// 应用设计系统的主要文本样式
    func primaryTextStyle() -> some View {
        foregroundColor(DesignSystem.Colors.textPrimary)
    }

    /// 应用设计系统的次要文本样式
    func secondaryTextStyle() -> some View {
        foregroundColor(DesignSystem.Colors.textSecondary)
    }

    /// 应用设计系统的卡片背景样式
    func cardBackgroundStyle() -> some View {
        background(DesignSystem.Colors.backgroundCard)
    }

    /// 应用设计系统的页面背景样式
    func pageBackgroundStyle() -> some View {
        background(DesignSystem.Colors.backgroundPage)
    }

    /// 应用设计系统的边框样式
    /// - Parameter isSelected: 是否选中状态
    func designSystemBorder(isSelected: Bool = false) -> some View {
        overlay(
            RoundedRectangle(cornerRadius: DesignSystem.Rounded.md)
                .stroke(
                    Color.borderColor(isSelected: isSelected),
                    lineWidth: DesignSystem.BorderWidth.thin
                )
        )
    }
}
