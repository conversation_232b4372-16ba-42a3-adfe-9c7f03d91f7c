//
//  AppStoreHelper.swift
//  GrofyAI
//
//  Created by kissy on 2025/7/21.
//

import SwiftUI

struct AppStoreHelper {
    
    // 跳转到 App Store 的产品页面
    static func openAppStoreProductPage() {
//        https://apps.apple.com/cn/app/王者荣耀/id989673964
        let urlString = "https://apps.apple.com/app/id\(AppConfig.Store.AppId)"
        guard let url = URL(string: urlString) else { return }
        
        // 检查是否能打开这个 URL
        if UIApplication.shared.canOpenURL(url) {
            UIApplication.shared.open(url)
        }
    }

    // 直接跳转到 App Store 的“撰写评论”页面
    static func openAppStoreWriteReviewPage() {
        let urlString = "https://apps.apple.com/app/id\(AppConfig.Store.AppId)?action=write-review"
        guard let url = URL(string: urlString) else { return }

        if UIApplication.shared.canOpenURL(url) {
            UIApplication.shared.open(url)
        }
    }
}
