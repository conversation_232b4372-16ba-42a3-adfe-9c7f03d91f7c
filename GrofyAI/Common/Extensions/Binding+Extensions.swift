import SwiftUI

extension Binding {
    /// 将一个不可选的 Binding<Value> 转换为一个可选的 Binding<Value?>
    /// 这对于需要可选绑定的组件非常有用，而你的数据源是不可选的。
    func toOptional() -> Binding<Value?> {
        return Binding<Value?>(
            get: {
                // Getter: 直接返回原始值
                self.wrappedValue
            },
            set: { newValue in
                // Setter: 只有当新值不为 nil 时，才更新原始的绑定
                if let unwrappedValue = newValue {
                    self.wrappedValue = unwrappedValue
                }
            }
        )
    }

    /// 将一个可选值的绑定转换为一个非可选值的绑定，并在源值为 nil 时提供一个默认值。
    /// - Parameter defaultValue: 当源绑定的 `wrappedValue` 为 `nil` 时使用的默认值。
    /// - Returns: 一个新的、非可选的绑定。
    func orDefault<T>(_ defaultValue: T) -> Binding<T> where Value == T? {
        return Binding<T>(
            get: { self.wrappedValue ?? defaultValue },
            set: { self.wrappedValue = $0 }
        )
    }
}
