import Foundation
import StoreKit

// MARK: - MembershipType StoreKit 扩展

extension MembershipType {
    /// 获取该会员类型对应的月度订阅产品标识符
    var monthlyProductId: String? {
        switch self {
        case .free, .vip:
            return nil
        case .pro:
            return GrofyProduct.proMonthly.rawValue
        case .plus:
            return GrofyProduct.plusMonthly.rawValue
        case .ultra:
            return GrofyProduct.ultraMonthly.rawValue
        }
    }

    /// 获取该会员类型对应的年度订阅产品标识符
    var yearlyProductId: String? {
        switch self {
        case .free, .vip:
            return nil
        case .pro:
            return GrofyProduct.proYearly.rawValue
        case .plus:
            return GrofyProduct.plusYearly.rawValue
        case .ultra:
            return GrofyProduct.ultraYearly.rawValue
        }
    }

    /// 获取该会员类型对应的所有产品标识符
    var productIds: [String] {
        return [monthlyProductId, yearlyProductId].compactMap(\.self)
    }

    /// 根据产品标识符获取会员类型
    static func from(productId: String) -> MembershipType? {
        guard let product = GrofyProduct(rawValue: productId) else { return nil }
        return from(product.membershipType)
    }

    /// 获取可购买的会员类型（排除免费和VIP）
    static var purchasableTypes: [MembershipType] {
        return [.pro, .plus, .ultra]
    }
}

// MARK: - Product 价格格式化扩展

extension Product {
    /// 格式化显示月度价格
    var formattedMonthlyPrice: String {
        return displayPrice
    }

    /// 格式化显示年度价格（计算到每月）
    var formattedYearlyMonthlyPrice: String? {
        guard let subscription,
              subscription.subscriptionPeriod.unit == .year
        else {
            return nil
        }

        let monthlyPrice = price / 12
        let formatter = NumberFormatter()
        formatter.numberStyle = .currency
        formatter.currencyCode = priceFormatStyle.currencyCode
        formatter.locale = priceFormatStyle.locale

        return formatter.string(from: NSNumber(value: Double(truncating: monthlyPrice as NSNumber)))
    }

    /// 计算年付节省金额
    func yearlySavings(comparedTo monthlyProduct: Product?) -> String? {
        guard let monthlyProduct,
              let subscription,
              subscription.subscriptionPeriod.unit == .year
        else {
            return nil
        }

        let yearlyTotal = monthlyProduct.price * 12
        let savings = yearlyTotal - price

        let formatter = NumberFormatter()
        formatter.numberStyle = .currency
        formatter.currencyCode = priceFormatStyle.currencyCode
        formatter.locale = priceFormatStyle.locale

        return formatter.string(from: NSNumber(value: Double(truncating: savings as NSNumber)))
    }

    /// 计算折扣百分比
    func discountPercentage(comparedTo monthlyProduct: Product?) -> Int? {
        guard let monthlyProduct,
              let subscription,
              subscription.subscriptionPeriod.unit == .year
        else {
            return nil
        }

        let yearlyTotal = monthlyProduct.price * 12
        let discount = ((yearlyTotal - price) / yearlyTotal) * 100

        return Int(Double(truncating: discount as NSNumber))
    }
}
