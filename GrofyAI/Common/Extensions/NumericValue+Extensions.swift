import Foundation

/// (最终版) 定义一个可以被 UI 组件处理的、可量化的值类型。
/// 适用于滑块、数字输入框等。

public protocol NumericValue: Numeric, Comparable {
    /// 将当前值转换为 Double。
    var doubleValue: Double { get }

    /// 从一个 Double 值初始化。
    init(_ doubleValue: Double)
}

// MARK: - 让 Double 遵守 NumericValue

extension Double: NumericValue {
    /// Double 的 doubleValue 就是它自身。
    public var doubleValue: Double { self }

    /// 从另一个 Double 初始化。
    /// Swift 的 Double 类型已经内置了此构造器，但我们显式地实现它以符合协议。
    public init(_ doubleValue: Double) {
        self = doubleValue
    }
}

// MARK: - 让 Int 遵守 NumericValue

extension Int: NumericValue {
    /// 将 Int 转换为 Double。
    public var doubleValue: Double { Double(self) }

    /// 从一个 Double 初始化 Int。
    /// 注意：这会截断小数部分，这是将浮点滑块值赋给整数时的预期行为。
    /// 例如，5.8 会变成 5。
    public init(_ doubleValue: Double) {
//        self.init(doubleValue)
        let roundedValue = round(doubleValue)
        self.init(Int64(roundedValue))
    }
}
