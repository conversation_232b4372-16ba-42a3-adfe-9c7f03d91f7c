//
//  UIImage+Extensions.swift
//  GrofyAI
//
//  Created by kissy on 2025/7/24.
//

import UIKit

extension UIImage {
    
    /// 灵活地调整图片大小并进行JPEG压缩。
    /// - Parameters:
    ///   - maxDimension: 图片的最长边允许的最大尺寸。
    ///   - compressionQuality: JPEG压缩质量。
    ///   - isResizeEnabled: 一个布尔值，决定是否执行缩放操作。如果为 false，将仅对原图进行压缩。
    /// - Returns: 处理后的图片对象 (UIImage) 和它的数据 (Data)。
    func flexibleResizedAndCompressed(
        maxDimension: CGFloat = 1920.0,
        compressionQuality: CGFloat = 0.8,
        isResizeEnabled: Bool // 新增的控制参数
    ) -> (image: UIImage, data: Data)? {
        
        var imageToProcess = self
        
        // [!!] 核心优化逻辑: 只有在允许且有必要时才缩放
        if isResizeEnabled {
            let largerDimension = max(self.size.width, self.size.height)
            if largerDimension > maxDimension {
                let scale = maxDimension / largerDimension
                let newSize = CGSize(
                    width: self.size.width * scale,
                    height: self.size.height * scale
                )
                
                let renderer = UIGraphicsImageRenderer(size: newSize)
                imageToProcess = renderer.image { _ in
                    self.draw(in: CGRect(origin: .zero, size: newSize))
                }
            }
        }
        
        // 无论是否缩放，最后都进行JPEG压缩
        guard let jpegData = imageToProcess.jpegData(compressionQuality: compressionQuality) else {
            return nil
        }
        
        // 返回最终处理的图片和数据
        return (image: imageToProcess, data: jpegData)
    }
}
