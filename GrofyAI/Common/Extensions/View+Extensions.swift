import SwiftUI

extension View {
    // MARK: - 键盘处理相关

    /// 隐藏键盘
    /// 全局函数，用于在任何视图中隐藏当前显示的键盘
    func hideKeyboard() {
        UIApplication.shared.sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
    }

    // MARK: - 圆角处理相关

    /// 应用指定角的圆角
    /// 为视图的特定角落应用圆角效果，常用于弹窗和特殊布局组件
    /// - Parameters:
    ///   - radius: 圆角半径
    ///   - corners: 需要应用圆角的角落
    func cornerRadius(_ radius: CGFloat, corners: UIRectCorner) -> some View {
        clipShape(RoundedCorner(radius: radius, corners: corners))
    }

    /// 应用真正的自定义下拉刷新
    /// - Parameters:
    ///   - controller: 刷新控制器
    ///   - refreshAction: 刷新操作
    /// - Returns: 修饰后的视图
    func customRefreshable(
        _ controller: EnhancedRefreshController,
        refreshAction: @escaping () async throws -> Void
    ) -> some View {
        modifier(controller.customRefreshable(refreshAction))
    }

    /// 应用原生iOS风格的下拉刷新
    func nativeStyleRefreshable(
        onRefresh: @escaping () async throws -> Void
    ) -> some View {
        NativeStyleRefresh(onRefresh: onRefresh) {
            self
        }
    }

    // MARK: - 工具方法

    func keyboardAware() -> some View {
        modifier(KeyboardAwareModifier())
    }

    @ViewBuilder
    func `if`(_ condition: Bool, transform: (Self) -> some View) -> some View {
        if condition {
            transform(self)
        } else {
            self
        }
    }
}

// MARK: - 辅助形状定义

/// 自定义圆角形状
/// 支持为视图的指定角落应用圆角效果
private struct RoundedCorner: Shape {
    var radius: CGFloat = .infinity
    var corners: UIRectCorner = .allCorners

    func path(in rect: CGRect) -> Path {
        let path = UIBezierPath(
            roundedRect: rect,
            byRoundingCorners: corners,
            cornerRadii: CGSize(width: radius, height: radius)
        )
        return Path(path.cgPath)
    }
}
