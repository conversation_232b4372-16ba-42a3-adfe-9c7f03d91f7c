import SwiftUI

// MARK: - Color扩展支持十六进制颜色

extension Color {
    /// 通过十六进制字符串创建Color
    /// - Parameter hex: 十六进制颜色字符串，支持格式：#RGB, #RRGGBB, #AARRGGBB
    /// - 示例: Color(hex: "#FF0000"), Color(hex: "637DFE"), Color(hex: "#80FF0000")
    init(hex: String) {
        let hex = hex.trimmingCharacters(in: CharacterSet.alphanumerics.inverted)
        var int: UInt64 = 0
        Scanner(string: hex).scanHexInt64(&int)
        let a, r, g, b: UInt64
        switch hex.count {
        case 3: // RGB (12-bit)
            (a, r, g, b) = (255, (int >> 8) * 17, (int >> 4 & 0xF) * 17, (int & 0xF) * 17)
        case 6: // RGB (24-bit)
            (a, r, g, b) = (255, int >> 16, int >> 8 & 0xFF, int & 0xFF)
        case 8: // ARGB (32-bit)
            (a, r, g, b) = (int >> 24, int >> 16 & 0xFF, int >> 8 & 0xFF, int & 0xFF)
        default:
            (a, r, g, b) = (1, 1, 1, 0)
        }

        self.init(
            .sRGB,
            red: Double(r) / 255,
            green: Double(g) / 255,
            blue: Double(b) / 255,
            opacity: Double(a) / 255
        )
    }

    /// 根据颜色模式获取自适应文本色
    /// - Parameter colorScheme: 当前颜色模式
    /// - Returns: 适配当前模式的文本颜色
    static func adaptiveTextColor(for colorScheme: ColorScheme) -> Color {
        colorScheme == .dark ? .white : DesignSystem.Colors.textPrimary
    }

    /// 根据颜色模式获取自适应背景色
    /// - Parameter colorScheme: 当前颜色模式
    /// - Returns: 适配当前模式的背景颜色
    static func adaptiveBackgroundColor(for colorScheme: ColorScheme) -> Color {
        colorScheme == .dark ? Color.black : DesignSystem.Colors.backgroundPage
    }

    /// 获取按钮状态颜色
    /// - Parameters:
    ///   - isEnabled: 是否启用
    ///   - isPressed: 是否按下
    /// - Returns: 对应状态的颜色
    static func buttonColor(isEnabled: Bool, isPressed: Bool = false) -> Color {
        if !isEnabled {
            return DesignSystem.Colors.disabled
        }
        return isPressed ? DesignSystem.Colors.primary.opacity(0.8) : DesignSystem.Colors.primary
    }

    /// 获取边框颜色（支持选中状态）
    /// - Parameter isSelected: 是否选中
    /// - Returns: 对应状态的边框颜色
    static func borderColor(isSelected: Bool = false) -> Color {
        isSelected ? DesignSystem.Colors.primary : DesignSystem.Colors.border
    }
}
