import Foundation

/// let user = User(name: "<PERSON>", age: 18)
/// if let dict = user.toDictionary() {
///    print(dict)
/// }
///
extension Encodable {
    func toDictionary(removeNil: Bool = true, convertBool: Bool = true) -> [String: Any]? {
        do {
            let data = try JSONEncoder().encode(self)
            let jsonObject = try JSONSerialization.jsonObject(with: data, options: [])
            guard var dictionary = jsonObject as? [String: Any] else { return nil }

            if removeNil {
                dictionary = dictionary.filter { !($0.value is NSNull) }
            }

            if convertBool {
                for (key, value) in dictionary {
                    if let number = value as? NSNumber {
                        // NSNumber 只有 bool 类型才改
                        if CFGetTypeID(number) == CFBooleanGetTypeID() {
                            dictionary[key] = number.boolValue
                        }
                    }
                }
            }

            return dictionary
        } catch {
            print("Error converting to dictionary: \(error)")
            return nil
        }
    }
}
