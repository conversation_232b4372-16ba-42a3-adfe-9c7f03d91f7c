import Foundation

extension Dictionary {
    /// 移除字典中所有值为 nil 的键值对
    /// - Returns: 返回一个新的字典，不包含任何 nil 值
    func removingNilValues() -> [Key: Any] where Value == Any? {
        return compactMapValues { $0 }
    }

    // 等同于
    // func removingEmptyValues() -> [Key: Any] where Value == Any? {
    //     return self.compactMapValues { value in
    //         if let str = value as? String, str.isEmpty { return nil }
    //         if let arr = value as? [Any], arr.isEmpty { return nil }
    //         if let dict = value as? [String: Any], dict.isEmpty { return nil }
    //         return value
    //     }
    // }
}

extension [String: Any] {
    /// 尝试从字典中获取一个值，将其转换为字符串，然后用该字符串初始化一个 RawRepresentable 的枚举。
    /// - Parameter key: 字典的键.
    /// - Returns: 如果所有步骤都成功，则返回枚举实例；否则返回 nil.
    func decode<T: RawRepresentable>(key: Key) -> T? where T.RawValue == String {
        // 1. 从字典中获取值，并尝试转换为 String
        // 2. 如果成功，用这个 String 初始化枚举
        // 这两步通过可选链 (optional chaining) 和 flatMap 优雅地结合在一起
        return (self[key] as? String).flatMap(T.init(rawValue:))
    }

    func decodeInt<T: RawRepresentable>(key: Key) -> T? where T.RawValue == Int {
        // 1. 从字典中获取值，并尝试转换为 Int
        // 2. 如果成功，用这个 Int 初始化枚举
        // 这两步通过可选链 (optional chaining) 和 flatMap 优雅地结合在一起
        return (self[key] as? Int).flatMap(T.init(rawValue:))
    }

    /// 从字典中解码出一个 KlingConfig 实例
    func decodeKlingConfig(key: Key) -> KlingConfig? {
        // 1. 尝试将 key 对应的值转换为 [String: Any] 或更具体的 [String: Double/Int]
        guard let configDict = self[key] as? [String: Any] else {
            return nil
        }

        // 2. 从嵌套字典中安全地提取每个值
        //    使用 ?? 0 来提供默认值，防止因某个键缺失而导致整个解码失败
        let horizontal = configDict["horizontal"] as? Int ?? 0
        let vertical = configDict["vertical"] as? Int ?? 0
        let pan = configDict["pan"] as? Int ?? 0
        let tilt = configDict["tilt"] as? Int ?? 0
        let roll = configDict["roll"] as? Int ?? 0
        let zoom = configDict["zoom"] as? Int ?? 0

        // 3. 使用提取的值创建 KlingConfig 实例
        //    (假设 KlingConfig 的成员是 Double 类型)
        return KlingConfig(
            horizontal: horizontal,
            vertical: vertical,
            pan: pan,
            tilt: tilt,
            roll: roll,
            zoom: zoom
        )
    }
}
