import SwiftUI
import UIKit

/// 隐藏导航栏边框并可选地设置背景颜色。
struct HideNavigationBarLineModifier: ViewModifier {
    var backgroundColor: Color?

    private var oldStandardAppearance: UINavigationBarAppearance?
    private var oldScrollEdgeAppearance: UINavigationBarAppearance?

    init(backgroundColor: Color? = nil) {
        self.backgroundColor = backgroundColor

        oldStandardAppearance = UINavigationBar.appearance().standardAppearance
        oldScrollEdgeAppearance = UINavigationBar.appearance().scrollEdgeAppearance
    }

    func body(content: Content) -> some View {
        content
            .onAppear {
                let standardAppearance = UINavigationBarAppearance()
                let scrollEdgeAppearance = UINavigationBarAppearance()

                standardAppearance.configureWithDefaultBackground()
                standardAppearance.shadowColor = .clear // 移除边框

                if let backgroundColor {
                    standardAppearance.backgroundColor = UIColor(backgroundColor)
                }

                scrollEdgeAppearance.configureWithTransparentBackground()
                scrollEdgeAppearance.shadowColor = .clear // 移除边框

                UINavigationBar.appearance().standardAppearance = standardAppearance
                UINavigationBar.appearance().scrollEdgeAppearance = scrollEdgeAppearance
            }
            .onDisappear {
                if let savedAppearance = oldStandardAppearance {
                    UINavigationBar.appearance().standardAppearance = savedAppearance
                }
                if let savedScrollEdgeAppearance = oldScrollEdgeAppearance {
                    UINavigationBar.appearance().scrollEdgeAppearance = savedScrollEdgeAppearance
                }
            }
    }
}

extension View {
    func hideNavigationBarLine() -> some View {
        modifier(HideNavigationBarLineModifier())
    }

    func hideNavigationBarLine(backgroundColor: Color) -> some View {
        modifier(HideNavigationBarLineModifier(backgroundColor: backgroundColor))
    }
}
