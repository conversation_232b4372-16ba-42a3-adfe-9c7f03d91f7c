import Foundation

// MARK: - 数组扩展（去重）

extension Array {
    func uniqued<T: Hashable>(by keyPath: KeyPath<Element, T>) -> [Element] {
        var seen = Set<T>()
        return filter { seen.insert($0[keyPath: keyPath]).inserted }
    }

    func uniqued<T: Hashable>(by transform: (Element) -> T) -> [Element] {
        var seen = Set<T>()
        return filter { seen.insert(transform($0)).inserted }
    }

    var isNilOrEmpty: Bool {
        return isEmpty
    }
}
