import Kingfisher
import SwiftUI

extension KFImage {
    static func from(urlString: String?) -> KFImage? {
        guard let urlString else { return nil }

        if urlString.hasPrefix("data:image/"), urlString.contains(";base64,") {
            let components = urlString.split(separator: ",", maxSplits: 1)
            guard components.count == 2 else { return nil }

            let base64String = String(components[1])
            let provider = Base64ImageDataProvider(
                base64String: base64String,
                cacheKey: urlString.hashValue.description
            )

            return KFImage(source: .provider(provider))
        } else {
            guard let url = URL(string: urlString) else { return nil }
            return KFImage(url)
        }
    }
}

extension View {
    @ViewBuilder
    func kfImage(
        from urlString: String?,
        placeholder: (() -> AnyView)? = nil,
        onSuccess: ((RetrieveImageResult) -> Void)? = nil,
        onFailure: ((KingfisherError) -> Void)? = nil
    ) -> some View {
        if let kfImage = KFImage.from(urlString: urlString) {
            kfImage
                .placeholder {
                    if let placeholder {
                        placeholder()
                    } else {
                        ProgressView()
                    }
                }
                .onSuccess { result in
                    onSuccess?(result)
                }
                .onFailure { error in
                    onFailure?(error)
                }
        } else {
            if let placeholder {
                placeholder()
            } else {
                EmptyView()
            }
        }
    }
}

func createKingfisherSource(from urlString: String?) -> Source? {
    guard let urlString else { return nil }

    if urlString.hasPrefix("data:image/"), urlString.contains(";base64,") {
        let components = urlString.split(separator: ",", maxSplits: 1)
        guard components.count == 2 else { return nil }

        let base64String = String(components[1])
        return .provider(Base64ImageDataProvider(
            base64String: base64String,
            cacheKey: urlString.hashValue.description
        ))
    } else {
        guard let url = URL(string: urlString) else { return nil }
        return .network(url)
    }
}
