//
//  VideoAiEnum.swift
//  GrofyAI
//
//  Created by kissy on 2025/5/1.
//


enum VideoAiModel: String, CaseIterable, Codable {
    case wanx = "Wanx"
    case kling = "KLing"
    case minimax = "Minimax"
    case pixverse = "Pixverse"
    case runway = "Runway"

  
    //中文描述
    var description: String {
        switch self {
         case .wanx:
             return "WANX"
         case .kling:
             return "KLing"
         case .minimax:
             return "MiniMAx"
         case .pixverse:
             return "PixVerse"
        case .runway:
            return "Runway"
        }
    }
}

//MARK: VideoAi
enum VideoAiModel1: String, CaseIterable {
    //万象
    case wanx_t2v = "wanx_t2v", wanx_i2v = "wanx_i2v"
    
    //克灵
    case kling_t2v = "kling_t2v", kling_i2v = "kling_i2v"
    
    //minimax
    case minimax_t2v = "minimax_t2v", minimax_i2v = "minimax_i2v"
    
    //pixverse  
    case pixverse_t2v = "pixverse_t2v", pixverse_i2v = "pixverse_i2v"
    
    //runway
    case runway_i2v = "runway_i2v"
    
    //效果视频
    case effect_video = "effect_video"
    

  
    //中文描述
    var description: String {
        switch self {
        case .wanx_t2v:
            return "万象-文本转视频"
        case .wanx_i2v:
            return "万象-图片转视频"
        case .kling_t2v:
            return "克灵-文本转视频"
        case .kling_i2v:
            return "克灵-图片转视频"
        case .minimax_t2v:
            return "minimax-文本转视频"
        case .minimax_i2v:
            return "minimax-图片转视频"
        case .pixverse_t2v:
            return "pixverse-文本转视频"
        case .pixverse_i2v:
            return "pixverse-图片转视频"
        case .runway_i2v:
            return "runway-图片转视频"
        case .effect_video:
            return "效果视频"
        }
    }
}
