//
//  ImageAiEnum.swift
//  GrofyAI
//
//  Created by kissy on 2025/4/25.
//
//MARK: ImageAi
enum ImageAiModel: String, CaseIterable, Codable {
    case stable_diffusion_3_5 = "StableDiffusion"
    case midjourney = "MidJourney"
    case openAi = "ChatGPT"
    case flux = "Flux"
    case ideogram = "Ideogram"
    

    //中文描述
    var description: String {
        switch self {
         case .openAi:
             return "ChatGPT"
         case .flux:
             return "Flux"
         case .ideogram:
             return "Ideogram"
         case .midjourney:
             return "Midjourney"
         case .stable_diffusion_3_5:
             return "StableDiffusion"
        }
    }
}






