import Foundation

// MARK: - 会员等级枚举定义

enum MembershipLevel: String, CaseIterable {
    case free = "FREE"
    case vip = "VIP"
    case pro = "PRO"
    case plus = "PLUS"
    case ultra = "ULTRA"

    /// 会员等级描述
    var description: String {
        switch self {
        case .free:
            return "免费用户"
        case .vip:
            return "VIP"
        case .pro:
            return "PRO"
        case .plus:
            return "PLUS"
        case .ultra:
            return "ULTRA"
        }
    }

    /// 会员等级显示名称
    var displayName: String {
        switch self {
        case .free:
            return "免费用户"
        case .vip:
            return "高级会员"
        case .pro:
            return "专业版"
        case .plus:
            return "旗舰版"
        case .ultra:
            return "无限版"
        }
    }

    /// 会员等级权重
    var weight: Int {
        switch self {
        case .free:
            return 0
        case .vip:
            return 1
        case .pro:
            return 2
        case .plus:
            return 3
        case .ultra:
            return 4
        }
    }

    /// 是否为付费会员
    var isPaid: Bool {
        return self != .free
    }

    /// 根据字符串创建会员等级
    static func from(_ string: String?) -> MembershipLevel {
        guard let string else { return .free }
        return MembershipLevel(rawValue: string.uppercased()) ?? .free
    }

    /// 判断当前等级是否高于或等于指定等级
    func isEqualOrHigherThan(_ level: MembershipLevel) -> Bool {
        return weight >= level.weight
    }

    /// 判断当前等级是否高于指定等级
    func isHigherThan(_ level: MembershipLevel) -> Bool {
        return weight > level.weight
    }

    /// 判断是否为VIP或更高级别会员
    static func isVIP(_ level: String?) -> Bool {
        let memberLevel = from(level)
        return memberLevel.isEqualOrHigherThan(.vip)
    }

    /// 判断是否为免费用户
    static func isFree(_ level: String?) -> Bool {
        return from(level) == .free
    }

    /// 判断是否为付费会员
    static func isPaidMember(_ level: String?) -> Bool {
        return from(level).isPaid
    }

    /// 获取会员等级显示名称
    static func displayName(for level: String?) -> String {
        return from(level).displayName
    }
}
