//
//  CompressionUIImage.swift
//  GrofyAI
//
//  Created by kissy on 2025/7/28.
//
import SwiftUI

///压缩图片
func compressionUIImage(originalImage: UIImage, name: String?) throws -> (image: UIImage, fileName: String) {
    
    // 1. 确定文件名
    let fileName = name ?? "image-\(UUID().uuidString).jpg"
    
    // 2. 检查原始图片大小，决定是否需要缩放
    // 这种检查方式比获取完整 Data 更高效，因为它只读取元数据
    let sizeThresholdInBytes = 500 * 1024
    let originalDataForCheck = originalImage.jpegData(compressionQuality: 1.0)
    let shouldResize = (originalDataForCheck?.count ?? (sizeThresholdInBytes + 1)) > sizeThresholdInBytes

    if shouldResize {
        print("ℹ️ 图片体积较大或未知，将进行缩放和压缩。")
    } else {
        print("✅ 图片体积小于 500KB，跳过缩放步骤，仅压缩。")
    }
    
    // 3. 调用核心处理逻辑
    guard let processed = originalImage.flexibleResizedAndCompressed(
        maxDimension: 1920.0,
        compressionQuality: 0.8,
        isResizeEnabled: shouldResize
    ) else {
        // 如果处理失败，抛出一个错误
        throw NSError(domain: "ImageProcessingError", code: -1, userInfo: [NSLocalizedDescriptionKey: "图片处理失败（缩放或压缩时出错）。"])
    }
    
    // 4. 返回处理好的 UIImage 和文件名
    // `processed` 本身就是 (image: UIImage, data: Data) 的元组
    return (image: processed.image, fileName: fileName)
}
