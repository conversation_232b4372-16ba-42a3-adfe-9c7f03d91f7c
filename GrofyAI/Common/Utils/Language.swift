import Foundation

enum Language: String, CaseIterable {
    case english = "en"
    case spanish = "es"
    case arabic = "ar"
    case hindi = "hi"
    case french = "fr"
    case portuguese = "pt"
    case russian = "ru"
    case simplifiedChinese = "zh-Hans"
    case traditionalChinese = "zh-Hant"
    case japanese = "ja"
    case korean = "ko"
    case german = "de"
    case italian = "it"
    case dutch = "nl"
    case vietnamese = "vi"
    case thai = "th"
    case swedish = "sv"
    case hungarian = "hu"
    case finnish = "fi"
    case czech = "cs"
    case norwegian = "no"

    // 获取语言的本地化显示名称
    var displayName: String {
        switch self {
        case .simplifiedChinese:
            return "简体中文"
        case .traditionalChinese:
            return "繁體中文"
        case .english:
            return "English"
        case .japanese:
            return "日本語"
        case .korean:
            return "한국어"
        case .german:
            return "Deutsch"
        case .italian:
            return "Italiano"
        case .spanish:
            return "Español"
        case .portuguese:
            return "Português"
        case .french:
            return "Français"
        case .arabic:
            return "العربية"
        case .hindi:
            return "हिन्दी"
        case .dutch:
            return "Nederlands"
        case .hungarian:
            return "Magyar"
        case .swedish:
            return "Svenska"
        case .vietnamese:
            return "Tiếng Việt"
        case .finnish:
            return "Suomi"
        case .czech:
            return "Čeština"
        case .thai:
            return "ไทย"
        case .norwegian:
            return "Norsk"
        case .russian:
            return "Русский"
        }
    }

    // 获取对应的国家/地区代码(用于FlagKit)
    var flagCode: String {
        switch self {
        case .simplifiedChinese:
            return "CN"
        case .traditionalChinese:
            return "TW"
        case .english:
            return "GB"
        case .japanese:
            return "JP"
        case .korean:
            return "KR"
        case .german:
            return "DE"
        case .italian:
            return "IT"
        case .spanish:
            return "ES"
        case .portuguese:
            return "PT"
        case .french:
            return "FR"
        case .arabic:
            return "SA" // 使用沙特阿拉伯作为阿拉伯语的代表
        case .hindi:
            return "IN"
        case .dutch:
            return "NL"
        case .hungarian:
            return "HU"
        case .swedish:
            return "SE"
        case .vietnamese:
            return "VN"
        case .finnish:
            return "FI"
        case .czech:
            return "CZ"
        case .thai:
            return "TH"
        case .norwegian:
            return "NO"
        case .russian:
            return "RU"
        }
    }

    // 判断是否是RTL语言
    var isRTL: Bool {
        switch self {
        case .arabic:
            return true
        default:
            return false
        }
    }

    // 获取语言的英文名称
    var englishName: String {
        switch self {
        case .simplifiedChinese:
            return "Simplified Chinese"
        case .traditionalChinese:
            return "Traditional Chinese"
        case .english:
            return "English"
        case .japanese:
            return "Japanese"
        case .korean:
            return "Korean"
        case .german:
            return "German"
        case .italian:
            return "Italian"
        case .spanish:
            return "Spanish"
        case .portuguese:
            return "Portuguese"
        case .french:
            return "French"
        case .arabic:
            return "Arabic"
        case .hindi:
            return "Hindi"
        case .dutch:
            return "Dutch"
        case .hungarian:
            return "Hungarian"
        case .swedish:
            return "Swedish"
        case .vietnamese:
            return "Vietnamese"
        case .finnish:
            return "Finnish"
        case .czech:
            return "Czech"
        case .thai:
            return "Thai"
        case .norwegian:
            return "Norwegian"
        case .russian:
            return "Russian"
        }
    }

    // 获取设备当前语言
    static var deviceLanguage: String {
        let code = Bundle.main.preferredLocalizations.first ?? "en"
        return convert(from: code)
    }

    // 转换语言代码格式
    private static func convert(from code: String) -> String {
        switch code {
        case "zh-Hans":
            return "zh_Hans"
        case "zh-Hant":
            return "zh_Hant"
        default:
            return code.replacingOccurrences(of: "-", with: "_")
        }
    }
}
