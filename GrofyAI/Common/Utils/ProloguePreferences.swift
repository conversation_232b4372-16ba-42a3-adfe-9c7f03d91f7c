import Foundation

// MARK: - Prologue显示偏好管理

/// 管理各种聊天场景下Prologue的显示偏好
final class ProloguePreferences {
    static let shared = ProloguePreferences()

    private enum Keys {
        static let imageChatPrologueHidden = "prologue_image_chat_hidden"
        static let textChatPrologueHidden = "prologue_text_chat_hidden"
        static let fileChatPrologueHidden = "prologue_file_chat_hidden"
        static let visionChatPrologueHidden = "prologue_vision_chat_hidden"
    }

    // MARK: - 图片聊天Prologue偏好

    /// 图片聊天Prologue是否被隐藏
    var isImageChatPrologueHidden: Bool {
        get {
            UserDefaults.standard.bool(forKey: Keys.imageChatPrologueHidden)
        }
        set {
            UserDefaults.standard.set(newValue, forKey: Keys.imageChatPrologueHidden)
        }
    }

    /// 隐藏图片聊天Prologue
    func hideImageChatPrologue() {
        isImageChatPrologueHidden = true
    }

    /// 显示图片聊天Prologue
    func showImageChatPrologue() {
        isImageChatPrologueHidden = false
    }

    // MARK: - 文本聊天Prologue偏好

    /// 文本聊天Prologue是否被隐藏
    var isTextChatPrologueHidden: Bool {
        get {
            UserDefaults.standard.bool(forKey: Keys.textChatPrologueHidden)
        }
        set {
            UserDefaults.standard.set(newValue, forKey: Keys.textChatPrologueHidden)
        }
    }

    /// 隐藏文本聊天Prologue
    func hideTextChatPrologue() {
        isTextChatPrologueHidden = true
    }

    /// 显示文本聊天Prologue
    func showTextChatPrologue() {
        isTextChatPrologueHidden = false
    }

    // MARK: - 文件聊天Prologue偏好

    /// 文件聊天Prologue是否被隐藏
    var isFileChatPrologueHidden: Bool {
        get {
            UserDefaults.standard.bool(forKey: Keys.fileChatPrologueHidden)
        }
        set {
            UserDefaults.standard.set(newValue, forKey: Keys.fileChatPrologueHidden)
        }
    }

    /// 隐藏文件聊天Prologue
    func hideFileChatPrologue() {
        isFileChatPrologueHidden = true
    }

    /// 显示文件聊天Prologue
    func showFileChatPrologue() {
        isFileChatPrologueHidden = false
    }

    // MARK: - 图片识别聊天Prologue偏好

    /// 图片识别聊天Prologue是否被隐藏
    var isVisionChatPrologueHidden: Bool {
        get {
            UserDefaults.standard.bool(forKey: Keys.visionChatPrologueHidden)
        }
        set {
            UserDefaults.standard.set(newValue, forKey: Keys.visionChatPrologueHidden)
        }
    }

    /// 隐藏图片识别聊天Prologue
    func hideVisionChatPrologue() {
        isVisionChatPrologueHidden = true
    }

    /// 显示图片识别聊天Prologue
    func showVisionChatPrologue() {
        isVisionChatPrologueHidden = false
    }

    // MARK: - 重置所有偏好

    /// 重置所有Prologue显示偏好
    func resetAllPreferences() {
        isImageChatPrologueHidden = false
        isTextChatPrologueHidden = false
        isFileChatPrologueHidden = false
        isVisionChatPrologueHidden = false
    }
}
