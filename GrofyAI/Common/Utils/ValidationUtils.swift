import Foundation

/// 验证工具
enum ValidationUtils {
    // MARK: - 正则表达式常量

    /// 邮箱验证正则表达式
    /// 支持标准邮箱格式：用户名@域名.顶级域名
    private static let emailRegex = "^[\\w\\.-]+@[\\w\\.-]+\\.[A-Za-z]{2,}$"

    /// 密码字符验证正则表达式
    /// 只允许英文字母、数字和常用符号，不允许中文字符
    private static let passwordCharacterRegex = "^[a-zA-Z0-9!@#$%^&*()_+\\-=\\[\\]{};':\"\\\\|,.<>\\/?]*$"

    // MARK: - 预编译的正则表达式

    /// 预编译的邮箱验证谓词
    private static let emailPredicate = NSPredicate(format: "SELF MATCHES %@", emailRegex)

    /// 预编译的密码字符验证谓词
    private static let passwordCharacterPredicate = NSPredicate(format: "SELF MATCHES %@", passwordCharacterRegex)

    // MARK: - 验证常量

    /// 密码最小长度要求
    static let minimumPasswordLength = 8

    // MARK: - 邮箱验证

    /// 验证邮箱格式是否有效
    static func isValidEmail(_ email: String) -> Bool {
        return emailPredicate.evaluate(with: email)
    }

    /// 获取邮箱验证错误信息
    static func getEmailValidationError(_ email: String) -> String? {
        if email.isEmpty {
            return nil
        }

        if !isValidEmail(email) {
            return "请输入有效的邮箱地址"
        }

        return nil
    }

    // MARK: - 密码验证

    /// 验证密码字符是否符合要求（不包含中文等非法字符）
    static func hasValidPasswordCharacters(_ password: String) -> Bool {
        return passwordCharacterPredicate.evaluate(with: password)
    }

    /// 验证密码长度是否符合要求
    static func hasValidPasswordLength(_ password: String) -> Bool {
        return password.count >= minimumPasswordLength
    }

    /// 验证密码是否完全有效（字符和长度都符合要求）
    static func isValidPassword(_ password: String) -> Bool {
        return hasValidPasswordCharacters(password) && hasValidPasswordLength(password)
    }

    /// 获取密码验证错误信息
    static func getPasswordValidationError(_ password: String) -> String? {
        if password.isEmpty {
            return nil
        }

        if !hasValidPasswordCharacters(password) {
            return "密码只能包含英文字母、数字和符号"
        }

        if !hasValidPasswordLength(password) {
            return "密码至少需要\(minimumPasswordLength)个字符"
        }

        return nil
    }

    // MARK: - 验证码验证

    /// 验证验证码是否为空
    static func isValidVerificationCode(_ code: String) -> Bool {
        return !code.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
    }

    /// 获取验证码验证错误信息
    static func getVerificationCodeValidationError(_ code: String) -> String? {
        if !isValidVerificationCode(code) {
            return "请输入验证码"
        }

        return nil
    }
}

extension String {
    /// 验证当前字符串是否为有效邮箱
    var isValidEmail: Bool {
        return ValidationUtils.isValidEmail(self)
    }

    /// 验证当前字符串是否为有效密码
    var isValidPassword: Bool {
        return ValidationUtils.isValidPassword(self)
    }

    /// 获取当前字符串作为邮箱的验证错误信息
    var emailValidationError: String? {
        return ValidationUtils.getEmailValidationError(self)
    }

    /// 获取当前字符串作为密码的验证错误信息
    var passwordValidationError: String? {
        return ValidationUtils.getPasswordValidationError(self)
    }
}
