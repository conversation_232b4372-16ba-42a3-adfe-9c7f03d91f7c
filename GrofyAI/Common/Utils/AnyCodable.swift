//
//  AnyCodable.swift
//  GrofyAI
//
//  Created by kissy on 2025/6/20.
//

import Foundation

struct RobustAnyCodable: Codable {
    let value: Any

    init(_ value: Any) {
        self.value = value
    }

    init(from decoder: Decoder) throws {
        let container = try decoder.singleValueContainer()

        // 1. 优先解码 Double，避免精度丢失 (因为 123 也能被解码为 Double)
        if let doubleValue = try? container.decode(Double.self) {
            // 如果是整数，则存为 Int
            if doubleValue == floor(doubleValue) {
                self.value = Int(doubleValue)
            } else {
                self.value = doubleValue
            }
        } else if let boolValue = try? container.decode(Bool.self) {
            self.value = boolValue
        } else if let stringValue = try? container.decode(String.self) {
            self.value = stringValue
        } else if let arrayValue = try? container.decode([AnyCodable].self) {
            self.value = arrayValue.map { $0.value }
        } else if let dictionaryValue = try? container.decode([String: AnyCodable].self) {
            self.value = dictionaryValue.mapValues { $0.value }
        } else if container.decodeNil() {
            // 2. 明确处理 null 值
            self.value = NSNull()
        } else {
            // 3. 如果都不是，则抛出错误，而不是静默失败
            throw DecodingError.dataCorruptedError(in: container, debugDescription: "AnyCodable value cannot be decoded")
        }
    }

    func encode(to encoder: Encoder) throws {
        var container = encoder.singleValueContainer()
        
        // 4. 完整的编码逻辑，覆盖所有解码时处理的类型
        switch value {
        case is NSNull:
            try container.encodeNil()
        case let boolValue as Bool:
            try container.encode(boolValue)
        case let intValue as Int:
            try container.encode(intValue)
        case let doubleValue as Double:
            try container.encode(doubleValue)
        case let stringValue as String:
            try container.encode(stringValue)
        case let arrayValue as [Any]:
            try container.encode(arrayValue.map { AnyCodable($0) })
        case let dictionaryValue as [String: Any]:
            try container.encode(dictionaryValue.mapValues { AnyCodable($0) })
        default:
            // 5. 如果遇到无法编码的类型，抛出错误
            throw EncodingError.invalidValue(value, EncodingError.Context(codingPath: container.codingPath, debugDescription: "AnyCodable value cannot be encoded"))
        }
    }
}
