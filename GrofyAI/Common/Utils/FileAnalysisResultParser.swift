import Foundation

// MARK: - Python字典格式转换器

class DictFormatConverter {
    private enum ParseState {
        case normal // 正常状态
        case inString // 在字符串内部
        case inEscape // 在转义字符内部
    }

    /// 将字典转换为JSON
    func convert(_ pythonDict: String) -> String {
        var result = ""
        var state = ParseState.normal
        var i = pythonDict.startIndex

        while i < pythonDict.endIndex {
            let char = pythonDict[i]

            switch state {
            case .normal:
                switch char {
                case "'":
                    // 检查是否是字符串开始的单引号
                    if isStringStartQuote(at: i, in: pythonDict) {
                        result.append("\"")
                        state = .inString
                    } else {
                        // 撇号，保持原样
                        result.append(char)
                    }
                case "T":
                    // 处理 True
                    if isKeywordAt(i, keyword: "True", in: pythonDict) {
                        result.append("true")
                        i = pythonDict.index(i, offsetBy: 3) // 跳过 "rue"
                    } else {
                        result.append(char)
                    }
                case "F":
                    // 处理 False
                    if isKeywordAt(i, keyword: "False", in: pythonDict) {
                        result.append("false")
                        i = pythonDict.index(i, offsetBy: 4) // 跳过 "alse"
                    } else {
                        result.append(char)
                    }
                case "N":
                    // 处理 None
                    if isKeywordAt(i, keyword: "None", in: pythonDict) {
                        result.append("null")
                        i = pythonDict.index(i, offsetBy: 3) // 跳过 "one"
                    } else {
                        result.append(char)
                    }
                default:
                    result.append(char)
                }

            case .inString:
                switch char {
                case "'":
                    // 检查是否是字符串结束的单引号
                    if isStringEndQuote(at: i, in: pythonDict) {
                        result.append("\"")
                        state = .normal
                    } else {
                        // 这是字符串内部的撇号，需要转义
                        result.append("\\'")
                    }
                case "\"":
                    // 字符串内部的双引号需要转义
                    result.append("\\\"")
                case "\\":
                    // 转义字符开始
                    result.append(char)
                    state = .inEscape
                default:
                    result.append(char)
                }

            case .inEscape:
                // 转义字符，直接添加并返回字符串状态
                result.append(char)
                state = .inString
            }

            i = pythonDict.index(after: i)
        }

        return result
    }

    // MARK: - 辅助方法

    /// 检查指定位置的单引号是否是字符串开始的引号
    private func isStringStartQuote(at index: String.Index, in string: String) -> Bool {
        // 检查前一个非空白字符
        var prevIndex = index
        while prevIndex > string.startIndex {
            prevIndex = string.index(before: prevIndex)
            let prevChar = string[prevIndex]

            if !prevChar.isWhitespace {
                // 如果前一个字符是 :, [, {, , 则这是字符串开始
                return prevChar == ":" || prevChar == "[" || prevChar == "{" || prevChar == ","
            }
        }

        // 如果到达字符串开始，也认为是字符串开始
        return true
    }

    /// 检查指定位置的单引号是否是字符串结束的引号
    private func isStringEndQuote(at index: String.Index, in string: String) -> Bool {
        // 检查下一个非空白字符
        var nextIndex = string.index(after: index)
        while nextIndex < string.endIndex {
            let nextChar = string[nextIndex]

            if !nextChar.isWhitespace {
                // 如果下一个字符是 :, ], }, , 则这是字符串结束
                return nextChar == ":" || nextChar == "]" || nextChar == "}" || nextChar == ","
            }

            nextIndex = string.index(after: nextIndex)
        }

        // 如果到达字符串结束，也认为是字符串结束
        return true
    }

    /// 检查指定位置是否是完整的关键字
    private func isKeywordAt(_ index: String.Index, keyword: String, in string: String) -> Bool {
        // 检查是否有足够的字符
        guard string.distance(from: index, to: string.endIndex) >= keyword.count else {
            return false
        }

        // 检查关键字匹配
        let endIndex = string.index(index, offsetBy: keyword.count)
        let substring = String(string[index..<endIndex])
        guard substring == keyword else {
            return false
        }

        // 检查前一个字符（如果存在）不是字母或数字
        if index > string.startIndex {
            let prevChar = string[string.index(before: index)]
            if prevChar.isLetter || prevChar.isNumber || prevChar == "_" {
                return false
            }
        }

        // 检查后一个字符（如果存在）不是字母或数字
        if endIndex < string.endIndex {
            let nextChar = string[endIndex]
            if nextChar.isLetter || nextChar.isNumber || nextChar == "_" {
                return false
            }
        }

        return true
    }
}

// MARK: - 文件分析结果解析器

class FileAnalysisResultParser {
    private let dictConverter = DictFormatConverter()
    private let jsonDecoder = JSONDecoder()

    func parseResult(_ content: String, currentResult: FileAnalysisResult) -> FileAnalysisResult? {
        let trimmedContent = content.trimmingCharacters(in: .whitespacesAndNewlines)
        guard !trimmedContent.isEmpty else { return nil }

        if let result = tryDirectJsonParsing(trimmedContent) {
            return mergeResults(current: currentResult, new: result)
        }

        if let result = tryPythonDictParsing(trimmedContent) {
            return mergeResults(current: currentResult, new: result)
        }

        if let result = tryPartialJsonParsing(trimmedContent) {
            return mergeResults(current: currentResult, new: result)
        }

        return nil
    }

    private func tryDirectJsonParsing(_ content: String) -> FileAnalysisResult? {
        guard let data = content.data(using: .utf8) else { return nil }

        do {
            return try jsonDecoder.decode(FileAnalysisResult.self, from: data)
        } catch {
            return nil
        }
    }

    private func tryPythonDictParsing(_ content: String) -> FileAnalysisResult? {
        let normalizedJson = dictConverter.convert(content)
        guard let data = normalizedJson.data(using: .utf8) else { return nil }

        do {
            return try jsonDecoder.decode(FileAnalysisResult.self, from: data)
        } catch {
            return nil
        }
    }

    private func tryPartialJsonParsing(_ content: String) -> FileAnalysisResult? {
        let strategies = [
            fixIncompleteJson,
            fixTruncatedString,
            fixMissingQuotes,
            fixExtraCommas,
        ]

        for strategy in strategies {
            if let fixedContent = strategy(content),
               let result = tryDirectJsonParsing(fixedContent) ?? tryPythonDictParsing(fixedContent)
            {
                return result
            }
        }

        return nil
    }

    private func fixIncompleteJson(_ content: String) -> String? {
        var fixedContent = content.trimmingCharacters(in: .whitespacesAndNewlines)

        // 移除逗号结尾
        if fixedContent.hasSuffix(",") {
            fixedContent = String(fixedContent.dropLast())
        }

        // 如果不以}结尾，尝试添加}
        if !fixedContent.hasSuffix("}") {
            fixedContent += "}"
        }

        // 如果不以{开头，尝试添加{
        if !fixedContent.hasPrefix("{") {
            fixedContent = "{" + fixedContent
        }

        return fixedContent
    }

    /// 修复截断的字符串
    private func fixTruncatedString(_ content: String) -> String? {
        var fixedContent = content

        // 检查是否有未闭合的字符串
        let quoteCount = fixedContent.count(where: { $0 == "\"" || $0 == "'" })
        if quoteCount % 2 != 0 {
            // 有未闭合的字符串，尝试闭合它
            if fixedContent.contains("\""), !fixedContent.hasSuffix("\"") {
                fixedContent += "\""
            } else if fixedContent.contains("'"), !fixedContent.hasSuffix("'") {
                fixedContent += "'"
            }
        }

        return fixIncompleteJson(fixedContent)
    }

    /// 修复缺失的引号
    private func fixMissingQuotes(_ content: String) -> String? {
        // 修复引号问题
        var fixedContent = content

        // 修复 key: value 格式中缺失的引号
        fixedContent = fixedContent.replacingOccurrences(
            of: "([a-zA-Z_][a-zA-Z0-9_]*):",
            with: "\"$1\":",
            options: .regularExpression
        )

        return fixIncompleteJson(fixedContent)
    }

    /// 修复多余的逗号
    private func fixExtraCommas(_ content: String) -> String? {
        var fixedContent = content

        // 移除 } 前的逗号
        fixedContent = fixedContent.replacingOccurrences(of: ",}", with: "}")
        // 移除 ] 前的逗号
        fixedContent = fixedContent.replacingOccurrences(of: ",]", with: "]")
        // 移除连续的逗号
        fixedContent = fixedContent.replacingOccurrences(of: ",,+", with: ",", options: .regularExpression)

        return fixIncompleteJson(fixedContent)
    }

    /// 合并分析结果
    private func mergeResults(current: FileAnalysisResult, new: FileAnalysisResult) -> FileAnalysisResult {
        return FileAnalysisResult(
            summary: mergeStringField(new: new.summary, current: current.summary),
            keyPoints: mergeStringField(new: new.keyPoints, current: current.keyPoints),
            outline: mergeStringField(new: new.outline, current: current.outline),
            fileUrl: mergeStringField(new: new.fileUrl, current: current.fileUrl),
            tags: mergeTagsField(new: new.tags, current: current.tags),
            language: new.language ?? current.language
        )
    }

    /// 合并字符串字段，优先选择有有效内容的字段
    private func mergeStringField(new: String?, current: String?) -> String? {
        if let newValue = new, !newValue.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
            return newValue
        }
        return current
    }

    /// 合并 tags 字段，优先选择有有效内容的数组
    private func mergeTagsField(new: [String]?, current: [String]?) -> [String]? {
        if let newTags = new, !newTags.isEmpty {
            let validNewTags = newTags.compactMap { tag in
                let trimmed = tag.trimmingCharacters(in: .whitespacesAndNewlines)
                return trimmed.isEmpty ? nil : trimmed
            }
            return validNewTags.isEmpty ? current : validNewTags
        }
        return current
    }
}
