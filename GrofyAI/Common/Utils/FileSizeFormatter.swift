import Foundation

// MARK: - 文件大小格式化工具

/// 统一的文件大小格式化工具类
/// 提供一致的文件大小格式化行为，避免重复代码
class FileSizeFormatter {
    static let shared = FileSizeFormatter()

    /// 预配置的ByteCountFormatter实例
    private let formatter: ByteCountFormatter

    private init() {
        formatter = ByteCountFormatter()
        formatter.allowedUnits = [.useKB, .useMB, .useGB]
        formatter.countStyle = .file
    }

    /// 格式化文件大小
    /// - Parameter bytes: 文件大小（字节）
    /// - Returns: 格式化后的文件大小字符串
    func formatFileSize(_ bytes: Int64) -> String {
        return formatter.string(fromByteCount: bytes)
    }

    /// 格式化文件大小
    /// - Parameter bytes: 文件大小（字节）
    /// - Returns: 格式化后的文件大小字符串
    func formatFileSize(_ bytes: Int) -> String {
        return formatter.string(fromByteCount: Int64(bytes))
    }

    /// 格式化可选的文件大小
    /// - Parameters:
    ///   - bytes: 可选的文件大小（字节）
    ///   - fallback: 当bytes为nil时的默认值
    /// - Returns: 格式化后的文件大小字符串
    func formatFileSize(_ bytes: Int?, fallback: String = "未知大小") -> String {
        guard let bytes else {
            return fallback
        }
        return formatFileSize(bytes)
    }
}

// MARK: - Optional<Int> Extensions

extension Int? {
    /// 格式化可选的文件大小
    /// - Parameter fallback: 当值为nil时的默认值
    /// - Returns: 格式化后的文件大小字符串
    func formattedFileSize(fallback: String = "未知大小") -> String {
        return FileSizeFormatter.shared.formatFileSize(self, fallback: fallback)
    }
}
