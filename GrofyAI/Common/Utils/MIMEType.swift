//
//  MIMEType.swift
//  GrofyAI
//
//  Created by kissy on 2025/6/19.
//

import Foundation
import UniformTypeIdentifiers // 👈 导入这个框架

func mimeType(for fileExtension: String) -> String {
    // 使用 UTType 从文件扩展名推断类型
    if let type = UTType(filenameExtension: fileExtension.lowercased()) {
        // 获取首选的 MIME 类型
        if let mimeType = type.preferredMIMEType {
            return mimeType
        }
    }
    // 如果无法识别，返回一个通用的二进制流类型
    return "application/octet-stream"
}
