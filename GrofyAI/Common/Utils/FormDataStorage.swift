//
//  FormDataStorage.swift
//  GrofyAI
//
//  Created by kissy on 2025/7/25.
//
import SwiftUI

struct FormDataStorage {
    
    // 使用一个固定的 Key 来存储数据
    private static let storageKey = "creationReq"
    private static let modelKey = "lastUsedModel"
    private static let modelTypeKey = "lastUsedModelType"
    
    //MARK: 请求参数本地缓存
    /// 保存请求参数到 UserDefaults
    /// - Parameter request: 任何遵循 Codable 协议的对象
    static func saveRequest<T: Encodable>(request: T) {
        let encoder = JSONEncoder()
        encoder.outputFormatting = .prettyPrinted // 可选，让 JSON 更易读
        
        do {
            let data = try encoder.encode(request)
            let jsonString = String(data: data, encoding: .utf8)
            UserDefaults.standard.set(jsonString, forKey: storageKey)
            print("✅ Form data saved successfully.")
        } catch {
            print("❌ Failed to encode and save form data: \(error.localizedDescription)")
        }
    }
    
    /// 从 UserDefaults 加载并解析请求参数
    /// - Parameter type: 你期望解析成的具体类型 (例如 FluxImageReq.self)
    /// - Returns: 一个可选的、已解析的对象
    static func loadRequest<T: Decodable>(as type: T.Type) -> T? {
        guard let jsonString = UserDefaults.standard.string(forKey: storageKey) else {
            print("ℹ️ No saved form data found for key '\(storageKey)'.")
            return nil
        }
        
        guard let data = jsonString.data(using: .utf8) else {
            print("❌ Failed to convert saved JSON string to data.")
            return nil
        }
        
        let decoder = JSONDecoder()
        
        do {
            let request = try decoder.decode(type, from: data)
            print("✅ Form data loaded and parsed successfully.")
            return request
        } catch {
            // 如果存储的数据类型和期望的类型不匹配（例如上次存的是OpenAI，这次想加载Flux），解析会失败。
            // 这种情况是正常的，直接返回 nil 即可。
            print("⚠️ Failed to parse saved data as \(type): \(error.localizedDescription). This can happen if the form type has changed.")
            return nil
        }
    }
    
    /// 清除存储的表单数据
    static func clearRequest() {
        UserDefaults.standard.removeObject(forKey: storageKey)
        print("🗑️ Saved form data has been cleared.")
    }
    
    
    //MARK: 请求模型
    static func saveModel<T: RawRepresentable>(model: T) where T.RawValue == String {
        UserDefaults.standard.set(model.rawValue, forKey: modelKey)
    }
    
    static func loadModel<T: RawRepresentable>(as type: T.Type) -> T? where T.RawValue == String {
        guard let rawValue = UserDefaults.standard.string(forKey: modelKey) else {
            return nil
        }
        // 现在编译器知道 T 有 init?(rawValue:)，因为 T 遵循 RawRepresentable
        return T(rawValue: rawValue)
    }
    
    static func clearModel() {
        UserDefaults.standard.removeObject(forKey: modelKey)
        print("🗑️ Saved form data has been cleared.")
    }
    
    
    //MARK: 请求模型类类型
    static func saveModelType<T: RawRepresentable>(modelType: T) where T.RawValue == String {
        UserDefaults.standard.set(modelType.rawValue, forKey: modelTypeKey)
    }
    
    static func loadModelType<T: RawRepresentable>(as type: T.Type) -> T? where T.RawValue == String {
        guard let rawValue = UserDefaults.standard.string(forKey: modelTypeKey) else {
            return nil
        }
        // 现在编译器知道 T 有 init?(rawValue:)，因为 T 遵循 RawRepresentable
        return T(rawValue: rawValue)
    }
    
    static func clearModelType() {
        UserDefaults.standard.removeObject(forKey: modelTypeKey)
        print("🗑️ Saved form data has been cleared.")
    }
    
}
