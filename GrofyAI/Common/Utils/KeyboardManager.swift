import Combine
import SwiftUI

// MARK: - 键盘状态管理器

class KeyboardManager: ObservableObject {
    @Published var height: CGFloat = 0
    @Published var isVisible = false
    @Published var animationDuration = 0.25

    private var cancellables = Set<AnyCancellable>()

    init() {
        setupKeyboardNotifications()
    }

    deinit {
        cancellables.forEach { $0.cancel() }
    }

    private func setupKeyboardNotifications() {
        NotificationCenter.default.publisher(for: UIResponder.keyboardWillShowNotification)
            .sink { [weak self] notification in
                self?.handleKeyboardShow(notification)
            }
            .store(in: &cancellables)

        NotificationCenter.default.publisher(for: UIResponder.keyboardWillHideNotification)
            .sink { [weak self] notification in
                self?.handleKeyboardHide(notification)
            }
            .store(in: &cancellables)
    }

    private func handleKeyboardShow(_ notification: Notification) {
        guard let frame = notification.userInfo?[UIResponder.keyboardFrameEndUserInfoKey] as? CGRect,
              let duration = notification.userInfo?[UIResponder.keyboardAnimationDurationUserInfoKey] as? Double
        else {
            return
        }

        DispatchQueue.main.async { [weak self] in
            self?.height = frame.height
            self?.isVisible = true
            self?.animationDuration = duration
        }
    }

    private func handleKeyboardHide(_ notification: Notification) {
        guard let duration = notification.userInfo?[UIResponder.keyboardAnimationDurationUserInfoKey] as? Double else {
            return
        }

        DispatchQueue.main.async { [weak self] in
            self?.height = 0
            self?.isVisible = false
            self?.animationDuration = duration
        }
    }
}

// MARK: - 键盘处理视图修饰器

struct KeyboardAwareModifier: ViewModifier {
    @StateObject private var keyboardManager = KeyboardManager()

    func body(content: Content) -> some View {
        content
            .environmentObject(keyboardManager)
            .animation(.easeOut(duration: keyboardManager.animationDuration), value: keyboardManager.height)
            .animation(.easeOut(duration: keyboardManager.animationDuration), value: keyboardManager.isVisible)
    }
}
