import SwiftUI

// MARK: - 统一动画配置

struct AnimationConfig {
    static let shared = AnimationConfig()
    
    private init() {}
    
    // 输入框动画配置
    static let inputTransition: Animation = .spring(response: 0.4, dampingFraction: 0.8)
    static let keyboardResponse: Animation = .easeOut(duration: 0.25)
    static let layoutTransition: Animation = .easeInOut(duration: 0.3)
    
    // 通用动画配置
    static let fast: Animation = .easeInOut(duration: 0.15)
    static let standard: Animation = .easeInOut(duration: 0.25)
    static let slow: Animation = .easeInOut(duration: 0.4)
    
    // Spring 动画配置
    static let bouncy: Animation = .spring(response: 0.3, dampingFraction: 0.6)
    static let smooth: Animation = .spring(response: 0.4, dampingFraction: 0.8)
    static let gentle: Animation = .spring(response: 0.6, dampingFraction: 0.9)
}

// MARK: - 布局缓存系统

struct LayoutCache {
    var compactSize: CGSize = .zero
    var expandedSize: CGSize = .zero
    var lastUpdate: Date = Date()
    private let cacheTimeout: TimeInterval = 0.1
    
    mutating func updateIfNeeded(compact: CGSize, expanded: CGSize) {
        if abs(lastUpdate.timeIntervalSinceNow) > cacheTimeout {
            compactSize = compact
            expandedSize = expanded
            lastUpdate = Date()
        }
    }
    
    var isValid: Bool {
        abs(lastUpdate.timeIntervalSinceNow) <= cacheTimeout
    }
    
    mutating func invalidate() {
        lastUpdate = Date.distantPast
    }
}

// MARK: - 性能监控工具

class PerformanceMonitor: ObservableObject {
    @Published var layoutCount: Int = 0
    @Published var animationCount: Int = 0
    
    private let resetInterval: TimeInterval = 1.0
    private var lastReset: Date = Date()
    
    func recordLayout() {
        checkReset()
        layoutCount += 1
    }
    
    func recordAnimation() {
        checkReset()
        animationCount += 1
    }
    
    private func checkReset() {
        if abs(lastReset.timeIntervalSinceNow) > resetInterval {
            layoutCount = 0
            animationCount = 0
            lastReset = Date()
        }
    }
}

// MARK: - 布局状态管理

enum InputLayoutState: CaseIterable {
    case compact    // 紧凑状态
    case expanding  // 展开中
    case expanded   // 已展开
    case collapsing // 收起中
    
    var isTransitioning: Bool {
        self == .expanding || self == .collapsing
    }
    
    var isExpanded: Bool {
        self == .expanded || self == .expanding
    }
}