import Foundation

class DateUtils {
    static func formatDateWithWeekday(_ date: Date = Date()) -> String {
        // 获取时区信息
        let timeZone = TimeZone.current
        let identifier = timeZone.identifier
        let seconds = timeZone.secondsFromGMT()
        let hours = seconds / 3600
        let minutes = abs(seconds / 60) % 60
        let sign = seconds >= 0 ? "+" : "-"

        let gmtOffset = String(format: "GMT%@%02d:%02d", sign, abs(hours), minutes)

        // 格式化日期和星期
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd HH:mm:ss EEEE"
        formatter.timeZone = timeZone
        formatter.locale = Locale(identifier: "en_US")
        let dateString = formatter.string(from: date)

        // 格式：Asia/Shanghai GMT+08:00 20xx-xx-xx xx:xx:xx Tuesday
        return "\(identifier) \(gmtOffset) \(dateString)"
    }
    
    /// 返回只包含时分秒的时间字符串
    /// - Parameter date: 日期对象，默认为当前时间
    /// - Returns: 格式化的时分秒字符串 (HH:mm:ss)
    static func formatTimeOnly(_ date: Date = Date()) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm:ss"
        formatter.timeZone = TimeZone.current
        return formatter.string(from: date)
    }

    /// 解析API返回的日期字符串
    /// - Parameter dateString: API返回的日期字符串
    /// - Returns: 解析后的Date对象，如果解析失败返回nil
    static func parseAPIDate(_ dateString: String) -> Date? {
        let formatters = [
            // 标准ISO8601格式：2024-12-26T08:49:20
            "yyyy-MM-dd'T'HH:mm:ss",
            // 带毫秒的格式：2024-12-26T08:49:20.123
            "yyyy-MM-dd'T'HH:mm:ss.SSS",
            // 带时区的格式：2024-12-26T08:49:20Z
            "yyyy-MM-dd'T'HH:mm:ssZ",
            // 带毫秒和时区的格式：2024-12-26T08:49:20.123Z
            "yyyy-MM-dd'T'HH:mm:ss.SSSZ",
            // 带微秒的格式：2024-12-26T08:49:20.123456
            "yyyy-MM-dd'T'HH:mm:ss.SSSSSS"
        ]

        for formatString in formatters {
            let formatter = DateFormatter()
            formatter.dateFormat = formatString
            formatter.timeZone = TimeZone.current

            if let date = formatter.date(from: dateString) {
                return date
            }
        }

        return nil
    }

    /// 格式化日期为友好的显示格式（今天/昨天/MM月dd日）
    /// - Parameter date: 要格式化的日期
    /// - Returns: 友好的日期显示字符串
    static func formatFriendlyDate(_ date: Date) -> String {
        if Calendar.current.isDateInToday(date) {
            return "今天"
        } else if Calendar.current.isDateInYesterday(date) {
            return "昨天"
        } else {
            let formatter = DateFormatter()
            formatter.dateFormat = "MM月dd日"
            return formatter.string(from: date)
        }
    }

    /// 格式化日期为简短的日期时间格式（MM-dd HH:mm）
    /// - Parameter date: 要格式化的日期
    /// - Returns: 简短的日期时间字符串
    static func formatShortDateTime(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "MM-dd HH:mm"
        return formatter.string(from: date)
    }
}
