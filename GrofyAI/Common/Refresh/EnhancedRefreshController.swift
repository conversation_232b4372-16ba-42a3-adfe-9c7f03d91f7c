import Combine
import SwiftUI

/// 下拉刷新控制器，完全符合iOS HIG标准
/// 完全替代SwiftUI的refreshable，提供精确的手势控制和状态管理
@MainActor
class EnhancedRefreshController: ObservableObject {
    // MARK: - 刷新状态枚举

    /// 刷新状态
    enum RefreshState: Equatable {
        case idle // 空闲状态
        case pulling // 用户正在拖拽
        case readyToRefresh // 达到刷新阈值，准备刷新
        case refreshing // 正在刷新
        case completed // 刷新完成
        case failed(String) // 刷新失败
    }

    @Published var refreshState: RefreshState = .idle
    @Published var pullProgress: CGFloat = 0.0 // 拖拽进度 (0.0 - 1.0)

    /// 触发刷新的最小拖拽距离
    private let refreshThreshold: CGFloat = 60.0

    /// 防抖间隔（秒）
    private let debounceInterval: TimeInterval = 0.5

    /// 最小刷新显示时间（避免闪烁）
    private let minimumRefreshDuration: TimeInterval = 0.8

    // MARK: - Private Properties

    private var lastRefreshTime: Date = .distantPast
    private var refreshStartTime: Date?
    private var currentRefreshTask: Task<Void, Never>?

    // MARK: - Computed Properties

    /// 是否正在刷新
    var isRefreshing: Bool {
        refreshState == .refreshing
    }

    /// 是否可以开始新的刷新
    var canStartRefresh: Bool {
        switch refreshState {
        case .completed, .failed, .idle:
            return Date().timeIntervalSince(lastRefreshTime) >= debounceInterval
        case .pulling, .readyToRefresh, .refreshing:
            return false
        }
    }

    /// 刷新进度文本
    var progressText: String {
        switch refreshState {
        case .idle:
            return ""
        case .pulling:
            return pullProgress >= 1.0 ? "松开即可刷新" : "下拉刷新"
        case .readyToRefresh:
            return "松开即可刷新"
        case .refreshing:
            return "正在刷新..."
        case .completed:
            return "刷新完成"
        case .failed(let message):
            return "刷新失败: \(message)"
        }
    }

    // MARK: - Public Methods

    /// 更新拖拽进度
    /// - Parameter progress: 拖拽进度 (0.0 - 1.0)
    func updatePullProgress(_ progress: CGFloat) {
        let clampedProgress = max(0.0, min(1.0, progress))
        pullProgress = clampedProgress

        switch refreshState {
        case .idle:
            if clampedProgress > 0 {
                refreshState = .pulling
            }
        case .pulling:
            if clampedProgress >= 1.0 {
                refreshState = .readyToRefresh
            } else if clampedProgress <= 0 {
                refreshState = .idle
            }
        case .readyToRefresh:
            if clampedProgress < 1.0 {
                refreshState = .pulling
            }
        default:
            break
        }
    }

    /// 用户松开手势，检查是否应该触发刷新
    func handlePullRelease() {
        switch refreshState {
        case .readyToRefresh:
            // 只有在达到阈值时才触发刷新
            triggerRefresh()
        case .pulling:
            // 未达到阈值，回到空闲状态
            resetToIdle()
        default:
            break
        }
    }

    /// 执行刷新操作（简化版本）
    /// - Parameter refreshAction: 异步刷新操作
    func performRefresh(_ refreshAction: @escaping () async throws -> Void) {
        // 防抖检查
        guard canStartRefresh else {
            return
        }

        // 取消之前的刷新任务
        currentRefreshTask?.cancel()

        refreshState = .refreshing
        refreshStartTime = Date()
        lastRefreshTime = Date()

        currentRefreshTask = Task { [weak self] in
            guard let self else { return }

            do {
                // 执行刷新操作
                try await refreshAction()

                // 确保最小显示时间
                await ensureMinimumRefreshDuration()

                await MainActor.run { [weak self] in
                    guard let self else { return }
                    refreshState = .completed

                    // 短暂延迟后回到空闲
                    Task { [weak self] in
                        try? await Task.sleep(nanoseconds: 300_000_000) // 0.3秒
                        await MainActor.run { [weak self] in
                            self?.resetToIdle()
                        }
                    }
                }

            } catch {
                await MainActor.run { [weak self] in
                    guard let self else { return }

                    let errorMessage = if let businessError = error as? BusinessError {
                        businessError.message
                    } else {
                        error.localizedDescription
                    }

                    refreshState = .failed(errorMessage)

                    // 显示错误状态后回到空闲
                    Task { [weak self] in
                        try? await Task.sleep(nanoseconds: 1_500_000_000) // 1.5秒
                        await MainActor.run { [weak self] in
                            self?.resetToIdle()
                        }
                    }
                }
            }
        }
    }

    /// 重置到空闲状态
    func resetToIdle() {
        refreshState = .idle
        pullProgress = 0.0
        refreshStartTime = nil
    }

    /// 取消当前刷新
    func cancelRefresh() {
        currentRefreshTask?.cancel()
        currentRefreshTask = nil
        resetToIdle()
    }

    // MARK: - Private Methods

    private func triggerRefresh() {
        refreshState = .refreshing
        refreshStartTime = Date()
        lastRefreshTime = Date()
    }

    /// 确保最小刷新显示时间
    private func ensureMinimumRefreshDuration() async {
        guard let startTime = refreshStartTime else { return }

        let elapsed = Date().timeIntervalSince(startTime)
        let remaining = minimumRefreshDuration - elapsed

        if remaining > 0 {
            try? await Task.sleep(nanoseconds: UInt64(remaining * 1_000_000_000))
        }
    }

    deinit {
        currentRefreshTask?.cancel()
    }
}

// MARK: - SwiftUI Integration

extension EnhancedRefreshController {
    /// 创建真正的自定义下拉刷新修饰符
    /// - Parameter refreshAction: 刷新操作
    /// - Returns: 修饰符函数
    func customRefreshable(_ refreshAction: @escaping () async throws -> Void) -> some ViewModifier {
        CustomRefreshableModifier(controller: self, refreshAction: refreshAction)
    }
}

/// 真正的自定义下拉刷新修饰符，完全替代SwiftUI的refreshable
private struct CustomRefreshableModifier: ViewModifier {
    let controller: EnhancedRefreshController
    let refreshAction: () async throws -> Void

    @State private var dragOffset: CGFloat = 0
    @State private var isDragging = false

    private let refreshThreshold: CGFloat = 80.0

    func body(content: Content) -> some View {
        VStack(spacing: 0) {
            if controller.refreshState != .idle || isDragging {
                RefreshIndicatorView(
                    controller: controller,
                    offset: dragOffset
                )
                .frame(height: max(0, dragOffset))
                .clipped()
            }

            content
                .offset(y: max(0, dragOffset))
        }
        .gesture(
            DragGesture(coordinateSpace: .global)
                .onChanged { value in
                    handleDragChanged(value)
                }
                .onEnded { value in
                    handleDragEnded(value)
                }
        )
    }

    private func handleDragChanged(_ value: DragGesture.Value) {
        // 只处理向下拖拽
        guard value.translation.height > 0 else { return }

        isDragging = true
        // 使用弹性阻尼效果
        dragOffset = elasticPullDistance(value.translation.height)

        let progress = min(dragOffset / refreshThreshold, 1.0)
        controller.updatePullProgress(progress)
    }

    private func handleDragEnded(_ value: DragGesture.Value) {
        isDragging = false

        // 检查是否达到刷新阈值
        if dragOffset >= refreshThreshold {
            // 触发刷新
            controller.handlePullRelease()

            controller.performRefresh(refreshAction)

            // 动画到刷新位置
            withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                dragOffset = refreshThreshold * 0.6
            }

            // 监听刷新完成
            Task {
                await waitForRefreshCompletion()
                await MainActor.run {
                    withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                        dragOffset = 0
                    }
                }
            }
        } else {
            // 未达到阈值，回弹
            controller.resetToIdle()
            withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                dragOffset = 0
            }
        }
    }

    /// 弹性拉伸距离计算，模拟iOS原生的阻尼效果
    private func elasticPullDistance(_ translation: CGFloat) -> CGFloat {
        let resistance: CGFloat = 0.4
        let maxDistance: CGFloat = refreshThreshold * 1.5

        if translation <= maxDistance {
            return translation * resistance
        } else {
            // 超过最大距离后，增加更强的阻尼
            let excess = translation - maxDistance
            return maxDistance * resistance + excess * 0.1
        }
    }

    private func waitForRefreshCompletion() async {
        while controller.refreshState == .refreshing {
            try? await Task.sleep(nanoseconds: 100_000_000) // 0.1秒
        }
    }
}

/// 刷新指示器视图 - 优化的iOS原生风格设计
private struct RefreshIndicatorView: View {
    let controller: EnhancedRefreshController
    let offset: CGFloat

    var body: some View {
        VStack(spacing: 8) {
            Spacer()

            HStack(spacing: 10) {
                // 状态图标 - 使用DesignSystem颜色
                Group {
                    switch controller.refreshState {
                    case .idle, .pulling, .readyToRefresh:
                        Image(systemName: "arrow.down")
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                            .rotationEffect(.degrees(controller.pullProgress >= 1.0 ? 180 : 0))
                            .animation(.spring(response: 0.3, dampingFraction: 0.8), value: controller.pullProgress)
                    case .refreshing:
                        ProgressView()
                            .scaleEffect(0.8)
                            .progressViewStyle(CircularProgressViewStyle(tint: DesignSystem.Colors.primary))
                    case .completed:
                        Image(systemName: "checkmark")
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(DesignSystem.Colors.success)
                    case .failed:
                        Image(systemName: "exclamationmark.triangle")
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(DesignSystem.Colors.error)
                    }
                }

                // 状态文本 - 使用DesignSystem字体和颜色
                Text(controller.progressText)
                    .font(DesignSystem.Typography.caption)
                    .foregroundColor(DesignSystem.Colors.textSecondary)
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 8)
            .background(
                RoundedRectangle(cornerRadius: DesignSystem.Rounded.lg)
                    .fill(DesignSystem.Colors.backgroundCard)
                    .shadow(color: .black.opacity(0.15), radius: 8, x: 0, y: 4)
            )
            .opacity(indicatorOpacity)
            .scaleEffect(indicatorScale)

            Spacer()
        }
        .frame(maxWidth: .infinity)
        .background(DesignSystem.Colors.backgroundPage)
    }

    private var indicatorOpacity: Double {
        if controller.refreshState == .refreshing {
            return 1.0
        } else {
            return min(offset / 30.0, 1.0) // 渐进显示
        }
    }

    private var indicatorScale: CGFloat {
        if controller.refreshState == .refreshing {
            return 1.0
        } else {
            return 0.8 + min(offset / 150.0, 0.2) // 从0.8缩放到1.0
        }
    }
}
