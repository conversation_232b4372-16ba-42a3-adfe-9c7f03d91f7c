import SwiftUI

/// 原生iOS风格的下拉刷新实现
/// 使用覆盖式指示器，不占用布局空间，符合iOS HIG标准
struct NativeStyleRefresh<Content: View>: View {
    let content: Content
    let onRefresh: () async throws -> Void

    @State private var pullDistance: CGFloat = 0
    @State private var isRefreshing = false
    @State private var isDragging = false
    @State private var currentRefreshTask: Task<Void, Never>?

    private let refreshThreshold: CGFloat = 60.0
    private let maxPullDistance: CGFloat = 100.0

    init(
        onRefresh: @escaping () async throws -> Void,
        @ViewBuilder content: () -> Content
    ) {
        self.onRefresh = onRefresh
        self.content = content()
    }

    var body: some View {
        ZStack(alignment: .top) {
            content
                .simultaneousGesture(
                    DragGesture(coordinateSpace: .global)
                        .onChanged { value in
                            handleDragChanged(value)
                        }
                        .onEnded { value in
                            handleDragEnded(value)
                        }
                )

            // 覆盖式刷新指示器 - 锚定在顶部
            if pullDistance > 0 || isRefreshing {
                refreshIndicatorOverlay
                    .transition(.asymmetric(
                        insertion: .opacity.combined(with: .scale(scale: 0.8)),
                        removal: .opacity.combined(with: .scale(scale: 0.8))
                    ))
            }
        }
        .onDisappear {
            currentRefreshTask?.cancel()
            currentRefreshTask = nil
        }
    }

    @ViewBuilder
    private var refreshIndicatorOverlay: some View {
        VStack(spacing: 8) {
            HStack(spacing: 10) {
                if isRefreshing {
                    ProgressView()
                        .scaleEffect(0.8)
                        .progressViewStyle(CircularProgressViewStyle(tint: DesignSystem.Colors.primary))
                } else {
                    Image(systemName: "arrow.down")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                        .rotationEffect(.degrees(pullDistance >= refreshThreshold ? 180 : 0))
                        .animation(
                            .spring(response: 0.3, dampingFraction: 0.8),
                            value: pullDistance >= refreshThreshold
                        )
                }

                Text(statusText)
                    .font(DesignSystem.Typography.caption)
                    .foregroundColor(DesignSystem.Colors.textSecondary)
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 8)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(.ultraThinMaterial)
                    .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
            )
        }
        .padding(.top, 8)
        .opacity(indicatorOpacity)
        .scaleEffect(indicatorScale)
        .animation(.spring(response: 0.4, dampingFraction: 0.8), value: pullDistance)
        .animation(.spring(response: 0.4, dampingFraction: 0.8), value: isRefreshing)
    }

    private var statusText: String {
        if isRefreshing {
            return "正在刷新..."
        } else if pullDistance >= refreshThreshold {
            return "松开即可刷新"
        } else {
            return "下拉刷新"
        }
    }

    private var indicatorOpacity: Double {
        if isRefreshing {
            return 1.0
        } else {
            return min(pullDistance / 30.0, 1.0) // 渐进显示
        }
    }

    private var indicatorScale: CGFloat {
        if isRefreshing {
            return 1.0
        } else {
            return 0.8 + min(pullDistance / 150.0, 0.2) // 从0.8缩放到1.0
        }
    }

    private func handleDragChanged(_ value: DragGesture.Value) {
        let verticalTranslation = value.translation.height
        let horizontalTranslation = value.translation.width

        // 检查是否为有效的下拉手势
        let isDownwardDrag = verticalTranslation > 0
        let isMainlyVertical = abs(verticalTranslation) > abs(horizontalTranslation)
        let hasMinimumDistance = verticalTranslation > 5

        guard isDownwardDrag, isMainlyVertical, hasMinimumDistance, !isRefreshing else {
            return
        }

        isDragging = true

        // 使用弹性阻尼函数，模拟iOS原生的拉伸效果
        pullDistance = elasticPullDistance(verticalTranslation)
    }

    private func handleDragEnded(_ value: DragGesture.Value) {
        isDragging = false

        if pullDistance >= refreshThreshold, !isRefreshing {
            triggerRefresh()
        } else {
            // 使用弹性动画回到原位
            withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                pullDistance = 0
            }
        }
    }

    /// 弹性拉伸距离计算，模拟iOS原生的阻尼效果
    private func elasticPullDistance(_ translation: CGFloat) -> CGFloat {
        let resistance: CGFloat = 0.4
        let maxDistance = maxPullDistance

        if translation <= maxDistance {
            return translation * resistance
        } else {
            // 超过最大距离后，增加更强的阻尼
            let excess = translation - maxDistance
            return maxDistance * resistance + excess * 0.1
        }
    }

    private func triggerRefresh() {
        currentRefreshTask?.cancel()

        isRefreshing = true

        withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
            pullDistance = refreshThreshold * 0.6
        }

        currentRefreshTask = Task {
            let refreshStartTime = Date()

            do {
                try await onRefresh()

                // 计算刷新操作实际耗时
                let elapsedTime = Date().timeIntervalSince(refreshStartTime)
                let minimumDisplayTime: TimeInterval = 0.6

                // 如果刷新操作很快，等待剩余时间以确保良好的用户体验
                if elapsedTime < minimumDisplayTime {
                    let remainingTime = minimumDisplayTime - elapsedTime
                    try await Task.sleep(nanoseconds: UInt64(remainingTime * 1_000_000_000))
                }

                await MainActor.run {
                    withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                        isRefreshing = false
                        pullDistance = 0
                    }
                    currentRefreshTask = nil
                }
            } catch {
                await MainActor.run {
                    withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                        isRefreshing = false
                        pullDistance = 0
                    }
                    currentRefreshTask = nil
                }
            }
        }
    }
}
