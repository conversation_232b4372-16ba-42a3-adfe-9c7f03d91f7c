import Foundation
import GoogleSignIn
import SDWebImageWebPCoder
import UIKit

// MARK: - App Delegate

/// 应用程序委托类
/// 负责应用程序生命周期管理和全局配置
class AppDelegate: NSObject, UIApplicationDelegate {
    func application(
        _ application: UIApplication,
        didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]? = nil
    ) -> Bool {
        setupWebPDecoder()
        setupGoogleSignIn()
        return true
    }

    // MARK: - Scene Configuration for Mijick Popups

    /// 配置Scene会话，用于支持Mijick Popups的SceneDelegate方法
    /// 这是解决Popup与原生SwiftUI元素冲突的关键配置
    func application(
        _ application: UIApplication,
        configurationForConnecting connectingSceneSession: UISceneSession,
        options: UIScene.ConnectionOptions
    ) -> UISceneConfiguration {
        let sceneConfig = UISceneConfiguration(name: nil, sessionRole: connectingSceneSession.role)
        sceneConfig.delegateClass = CustomPopupSceneDelegate.self
        return sceneConfig
    }

    // MARK: - Private Setup Methods

    /// 设置WebP解码器
    /// 用于支持WebP图像格式的显示
    private func setupWebPDecoder() {
        let webPCoder = SDImageWebPCoder.shared
        SDImageCodersManager.shared.addCoder(webPCoder)
    }

    /// 设置Google登录
    /// 配置Google Sign In SDK
    private func setupGoogleSignIn() {
        let clientId = SocialAuthConfig.googleClientID

        // 创建Google Sign In配置
        let config = GIDConfiguration(clientID: clientId)
        GIDSignIn.sharedInstance.configuration = config
    }

    /// 处理URL回调
    /// 用于Google登录的URL处理
    func application(
        _ app: UIApplication,
        open url: URL,
        options: [UIApplication.OpenURLOptionsKey: Any] = [:]
    ) -> Bool {
        return GIDSignIn.sharedInstance.handle(url)
    }
}
