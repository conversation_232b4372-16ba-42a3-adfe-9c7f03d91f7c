import Foundation
import MijickPopups
import SwiftUI

// MARK: - Custom Popup Scene Delegate

/// 自定义弹窗场景委托类
/// 继承自Mijick Popups的PopupSceneDelegate，用于解决popup与原生SwiftUI元素的冲突
///
/// 技术原理：
/// - SceneDelegate方法在UIKit层面管理popup，具有比SwiftUI视图层级更高的渲染优先级
/// - 可以覆盖原生SwiftUI元素如fullScreenCover、sheets、alerts等
/// - 避免了在SwiftUI视图层级中使用registerPopups()方法时的层级冲突问题
class CustomPopupSceneDelegate: PopupSceneDelegate {
    override init() {
        super.init()
        setupPopupConfiguration()
    }

    // MARK: - Private Setup Methods

    /// 配置弹窗的默认样式和行为
    /// 这些配置会应用到所有使用此SceneDelegate的popup
    private func setupPopupConfiguration() {
        configBuilder = { config in
            config
                .vertical { verticalConfig in
                    verticalConfig
                        .tapOutsideToDismissPopup(true)
                        .cornerRadius(16)
                        .popupBottomPadding(0)
                        .enableDragGesture(false)
                        .backgroundColor(Color("ColorBackgroundCard"))
                }
                .center { centerConfig in
                    centerConfig
                        .tapOutsideToDismissPopup(false)
                        .backgroundColor(.clear)
                }
        }
    }
}

// MARK: - Configuration Documentation

// 弹窗配置说明：
//
// Vertical Popup (底部弹窗):
// - tapOutsideToDismissPopup(true): 点击外部区域可关闭弹窗
// - cornerRadius(16): 圆角半径16pt，符合iOS设计规范
// - popupBottomPadding(0): 底部无额外内边距，贴合屏幕底部
// - enableDragGesture(false): 禁用拖拽手势，避免意外关闭
// - backgroundColor: 使用项目DesignSystem中的卡片背景色
//
// Center Popup (居中弹窗):
// - tapOutsideToDismissPopup(false): 点击外部区域不关闭，需要明确操作
// - backgroundColor(.clear): 透明背景，突出弹窗内容
