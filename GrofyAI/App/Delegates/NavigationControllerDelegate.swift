import ObjectiveC
import SwiftUI

class NavigationControllerDelegate: NSObject, UIGestureRecognizerDelegate {
    private weak var navigationController: UINavigationController?

    init(navigationController: UINavigationController) {
        self.navigationController = navigationController
        super.init()
    }

    func gestureRecognizerShouldBegin(_ gestureRecognizer: UIGestureRecognizer) -> <PERSON><PERSON> {
        guard let navigationController else {
            return false
        }
        return navigationController.viewControllers.count > 1
    }
}

extension UINavigationController {
    private enum AssociatedKeys {
        static var delegateKey: UInt8 = 0
    }

    private var gestureDelegate: NavigationControllerDelegate {
        guard let delegate = objc_getAssociatedObject(
            self,
            &AssociatedKeys.delegateKey
        ) as? NavigationControllerDelegate else {
            let delegate = NavigationControllerDelegate(navigationController: self)
            objc_setAssociatedObject(self, &AssociatedKeys.delegateKey, delegate, .OBJC_ASSOCIATION_RETAIN_NONATOMIC)
            return delegate
        }
        return delegate
    }

    open override func viewDidLoad() {
        super.viewDidLoad()
        interactivePopGestureRecognizer?.delegate = gestureDelegate
    }
}
