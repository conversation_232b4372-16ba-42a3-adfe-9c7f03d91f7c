import MijickPopups
import SwiftUI

@main
struct grofyaiApp: App {
    @StateObject private var keyboardManager = KeyboardManager()
    @StateObject private var authController = AuthenticationController()
    @StateObject private var imageRecognitionChatController = ImageRecognitionChatController()
    @StateObject private var globalAuthManager = GlobalAuthManager()
    @StateObject private var ttsService = TextToSpeechService()
    @StateObject private var sttService = SpeechRecognitionService()

    init() {
        Task.detached(priority: .utility) {
            NetworkMonitor.shared.startMonitoring()
        }

        Task.detached(priority: .background) {
            await InitControl.shared.performDelayedInitialization()
        }

        InitControl.shared.performCriticalSyncSetup()
    }

    @AppStorage("colorScheme") private var colorSchemeOption: ColorSchemeOption = .system
    @UIApplicationDelegateAdaptor(AppDelegate.self) var appDelegate

    var body: some Scene {
        WindowGroup {
            ContentView()
                .environmentObject(imageRecognitionChatController)
                .environmentObject(AuthStore.shared)
                .environmentObject(keyboardManager)
                .environmentObject(authController)
                .environmentObject(globalAuthManager)
                .environmentObject(ttsService)
                .environmentObject(sttService)
                ._zeroPaddingForAllHeadings()
                .preferredColorScheme(colorSchemeOption.override)
                .task {
                    await ttsService.warmup()
                }
        }
    }
}
