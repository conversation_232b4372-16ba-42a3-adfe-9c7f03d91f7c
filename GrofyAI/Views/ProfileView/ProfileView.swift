import FlowStacks
import SwiftUI

// MARK: - 个人中心主界面

struct ProfileView: View {
    @EnvironmentObject var navigator: FlowPathNavigator
    @Environment(\.colorScheme) private var colorScheme

    var body: some View {
        VStack(spacing: 10) {
            ProfileNavigationBarView()

            UserInfoCard()

            MyWorks()
                .frame(maxWidth: .infinity, maxHeight: .infinity)
        }
        .padding(.horizontal)
        .background(backgroundView)
        .navigationBarHidden(true)
    }

    @ViewBuilder
    private var backgroundView: some View {
        Group {
            if colorScheme == .light {
                Image("ProfileBackground")
                    .resizable()
                    .scaledToFill()
            } else {
                DesignSystem.Colors.backgroundPage
            }
        }
        .ignoresSafeArea()
    }
}

struct ProfileNavigationBarView: View {
    @EnvironmentObject var navigator: FlowPathNavigator
    var body: some View {
        HStack {
            Spacer()
            Image("IconSettings")
                .renderingMode(.template)
                .foregroundColor(DesignSystem.Colors.textPrimary)
                .onTapGesture {
                    navigator.push(Route.settings)
                }
        }
        .frame(height: 30)
        .frame(maxWidth: .infinity)
    }
}
