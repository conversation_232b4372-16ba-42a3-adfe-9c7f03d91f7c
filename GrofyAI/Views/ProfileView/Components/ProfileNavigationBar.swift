import SwiftUI

// MARK: - 个人中心导航栏组件

struct ProfileNavigationBar: View {
    let title: String
    let showSettingsButton: Bool
    let onSettingsTap: (() -> Void)?

    init(
        title: String = "我的",
        showSettingsButton: Bool = true,
        onSettingsTap: (() -> Void)? = nil
    ) {
        self.title = title
        self.showSettingsButton = showSettingsButton
        self.onSettingsTap = onSettingsTap
    }

    var body: some View {
        HStack {
            Text(title)
                .font(DesignSystem.Typography.titleMedium)
                .foregroundColor(DesignSystem.Colors.textPrimary)
                .fontWeight(.bold)

            Spacer()

            if showSettingsButton {
                Button(action: {
                    onSettingsTap?()
                }) {
                    Image(systemName: "gearshape")
                        .font(.system(size: 20, weight: .medium))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                }
                .accessibilityLabel("设置")
            }
        }
        .padding(.horizontal, DesignSystem.Spacing.lg)
        .padding(.vertical, DesignSystem.Spacing.sm)
        .background(DesignSystem.Colors.backgroundPage)
    }
}
