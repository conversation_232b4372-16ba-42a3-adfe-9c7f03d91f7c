import SwiftUI

struct ProfileMenuList: View {
    private let menuSections: [MenuSection] = [
        MenuSection(
            title: "我的内容",
            items: [
                MenuItem(
                    id: "myWorks",
                    title: "我的作品",
                    subtitle: "查看我创作的所有作品",
                    iconName: "photo.on.rectangle.angled",
                    iconColor: DesignSystem.Colors.primary,
                    badgeCount: 12
                ),
                MenuItem(
                    id: "myFavorites",
                    title: "我的收藏",
                    subtitle: "查看收藏的内容",
                    iconName: "heart.fill",
                    iconColor: DesignSystem.Colors.error,
                    badgeCount: 8
                ),
            ]
        ),
        MenuSection(
            title: "消息中心",
            items: [
                MenuItem(
                    id: "messages",
                    title: "消息中心",
                    subtitle: "查看系统消息和通知",
                    iconName: "bell.fill",
                    iconColor: DesignSystem.Colors.warning,
                    badgeCount: 3
                ),
            ]
        ),
    ]

    var body: some View {
        VStack(spacing: DesignSystem.Spacing.lg) {
            ForEach(menuSections) { section in
                menuSection(section)
            }
        }
    }

    // MARK: - 菜单分组

    @ViewBuilder
    private func menuSection(_ section: MenuSection) -> some View {
        VStack(alignment: .leading, spacing: DesignSystem.Spacing.md) {
            if !section.title.isEmpty {
                HStack {
                    Text(section.title)
                        .font(.footnote)
                        .foregroundColor(DesignSystem.Colors.textPrimary).opacity(0.5)

                    Spacer()
                }
                .padding(.horizontal, DesignSystem.Spacing.lg)
            }

            VStack(spacing: 0) {
                ForEach(Array(section.items.enumerated()), id: \.element.id) { index, item in
                    menuItem(item, isLast: index == section.items.count - 1)
                }
            }
            .background(DesignSystem.Colors.backgroundCard)
            .cornerRadius(DesignSystem.Rounded.md)
        }
    }

    // MARK: - 菜单项

    @ViewBuilder
    private func menuItem(_ item: MenuItem, isLast: Bool) -> some View {
        menuItemButton(item)

        if !isLast {
            Divider()
                .padding(.leading, 72)
        }
    }

    // MARK: - 菜单项按钮

    @ViewBuilder
    private func menuItemButton(_ item: MenuItem) -> some View {
        Button(action: {
            handleMenuTap(item)
        }) {
            menuItemContent(item)
        }
        .buttonStyle(PlainButtonStyle())
        .accessibilityAddTraits(.isButton)
        .accessibilityLabel(item.title)
        .accessibilityHint(item.subtitle ?? "")
    }

    // MARK: - 菜单项内容

    @ViewBuilder
    private func menuItemContent(_ item: MenuItem) -> some View {
        HStack(spacing: DesignSystem.Spacing.md) {
            menuItemIcon(item)
            menuItemTextContent(item)
            Spacer()
            menuItemTrailing(item)
        }
        .padding(DesignSystem.Spacing.lg)
        .contentShape(Rectangle())
    }

    // MARK: - 菜单项图标

    @ViewBuilder
    private func menuItemIcon(_ item: MenuItem) -> some View {
        ZStack {
            Circle()
                .fill(item.iconColor.opacity(0.1))
                .frame(width: 40, height: 40)

            Image(systemName: item.iconName)
                .font(.system(size: 18, weight: .medium))
                .foregroundColor(item.iconColor)
        }
    }

    // MARK: - 菜单项文本内容

    @ViewBuilder
    private func menuItemTextContent(_ item: MenuItem) -> some View {
        VStack(alignment: .leading, spacing: DesignSystem.Spacing.xs) {
            Text(item.title)
                .font(DesignSystem.Typography.body)
                .foregroundColor(DesignSystem.Colors.textPrimary)
                .fontWeight(.medium)

            if let subtitle = item.subtitle {
                Text(subtitle)
                    .font(DesignSystem.Typography.caption)
                    .foregroundColor(DesignSystem.Colors.textSecondary)
            }
        }
    }

    // MARK: - 菜单项尾部内容

    @ViewBuilder
    private func menuItemTrailing(_ item: MenuItem) -> some View {
        HStack(spacing: DesignSystem.Spacing.sm) {
            if let badgeCount = item.badgeCount, badgeCount > 0 {
                menuItemBadge(count: badgeCount)
            }

            Image(systemName: "chevron.right")
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(DesignSystem.Colors.textTertiary)
        }
    }

    // MARK: - 菜单项徽章

    @ViewBuilder
    private func menuItemBadge(count: Int) -> some View {
        Text("\(count)")
            .font(DesignSystem.Typography.caption)
            .foregroundColor(.white)
            .padding(.horizontal, DesignSystem.Spacing.sm)
            .padding(.vertical, DesignSystem.Spacing.xs)
            .background(DesignSystem.Colors.error)
            .cornerRadius(12)
            .minimumScaleFactor(0.8)
    }

    // MARK: - 菜单点击处理

    private func handleMenuTap(_ item: MenuItem) {
        print("点击了菜单项: \(item.title)")

        switch item.id {
        case "myWorks":
            // 导航到我的作品页面
            break
        case "myFavorites":
            // 导航到我的收藏页面
            break
        case "messages":
            // 导航到消息中心页面
            break
        default:
            break
        }
    }
}

// MARK: - 数据模型

/// 菜单分组模型
struct MenuSection: Identifiable {
    let id = UUID()
    let title: String
    let items: [MenuItem]
}

/// 菜单项模型
struct MenuItem: Identifiable {
    let id: String
    let title: String
    let subtitle: String?
    let iconName: String
    let iconColor: Color
    let badgeCount: Int?

    init(
        id: String,
        title: String,
        subtitle: String? = nil,
        iconName: String,
        iconColor: Color,
        badgeCount: Int? = nil
    ) {
        self.id = id
        self.title = title
        self.subtitle = subtitle
        self.iconName = iconName
        self.iconColor = iconColor
        self.badgeCount = badgeCount
    }
}
