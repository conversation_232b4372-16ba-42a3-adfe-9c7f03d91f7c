//
//  ImageCreation1.swift
//  GrofyAI
//
//  Created by kissy on 2025/6/12.
//
import SwiftUI
import FlowStacks


struct ImageCreationView: View {
    
    @State private var detail: ArtWorksCell?
    // 新增一个状态，用于判断是否是初次加载
    @State private var isInitialLoad = true
    
    
    @EnvironmentObject var navigator: FlowPathNavigator
    //    @Environment(\.activeTab) private var activeTab //当前活动路由
    @StateObject private var controller = ImageAiController()
    @Environment(\.colorScheme) private var colorScheme
    
    init(detail: ArtWorksCell? = nil) {
            // 使用 _detail 访问底层的 State 结构体，并用传入的值来初始化它
            self._detail = State(initialValue: detail)
        }
    
    var body: some View {
        ZStack{
//            DesignSystem.Colors.backgroundPage
//                .aspectRatio(contentMode: .fill) // 填充整个区域
//                .edgesIgnoringSafeArea(.all) // 忽略所有安全区域
            if colorScheme == .light {
                VStack{
                    Color.clear
                        .background( // 使用全屏背景层替代Spacer方案
                            Image("ImageNavBackground")
                                .resizable() // 关键：使图片可缩放
                                .aspectRatio(contentMode: .fill) // 填充整个区域
                                .edgesIgnoringSafeArea(.all) // 忽略所有安全区域
                        )
                        .frame(height: safeAreaTopInset())
                    Spacer()
                }
                
            }
            
            VStack(spacing: 5) {
                CustomNavigationBarView()
                // 顶部的可滚动标签栏
                ScrollViewReader { scrollViewProxy in
                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack(spacing: 20) {
                            ForEach(ImageAiModel.allCases, id: \.self) { tab in
                                TabButton(title: tab.description, isSelected: controller.selectedModel == tab) {
                                    // 这里的动画只影响顶部标签栏（下划线和滚动）
                                    withAnimation(.spring()) {
                                        controller.selectedModel = tab
                                    }
                                }
                                .id(tab)
                            }
                        }
                        .padding(.horizontal)
                    }
                    .frame(height: 35)
                    .background(Color.clear)
                    //                .background(Color(red: 246/255, green: 246/255, blue: 246/255))
                    //                .background(Color(.systemGray6).opacity(0.3))
                    .onChange(of: controller.selectedModel) { newTab in
                        hideKeyboard()
                        withAnimation {
                            scrollViewProxy.scrollTo(newTab, anchor: .center)
                        }
                        if !isInitialLoad {
                            self.detail = nil
                        }
                    }
                    .padding(.bottom,5)
                }
                
                // 下方的可滑动内容区域 (已优化)
                TabView(selection: $controller.selectedModel) {
                    ForEach(ImageAiModel.allCases, id: \.self) { tab in
                        //                    ScrollView {
                        ZStack {
                            getColorForTab(tab,info: detail)
                                .onAppear{
                                    FormDataStorage.saveModel(model: tab)
                                }
                                .ignoresSafeArea()
                        }
                        //                    }
                        .scrollContentBackground(.hidden) // 隐藏默认背景
                        .background(Color(red: 246/255, green: 246/255, blue: 246/255).opacity(0.8))
                        //                    .background(Color(red: 173/255, green: 169/255, blue: 255/255))
                        .tag(tab)
                    }
                }
                .tabViewStyle(.page(indexDisplayMode: .never))
                // ✅ 当 selectedTab 改变时，禁用 TabView 的动画
                .animation(nil, value: controller.selectedModel)
                
            }
        }
        .keyboardAware()
        .ignoresSafeArea(edges: .bottom)
        .navigationBarHidden(true)
        .background(DesignSystem.Colors.backgroundPage)
        .onAppear{
            if let imageModel = ImageAiModel(
                rawValue: detail?.provider ?? FormDataStorage.loadModel(as: ImageAiModel.self )?.rawValue ??  "StableDiffusion"
            ) {
                // 成功找到了枚举项
                controller.selectedModel = imageModel
            }
            DispatchQueue.main.async {
                // 使用 DispatchQueue.main.async 是一个好习惯
                // 确保 onAppear 中的状态设置完成后，再更新 isInitialLoad
                self.isInitialLoad = false
            }
            
        }
    }
    

    
    
    
    // 辅助函数：获取顶部安全区域高度
    private func safeAreaTopInset() -> CGFloat {
        let keyWindow = UIApplication.shared.connectedScenes
            .filter({ $0.activationState == .foregroundActive })
            .map({ $0 as? UIWindowScene })
            .compactMap({ $0 })
            .first?.windows
            .filter({ $0.isKeyWindow }).first
        return keyWindow?.safeAreaInsets.top ?? 0
    }
    
    @ViewBuilder
    private func getColorForTab(_ tab: ImageAiModel,info detail:ArtWorksCell?) -> some View {
        switch tab {
        case .openAi:
            OpenAiImageContentView(detail: detail)
        case .flux:
            FluxImageContentView(detail: detail)
        case .ideogram:
            IdeogramImageContentView(detail: detail)
        case .midjourney:
            MidjourneyImageContentView(detail: detail)
        case .stable_diffusion_3_5:
            StableDiffusionImageContentView(detail: detail)
        }
    }
}

// TabButton 视图保持不变
struct TabButton: View {
    let title: String
    let isSelected: Bool
    let action: () -> Void
    @Environment(\.colorScheme) private var colorScheme
    
    var body: some View {
        VStack(spacing: 5) {
            // 模型名称
            Button(action: action) {
                Text(title)
                    .font(.system(size: 16,weight: .medium))
                    .scaleEffect(isSelected ? 16/16 : 1.0) // 16 / 14 ≈ 1.14
                    .animation(.easeInOut(duration: 0.2), value: isSelected)
                    .foregroundColor(
                        isSelected ? DesignSystem.Colors.textPrimary : DesignSystem.Colors.textSecondary
                    )
                    .multilineTextAlignment(.center)
            }
            
            RoundedRectangle(cornerRadius: 2)
                .frame( maxWidth:(isSelected ? 20 :0), maxHeight: 2)
                .foregroundColor(isSelected ? DesignSystem.Colors.primary :  .clear)
        }
    }
}


struct CustomNavigationBarView: View {
    @EnvironmentObject var navigator: FlowPathNavigator
    var body: some View {
        VStack(spacing: 0) {
            // 自定义导航栏主体
            HStack {
                Image(systemName: "chevron.backward")
                    .foregroundStyle(DesignSystem.Colors.textPrimary)
                    .font(.title3)
                    .onTapGesture {
                        navigator.popToRoot()
                    }
                Spacer()
                Image(systemName: "clock")
                    .foregroundStyle(DesignSystem.Colors.textPrimary)
                    .font(.title3)
                    .onTapGesture {
                        navigator.push(Route.artWorkHistory(defaultMode: .image))
                    }
            }
            .frame(height: 10)
            
        }
        .frame(maxWidth: .infinity)
        .padding(.top,10)
        .padding(.bottom,20)
        .padding(.horizontal,15)
    }
}
