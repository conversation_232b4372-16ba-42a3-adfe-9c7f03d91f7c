//
//  FluxImageView.swift
//  GrofyAI
//
//  Created by kissy on 2025/4/27.
//

import SwiftUI
import PhotosUI
import FlowStacks


struct FluxImageContentView: View {
    var detail: ArtWorksCell?
    @StateObject private var controller = FluxImageController()
    @EnvironmentObject var navigator: FlowPathNavigator
    
    @EnvironmentObject private var keyboardManager: KeyboardManager
    @FocusState private var focusedField: FormFocusField?
    
    private var bottomPadding: CGFloat {
        if keyboardManager.isVisible {
//            最终高度是要减去确认按钮高度
            return keyboardManager.height - 80
        } else {
            return 0
        }
    }

    var body: some View {
        VStack(spacing: 10) {
            ScrollViewReader { proxy in
                ScrollView {
                    VStack(spacing: 15){
                        //MARK: model
                        VStack(alignment: .leading,spacing: 10) {
                            SectionHeader( title: "选择模型" )
                            StatefulDropDownPicker(
                                placeholder: "选择生成模型...",
                                options: FluxImageAi.Model.allCases,
                                selection: $controller.model.toOptional()
                            ) { option in
                                ModelOptionCell(
                                    icon: option.icon,
                                    icon_darkTheme: option.icon_darkTheme,
                                    describe:option.describe,
                                    displayName: option.displayName
                                )
                            }
                        }
                        
                        //MARK: prompt
                        VStack(alignment: .leading,spacing: 10) {
                            SectionHeader(
                                title: "画面描述",
                                helpTitle: "画面描述",
                                helpMessage: "描述画面中的主要元素、场景、氛围、光线和构图等。"
                            )
                            RichTextEditor(
                                placeholder: "请详细描述你的画面...",
                                text: $controller.prompt,
                                maxCount: 1000
                            )
                            .focused($focusedField, equals: .prompt)
                            .id(FormFocusField.prompt)
                        }
                        .zIndex(-1)
                        
                        //MARK: image_url
                        VStack(alignment: .leading,spacing: 10) {
                            SectionHeader( title: "参考图" )
                            VStack{
                                SingleImageUploader(
                                    imageUrl: $controller.image_url.orDefault(""),
                                    threadId: controller.threadId)
                            }
                            .padding(10)
                            .background(
                                RoundedRectangle(cornerRadius: 10)
                                    .fill(DesignSystem.Colors.backgroundCard)
                            )
                        }
                        
                        //MARK: aspect_ratio
                        VStack(alignment: .leading,spacing: 10) {
                            SectionHeader( title: "长宽比" )
                            AspectRatioSelector(
                                items: FluxImageAi.AspectRatio.allCases,
                                selectedItem: $controller.aspect_ratio
                            )
                            .padding(.vertical,10)
                            .padding(.horizontal,5)
                            .background(
                                RoundedRectangle(cornerRadius: 10)
                                    .fill(DesignSystem.Colors.backgroundCard)
                            )
                        }
                        
                        
                        VStack(alignment: .leading,spacing: 15) {
                            //MARK: output_format
                            VStack(alignment: .leading,spacing: 10) {
                                SectionHeader( title: "输出格式",custom: true )
                                    .font(.system(size: 14))
                                RadioSelector(
                                    items: FluxImageAi.OutputFormat.allCases,
                                    selection: $controller.output_format,
                                    rowHeight: 30
                                ) { quality, isSelected in
                                    // 在这里，你可以自由定义每个单元格的 UI
                                    //  RadioOptionCell 默认单元个格项
                                    RadioOptionCell(
                                        title: quality.description,
                                        isSelected: isSelected
                                    )
                                }
                            }
                            
                            VStack(alignment: .leading,spacing: 10) {
                                //MARK: prompt_upsampling
                                HStack{
                                    SectionHeader(
                                        title: "提示词上采样",
                                        helpTitle: "提示词上采样",
                                        helpMessage: "对提示词执行上采样，启用采样将会自动修改提示词以获得更具创造性的生成结果",
                                        custom: true )
                                    .font(.system(size:14))
                                    
                                    Spacer()
                                    
                                    Toggle("", isOn: $controller.prompt_upsampling)
                                        .foregroundColor(DesignSystem.Colors.textSecondary)
                                        .scaleEffect(0.65)
                                        .padding(.trailing,10)
                                        .frame(width:25)
                                }
                            }
                            
                            VStack(alignment: .leading,spacing: 10) {
                                //MARK: seed
                                SectionHeader(
                                    title: "随机种子 (可选)",
                                    helpTitle: "随机种子",
                                    helpMessage: "随机种子，用于生成相同样式的图像",
                                    custom: true )
                                .font(.system(size: 14))
                                
                                NumberField(
                                    placeholder: "请输入随机种子",
                                    value: $controller.seed,
                                    range: 0...2147483647,
                                    showRandomButton: true
                                )
                                .multilineTextAlignment(.leading)
                                .focused($focusedField, equals: .seed)
                                .id(FormFocusField.seed)
                            }
                            
                            VStack(alignment: .leading,spacing: 10) {
                                //MARK: safety_tolerance
                                StepperField(
                                    value: $controller.safety_tolerance.orDefault(0),
                                    in: 0...6 // 限制数量在 1 到 8 之间
                                ) {
                                    SectionHeader(
                                        title: "安全等级",
                                        helpTitle: "安全等级",
                                        helpMessage: "输入和输出的容忍审核级别。0表示最严格，6表示最宽松",
                                        custom: true )
                                    .font(.system(size: 14))
                                }
                            }
                            
                            //仅支持：FLUX_PRO_1_1_ULTRA
                            fluxProUltraView(show:controller.model == FluxImageAi.Model.flux_pro_1_1_ultra)
                        }
                        .padding(10)
                        .background(
                            RoundedRectangle(cornerRadius: 10)
                                .fill(DesignSystem.Colors.backgroundCard)
                        )
                        
                        VStack {}
                        // 在显示 Alert 时关闭键盘
                            .alert("错误", isPresented: $controller.showError) {
                                Button("确定"){}
                            } message: {
                                Text(controller.errorMessage)
                            }
                    }
                }
                .onChange(of: focusedField) { newFocus in
                    guard let focus = newFocus else {return}
                    withAnimation(.easeInOut(duration: keyboardManager.animationDuration)) {
                        proxy.scrollTo(focus, anchor: .bottom)
                    }
                }
                
            }
            .scrollIndicators(.hidden)
            .padding(.horizontal,15)
            //处理键盘高度
            .padding(.bottom,  bottomPadding)
            .animation(.easeOut(duration: keyboardManager.animationDuration), value: bottomPadding)
            
            
            VStack {
                // 在这里可以添加分割线或其他元素
                Button( action:controller.onSubmit) {
                    HStack{
                        Spacer()
                        HStack(spacing: 10){
                            Text("确认 \( controller.credits ?? 0 )")
                                .font(.headline)
                                .padding()
                            
                            if controller.isLoading {
                                ProgressView()
                                    .tint(DesignSystem.Colors.backgroundCard)
                            }
                        }
                        Spacer()
                    }
                    .frame(maxWidth: .infinity) // 使文本容器宽度最大化
                    .foregroundColor(.white)
                }
                .background(DesignSystem.Colors.primary) // 按钮背景色
                .cornerRadius(15) // 圆角
                .frame(width: UIScreen.main.bounds.width * 0.9) // 屏幕宽度的80%
                .padding(.bottom, 30) // 底部间距
            }
        }
        .background(DesignSystem.Colors.backgroundPage)
        .tint(DesignSystem.Colors.primary)// 设置 Toggle 按钮背景色
        .onTapGesture {
            // 当点击背景时，调用这个函数
            hideKeyboard()
        }
        .onChange(of: controller.transactionID) { newID in
            // 如果 newID 不是 nil，说明我们得到了一个有效的ID，应该导航
            if newID != nil {
                navigator.push(Route.imageArtWorkCreationResult(transactionID: newID!,detail: nil))
            }
        }
        .onAppear{
            controller.setThreadId()
            
            guard let workInfo = detail?.inputFormat else { return }
//             直接赋值，如果无法解析，nil-coalescing 操作符 (??) 会保留 controller 的原值
            controller.prompt = (workInfo["prompt"] as? String) ?? controller.prompt
            controller.image_url = (workInfo["image_url"] as? String) ?? controller.image_url
            controller.prompt_upsampling = (workInfo["prompt_upsampling"] as? Bool) ?? controller.prompt_upsampling
            controller.seed = (workInfo["seed"] as? Int) ?? controller.seed
            controller.safety_tolerance = (workInfo["safety_tolerance"] as? Int) ?? controller.safety_tolerance
            controller.raw = (workInfo["raw"] as? Bool) ?? controller.raw
            controller.image_prompt_strength = (workInfo["image_prompt_strength"] as? Double) ?? controller.image_prompt_strength
            
            controller.model = workInfo.decode(key: "model") ?? controller.model
            controller.aspect_ratio = workInfo.decode(key: "aspect_ratio") ?? controller.aspect_ratio
            controller.output_format = workInfo.decode(key: "output_format") ?? controller.output_format
        }
        .onDisappear {
            // 当视图消失时（例如用户返回上一页），停止轮询
            if detail?.inputFormat != nil {
                controller.resetToDefaults()
            }
        }
        
    }
    
    @ViewBuilder
    private func fluxProUltraView (show isShow: Bool) -> some View {
        if isShow {
            VStack(alignment: .leading,spacing: 10) {
                //MARK: raw
                HStack{
                    SectionHeader( title: "原始模式",custom: true )
                        .font(.system(size: 14))
                    Spacer()
                    Toggle("", isOn: $controller.raw.orDefault(false))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                        .scaleEffect(0.65)
                        .padding(.trailing,10)
                        .frame(width:25)
                }
            }
            
            VStack(alignment: .leading,spacing: 10) {
                // MARK: image_prompt_strength
                TitledSlider(
                    title: "混合度",
                    value: $controller.image_prompt_strength.orDefault(0), // 绑定到 Double
                    in: 0.0...1.0,       // 范围是 Double
                    step: 0.1,         // 步长是 Double
                    onInfoTapped: {
                        print("信息按钮被点击了！")
                    }
                )
            }
        }
    }
}

