//
//  IdeogramImageView.swift
//  GrofyAI
//
//  Created by kissy on 2025/4/27.
//

import SwiftUI
import FlowStacks


struct IdeogramImageContentView: View {
    var detail: ArtWorksCell?
    @StateObject private var controller = IdeogramImageController()
    @EnvironmentObject var navigator: FlowPathNavigator
    
    @EnvironmentObject private var keyboardManager: KeyboardManager
    @FocusState private var focusedField: FormFocusField?
    
    private var bottomPadding: CGFloat {
        if keyboardManager.isVisible {
//            最终高度是要减去确认按钮高度
            return keyboardManager.height - 80
        } else {
            return 0
        }
    }
    
    var body: some View {
        VStack(spacing: 10) {
            ScrollViewReader { proxy in
                ScrollView {
                    VStack(spacing: 15){
                        //MARK: prompt
                        VStack(alignment: .leading,spacing: 10) {
                            //MARK: prompt
                            SectionHeader(
                                title: "画面描述",
                                helpTitle: "画面描述",
                                helpMessage: "描述画面中的主要元素、场景、氛围、光线和构图等。"
                            )
                            RichTextEditor(
                                placeholder: "请详细描述你的画面...",
                                text: $controller.prompt,
                                maxCount: 1000
                            )
                            .focused($focusedField, equals: .prompt)
                            .id(FormFocusField.prompt)
                        }
                        .zIndex(-1)
                        
                        
                        VStack(alignment: .leading,spacing: 10) {
                            //MARK: aspect_ratio
                            SectionHeader( title: "长宽比" )
                            AspectRatioSelector(
                                items: IdeogramImageAi.AspectRatio.allCases,
                                selectedItem: $controller.aspect_ratio.orDefault(.aspect1_1)
                            )
                            .padding(.vertical,10)
                            .padding(.horizontal,5)
                            .background(
                                RoundedRectangle(cornerRadius: 10)
                                    .fill(DesignSystem.Colors.backgroundCard)
                            )
                        }
                        
                        VStack(alignment: .leading, spacing: 10) {
                            //MARK: style_type
                            SectionHeader( title: "图片风格")
                            StyleSelector(
                                items: IdeogramImageAi.StyleType.allCases,
                                selection: $controller.style_type.orDefault(.auto)
                                
                            ) { quality, isSelected in
                                // 在这里，你可以自由定义每个单元格的 UI
                                //  RadioOptionCell 默认单元个格项
                                StyleOptionCell(
                                    title: quality.description,
                                    imageName: quality.ImageName,
                                    isSelected: isSelected
                                )
                            }
                            .padding(10)
                            .background(
                                RoundedRectangle(cornerRadius: 10)
                                    .fill(DesignSystem.Colors.backgroundCard)
                            )
                        }
                        
                        VStack(alignment: .leading,spacing: 15) {
                            // MARK: speed
                            VStack(alignment: .leading, spacing: 10) {
                                SectionHeader(title: "创建速度",custom: true)
                                    .font(.system(size: 14))
                                RadioSelector(
                                    items: IdeogramImageAi.Speed.allCases,
                                    selection: $controller.speed,
                                    rowHeight: 30
                                ) { quality, isSelected in
                                    // 在这里，你可以自由定义每个单元格的 UI
                                    //  RadioOptionCell 默认单元个格项
                                    RadioOptionCell(
                                        title: quality.description,
                                        isSelected: isSelected
                                    )
                                }
                            }
                            //MARK: seed
                            VStack(alignment: .leading,spacing: 10) {
                                //MARK: seed
                                SectionHeader(
                                    title: "随机种子 (可选)",
                                    helpTitle: "随机种子",
                                    helpMessage: "随机种子，用于生成相同样式的图像",
                                    custom: true
                                )
                                .font(.system(size: 14))
                                NumberField(
                                    placeholder: "请输入随机种子",
                                    value: $controller.seed,
                                    range: 0...2147483647,
                                    showRandomButton: true
                                )
                                .multilineTextAlignment(.leading)
                                .padding(.bottom,1)
                                .focused($focusedField, equals: .seed)
                                .id(FormFocusField.seed)
                            }
                            
                            //MARK: magic_prompt
                            VStack(alignment: .leading,spacing: 10) {
                                SectionHeader(
                                    title: "魔法提示",
                                    helpTitle: "魔法提示",
                                    helpMessage: "将一个简短的想法转化为丰富、详细的提示，确保引人注目的视觉效果。",
                                    custom: true )
                                .font(.system(size:14))
                                GenericSelectorView(
                                    options: Array(IdeogramImageAi.MagicPromptOption.allCases),
                                    selectedOption: $controller.magic_prompt
                                )
                            }
                        }
                        .padding(10)
                        .background(
                            RoundedRectangle(cornerRadius: 10)
                                .fill(DesignSystem.Colors.backgroundCard)
                        )
                        
                        //MARK: image_Url
                        VStack(alignment: .leading,spacing: 7) {
                            SectionHeader( title: "参考图" )
                            VStack{
                                SingleImageUploader(imageUrl: $controller.image_Url.orDefault(""),threadId: controller.threadId)
                            }
                            .padding(10)
                            .background(
                                RoundedRectangle(cornerRadius: 10)
                                    .fill(DesignSystem.Colors.backgroundCard)
                            )
                            //                        ImageUploaderView(uploadedUrls: $finalImageUrls,maxCount: 1)
                        }
                        
                        //MARK: negative_prompt
                        VStack(alignment: .leading,spacing: 10) {
                            SectionHeader(
                                title: "反向提示词",
                                helpTitle: "反向提示词”？"
                            )
                            RichTextEditor(
                                placeholder: "请详细描述你的问题或想法...",
                                text: $controller.negative_prompt.orDefault(""),
                                maxCount: 1000
                            )
                            .focused($focusedField, equals: .negativePrompt)
                            .id(FormFocusField.negativePrompt)
                        }
                        
                        VStack {}
                        // 在显示 Alert 时关闭键盘
                            .alert("错误", isPresented: $controller.showError) {
                                Button("确定"){}
                            } message: {
                                Text(controller.errorMessage)
                            }
                    }
                }
                .onChange(of: focusedField) { newFocus in
                    guard let focus = newFocus else {return}
                    withAnimation(.easeInOut(duration: keyboardManager.animationDuration)) {
                        proxy.scrollTo(focus, anchor: .bottom)
                    }
                }
            }
            .tint(DesignSystem.Colors.primary) // 设置 Toggle 按钮背景色
            .scrollIndicators(.hidden)
            .padding(.horizontal,15)
            //处理键盘高度
            .edgesIgnoringSafeArea(.bottom)
            .padding(.bottom,  bottomPadding)
            .animation(.easeOut(duration: keyboardManager.animationDuration), value: bottomPadding)
            
            VStack {
                // 在这里可以添加分割线或其他元素
                //                Divider()
                Button( action:controller.onSubmit) {
                    HStack{
                        Spacer()
                        HStack(spacing: 10){
                            Text("确认 \( controller.credits ?? 0 )")
                                .font(.headline)
                                .padding()
                            
                            if controller.isLoading {
                                ProgressView()
                                    .tint(DesignSystem.Colors.backgroundCard)
                            }
                        }
                        Spacer()
                    }
                    .frame(maxWidth: .infinity) // 使文本容器宽度最大化
                    .foregroundColor(.white)
                }
                .background(DesignSystem.Colors.primary) // 按钮背景色
                .cornerRadius(15) // 圆角
                .frame(width: UIScreen.main.bounds.width * 0.9) // 屏幕宽度的80%
                .padding(.bottom, 30) // 底部间距
            }
        }
        .background(DesignSystem.Colors.backgroundPage)
        .tint(DesignSystem.Colors.primary) // 设置 Toggle 按钮背景色
        .onChange(of: controller.transactionID) { newID in
            // 如果 newID 不是 nil，说明我们得到了一个有效的ID，应该导航
            if newID != nil {
                navigator.push(Route.imageArtWorkCreationResult(transactionID: newID!,detail: nil))
            }
        }
        .onTapGesture {
            hideKeyboard()
        }
        .onAppear{
            controller.setThreadId()
            
            guard let workInfo = detail?.inputFormat else { return }
            // 直接赋值，如果无法解析，nil-coalescing 操作符 (??) 会保留 controller 的原值
            controller.prompt = (workInfo["prompt"] as? String) ?? controller.prompt
            controller.seed = (workInfo["seed"] as? Int) ?? controller.seed
            controller.negative_prompt = (workInfo["negative_prompt"] as? String) ?? controller.negative_prompt
            controller.image_Url = (workInfo["image_Url"] as? String) ?? controller.image_Url
            
            controller.aspect_ratio = workInfo.decode(key: "aspect_ratio") ?? controller.aspect_ratio
            controller.model = workInfo.decode(key: "model") ?? controller.model
            controller.magic_prompt = workInfo.decode(key: "magic_prompt") ?? controller.magic_prompt
            controller.style_type = workInfo.decode(key: "style_type") ?? controller.style_type

        }
        .onDisappear {
            if detail?.inputFormat != nil {
                controller.resetToDefaults()
            }
        }
    }
}
