//
//  StableDiffusionImageView.swift
//  GrofyAI
//
//  Created by kissy on 2025/4/27.
//

import SwiftUI
import PhotosUI
import FlowStacks


struct StableDiffusionImageContentView: View {
    var detail: ArtWorksCell?
    @StateObject private var controller = StableDiffusionImageController()
    @EnvironmentObject var navigator: FlowPathNavigator

    @EnvironmentObject private var keyboardManager: KeyboardManager
    @FocusState private var focusedField: FormFocusField?
    
    private var bottomPadding: CGFloat {
        if keyboardManager.isVisible {
//            最终高度是要减去确认按钮高度
            return keyboardManager.height - 80
        } else {
            return 0
        }
    }

    var body: some View {
        VStack(spacing: 10) {
            ScrollViewReader { proxy in
                ScrollView {
                    VStack(spacing: 15){
                        //MARK: model
                        VStack(alignment: .leading,spacing: 10) {
                            SectionHeader( title: "选择模型" )
                            
                            StatefulDropDownPicker(
                                placeholder: "选择生成模型...",
                                options: StableDiffusion3_5ImageAi.Model.allCases,
                                selection: $controller.model
                            ) { option in
                                ModelOptionCell(
                                    icon: option.icon,
                                    icon_darkTheme: option.icon_darkTheme,
                                    describe:option.describe,
                                    displayName: option.displayName
                                )
                            }
                        }
                        .zIndex(999)
                        
                        //MARK: mode
                        VStack(alignment: .leading,spacing: 10) {
                            SectionHeader( title: "选择模式" )
                            
                            HStack{
                                GenericSelectorView(
                                    options: Array(StableDiffusion3_5ImageAi.Mode.allCases),
                                    selectedOption: $controller.mode
                                )
                                Spacer()
                            }
                            .padding(8)
                            .background(
                                RoundedRectangle(cornerRadius: 10)
                                    .fill(DesignSystem.Colors.backgroundCard)
                            )
                        }
                        
                        //MARK: imageUrl
                        VStack(alignment: .leading,spacing: 10) {
                            SectionHeader( title: "参考图\(controller.mode == .text_to_image ? "" : "*")" )
                            VStack{
                                SingleImageUploader(imageUrl: $controller.imageUrl.orDefault(""),threadId: controller.threadId)
                            }
                            .padding(10)
                            .background(
                                RoundedRectangle(cornerRadius: 10)
                                    .fill(DesignSystem.Colors.backgroundCard)
                            )
                        }
                        
                        //MARK: prompt
                        VStack(alignment: .leading,spacing: 10) {
                            //MARK: prompt
                            SectionHeader(
                                title: "画面描述",
                                helpTitle: "画面描述",
                                helpMessage: "描述画面中的主要元素、场景、氛围、光线和构图等。"
                            )
                            RichTextEditor(
                                placeholder: "请详细描述你的画面...",
                                text: $controller.prompt,
                                maxCount: 1000
                            )
                            .focused($focusedField, equals: .prompt)
                            .id(FormFocusField.prompt)
                        }
                        .zIndex(-1)
                        
                        //MARK: aspect_ratio
                        VStack(alignment: .leading,spacing: 10) {
                            //MARK: aspect_ratio
                            SectionHeader( title: "长宽比" )
                            AspectRatioSelector(
                                items: StableDiffusion3_5ImageAi.AspectRatio.allCases,
                                selectedItem: $controller.aspect_ratio.orDefault(.ratio1_1)
                            )
                            .padding(.vertical,10)
                            .padding(.horizontal,5)
                            .background(
                                RoundedRectangle(cornerRadius: 10)
                                    .fill(DesignSystem.Colors.backgroundCard)
                            )
                        }
                        
                        //MARK: style_preset
                        VStack(alignment: .leading, spacing: 10) {
                            //MARK: style_type
                            SectionHeader( title: "图片风格")
                            StyleSelector(
                                items: StableDiffusion3_5ImageAi.StylePreset.allCases,
                                selection: $controller.style_preset.orDefault(.anime)
                                
                            ) { quality, isSelected in
                                // 在这里，你可以自由定义每个单元格的 UI
                                //  RadioOptionCell 默认单元个格项
                                StyleOptionCell(
                                    title: quality.description,
                                    imageName: quality.ImageName,
                                    isSelected: isSelected
                                )
                            }
                            .padding(10)
                            .background(
                                RoundedRectangle(cornerRadius: 10)
                                    .fill(DesignSystem.Colors.backgroundCard)
                            )
                        }
                        
                        VStack(alignment: .leading,spacing: 15) {
                            //MARK: output_format
                            VStack(alignment: .leading,spacing: 10) {
                                SectionHeader( title: "输出格式",custom: true )
                                    .font(.system(size: 14))
                                RadioSelector(
                                    items: StableDiffusion3_5ImageAi.OutputFormat.allCases,
                                    selection: $controller.output_format.orDefault(.jpeg),
                                    rowHeight: 30
                                ) { quality, isSelected in
                                    // 在这里，你可以自由定义每个单元格的 UI
                                    //  RadioOptionCell 默认单元个格项
                                    RadioOptionCell(
                                        title: quality.description,
                                        isSelected: isSelected
                                    )
                                }
                            }
                            
                            VStack(alignment: .leading,spacing: 10) {
                                // MARK: strength
                                TitledSliderWithInfo(
                                    title: "混合度\(controller.mode == .text_to_image ? "" : "*")",
                                    value: $controller.strength.orDefault(0), // 绑定到 Double
                                    in: 0...1,       // 范围是 Double
                                    step: 1,         // 步长是 Double
                                    infoTitle:"混合度",
                                    infoMessage:"控制图像参数对生成图像的影响程度。0:将产生与输入完全相同的图像。1:则相当于没有传入任何图像"
                                )
                            }
                        }
                        .padding(10)
                        .background(
                            RoundedRectangle(cornerRadius: 10)
                                .fill(DesignSystem.Colors.backgroundCard)
                        )
                        
                        //MARK: negative_prompt
                        VStack(alignment: .leading,spacing: 10) {
                            SectionHeader(
                                title: "反向提示词",
                                helpTitle: "反向提示词”？"
                            )
                            RichTextEditor(
                                placeholder: "请详细描述你的问题或想法...",
                                text: $controller.negative_prompt.orDefault(""),
                                maxCount: 1000
                            )
                            .focused($focusedField, equals: .negativePrompt)
                            .id(FormFocusField.negativePrompt)
                        }
                        
                        VStack {}
                        // 在显示 Alert 时关闭键盘
                            .alert("错误", isPresented: $controller.showError) {
                                Button("确定"){}
                            } message: {
                                Text(controller.errorMessage)
                            }
                    }
                }

                .onChange(of: focusedField) { newFocus in
                    guard let focus = newFocus else {return}
                    withAnimation(.easeInOut(duration: keyboardManager.animationDuration)) {
                        proxy.scrollTo(focus, anchor: .bottom)
                    }
                }
            }
            .tint(DesignSystem.Colors.primary) // 设置 Toggle 按钮背景色
            .scrollIndicators(.hidden)
            .padding(.horizontal,15)
            //处理键盘高度
            .padding(.bottom,  bottomPadding)
            .animation(.easeOut(duration: keyboardManager.animationDuration), value: bottomPadding)
            
            VStack {
                // 在这里可以添加分割线或其他元素
                Button( action:controller.onSubmit) {
                    HStack{
                        Spacer()
                        HStack(spacing: 10){
                            Text("确认 \( controller.credits ?? 0 )")
                                .font(.headline)
                                .padding()
                            
                            if controller.isLoading {
                                ProgressView()
                                    .tint(DesignSystem.Colors.backgroundCard)
                            }
                        }
                        Spacer()
                    }
                    .frame(maxWidth: .infinity) // 使文本容器宽度最大化
                    .foregroundColor(.white)
                }
                .background(DesignSystem.Colors.primary) // 按钮背景色
                .cornerRadius(15) // 圆角
                .frame(width: UIScreen.main.bounds.width * 0.9) // 屏幕宽度的80%
                .padding(.bottom, 30) // 底部间距
            }
        }
        .background(DesignSystem.Colors.backgroundPage)
        .tint(DesignSystem.Colors.primary) // 设置 Toggle 按钮背景色
        .ignoresSafeArea(.keyboard, edges: .bottom)
        .onTapGesture {
            hideKeyboard()
        }
        .onChange(of: controller.transactionID) { newID in
            // 如果 newID 不是 nil，说明我们得到了一个有效的ID，应该导航
            if newID != nil {
                navigator.push(Route.imageArtWorkCreationResult(transactionID: newID!,detail: nil))
            }
        }
        .onAppear{
            controller.setThreadId()
            guard let workInfo = detail?.inputFormat else { return }
            // 直接赋值，如果无法解析，nil-coalescing 操作符 (??) 会保留 controller 的原值
            controller.prompt = (workInfo["prompt"] as? String) ?? controller.prompt
            controller.mode = workInfo.decode(key: "mode") ?? controller.mode
            controller.aspect_ratio = workInfo.decode(key: "aspect_ratio") ?? controller.aspect_ratio
            controller.negative_prompt = (workInfo["negative_prompt"] as? String) ?? controller.negative_prompt
            controller.output_format = workInfo.decode(key: "output_format") ?? controller.output_format
            controller.model = workInfo.decode(key: "model") ?? controller.model
            controller.style_preset = workInfo.decode(key: "style_preset") ?? controller.style_preset
            controller.strength = (workInfo["strength"] as? Int) ?? controller.strength
        }
        .onDisappear {
            if detail?.inputFormat != nil {
                controller.resetToDefaults()
            }
        }
    }
}
