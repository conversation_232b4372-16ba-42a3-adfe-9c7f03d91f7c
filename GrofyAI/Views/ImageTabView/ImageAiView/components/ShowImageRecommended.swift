//
//  ShowImageRecommentded.swift
//  GrofyAI
//
//  Created by kissy on 2025/7/15.
//

import SwiftUI
import Kingfisher 

//struct ShowVideoRecommended: View {
struct ShowImageRecommended: View {
    
    @ObservedObject var controller: RecommendedCategoriesController
    
    // 2. init 方法现在直接接收一个已经初始化好的 controller 实例
    init(controller: RecommendedCategoriesController) {
        self.controller = controller
    }
    
    // 定义网格布局：2列，自适应高度
    private let gridColumns: [GridItem] = [
        GridItem(.flexible(), spacing: 16),
        GridItem(.flexible(), spacing: 16)
    ]
    
    var body: some View {
        VStack(spacing: 0) {
            // MARK: - 顶部动态 Tab 栏 (已改造)
            ScrollViewReader { scrollViewProxy in
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: 24) {
                        // 遍历从服务器获取的 categories
                        ForEach(controller.categoryList) { category in
                            TabButton(
                                title: category.title,
                                isSelected: controller.categoryId == String( category.id )
                            ) {
                                // 点击时，更新选中的 categoryId
                                withAnimation(.spring()) {
                                    controller.categoryId = String( category.id )
                                }
                            }
                            // 使用 category.id 作为唯一标识
                            .id(category.id)
                        }
                    }
                    .padding(.bottom,10)
                }
                //                .frame(height: 40)
                .onChange(of: controller.categoryId) { newId in
                    guard let newId = newId else { return }
                    // 当选中的 ID 改变时，滚动到对应的 Tab
                    withAnimation {
                        scrollViewProxy.scrollTo(newId, anchor: .center)
                    }
                }
            }
            
            TabView(selection: $controller.categoryId) {
                // 遍历所有分类，为每个分类创建一个页面
                ForEach(controller.categoryList) { category in
                    let categoryIdStr = String(category.id)
                    
                    // 每个页面的内容视图
                    contentPage(for: categoryIdStr)
                        .tag(categoryIdStr as String?) // 必须用 Optional String? 匹配 selection 类型
                }
            }
            .frame(height: UIScreen.main.bounds.height*0.6)
            // 设置 TabView 样式为页面滚动，并隐藏底部的分页指示器
            .tabViewStyle(.page(indexDisplayMode: .never))
            .ignoresSafeArea(edges: .bottom)
            
            Spacer()
        }
    }
    
    
    @ViewBuilder
    private func contentPage(for categoryId: String) -> some View {
        // 获取当前页面的数据和加载状态
        let items = controller.categoryDataCache[categoryId] ?? []
        let pageIsLoading = controller.isLoading[categoryId] ?? false
        
        ZStack {
            if pageIsLoading && items.isEmpty {
                // 如果正在加载且没有旧数据，显示加载动画
                ProgressView()
            } else if !items.isEmpty {
                // 如果有数据，显示瀑布流
                ScrollView(.vertical, showsIndicators: false) {
                    WaterfallGrid(items, columns: 2, spacing: 10) { item in
                        KFImage(URL(string: item.cover ?? ""))
                            .resizable()
                            .scaledToFit()
                            .clipShape(RoundedRectangle(cornerRadius: 12))
                            .onTapGesture {
                                // 点击图片时，传递相关信息给状态变量
                                controller.selectedArtwork = item
                            }
                    }
                }
            }else {
                // 加载完成但没有数据，或还未开始加载
                // 可以放一个空状态或提示视图
                // Color.clear
                imageGridSkeleton
            }
        }
        .scrollContentBackground(.hidden)
        // 【关键】当此页面出现时，触发数据加载
    }
    
    @ViewBuilder
    private var imageGridSkeleton: some View {
        LazyVGrid(columns: gridColumns, spacing: DesignSystem.Spacing.lg) {
            ForEach(0..<6, id: \.self) { _ in
                ImageGridSkeletonView()
            }
        }
    }
}

struct ImageGridSkeletonView: View {
    var body: some View {
        VStack(spacing: 0) {
            // 图片区域骨架
            RoundedRectangle(cornerRadius: DesignSystem.Rounded.md)
                .fill(DesignSystem.Colors.backgroundCard)
                .frame(height: 180)
                .overlay(alignment: .bottomLeading) {
                    // 标题区域骨架
                    RoundedRectangle(cornerRadius: 4)
                        .fill(DesignSystem.Colors.backgroundCard.opacity(0.6))
                        .frame(width: 80, height: 16)
                        .padding(DesignSystem.Spacing.md)
                }
        }
        .redacted(reason: .placeholder)
    }
}

