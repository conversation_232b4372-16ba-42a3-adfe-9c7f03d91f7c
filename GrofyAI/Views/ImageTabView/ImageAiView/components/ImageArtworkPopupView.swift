//
//  ArtworkPopupView.swift
//  GrofyAI
//
//  Created by kissy on 2025/7/15.
//
import SwiftUI
import Kingfisher
import FlowStacks

struct ImageArtworkPopupView: View {
    
    let artwork: ArtWorksCell
    @Binding var isPresented: ArtWorksCell? // 使用 Binding 来关闭弹窗
    @EnvironmentObject var navigator: FlowPathNavigator
    @EnvironmentObject private var globalAuthManager: GlobalAuthManager

    private let authStore = AuthStore.shared

    var body: some View {
        ZStack {
            // 半透明的背景遮罩
            Color.black.opacity(0.6)
                .ignoresSafeArea()
                .onTapGesture {
                    // 点击背景关闭弹窗
                    isPresented = nil
                }
            
            // 弹窗主体内容
            VStack(spacing: 0) {
                
                KFImage(URL(string: artwork.cover ?? ""))
                    .resizable()
                    .scaledToFit()
                    .clipShape(RoundedRectangle(cornerRadius: 12))
                    .frame(width: UIScreen.main.bounds.width * 0.6)
                
                Spacer().frame(height: 24)
                
                // “做同款” 按钮
                Button(action: {
                    if authStore.getAccessToken() == nil {
                        //显示登录弹窗(页面)
                        globalAuthManager.requestAuthentication()
                    }else {
                        navigator.push(Route.createImage(detail: artwork))
                    }
                    
                    // 操作完成后，关闭弹窗
                    isPresented = nil
                }) {
                    Text("做同款")
                        .font(.headline)
                        .fontWeight(.bold)
                        .foregroundColor(.white)
                        .frame(maxWidth: UIScreen.main.bounds.width * 0.6)
                        .padding()
                        .background(DesignSystem.Colors.primary)
                        .cornerRadius(12)
                }
            }
            .padding(24)
            .cornerRadius(12)
            
            // 右上角的关闭按钮 (X)
            .overlay(alignment: .topTrailing) {
                Button(action: {
                    isPresented = nil
                }) {
                    Image(systemName: "xmark.circle.fill")
                        .font(.system(size: 20))
                        .foregroundColor(.gray.opacity(0.8))
                        .background(Circle().fill(.white)) // 给X加一个白色底，更清晰
                }
                .padding(.horizontal,15) // 调整关闭按钮的位置
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
}
