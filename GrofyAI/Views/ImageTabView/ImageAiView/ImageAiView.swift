import FlowStacks
import Kingfisher
import SwiftUI


// MARK: - 图片AI主视图

struct ImageAiView: View {

    @StateObject private var imageRecController = RecommendedCategoriesController(agentType: .image)
    @StateObject private var controller = CreditsController()
    
    @EnvironmentObject var navigator: FlowPathNavigator
    @EnvironmentObject private var globalAuthManager: GlobalAuthManager
    @Environment(\.colorScheme) private var colorScheme
  
    
    private let authStore = AuthStore.shared
    
    private func safeAreaTopInset() -> CGFloat {
        let keyWindow = UIApplication.shared.connectedScenes
            .filter({ $0.activationState == .foregroundActive })
            .map({ $0 as? UIWindowScene })
            .compactMap({ $0 })
            .first?.windows
            .filter({ $0.isKeyWindow }).first
        return keyWindow?.safeAreaInsets.top ?? 0
    }
    
    var body: some View {
        GeometryReader { geometry in
            ZStack{
                
                if colorScheme == .light {
                    VStack{
                        Color.clear
                            .background( // 使用全屏背景层替代Spacer方案
                                Image("ImageNavBackground")
                                    .resizable() // 关键：使图片可缩放
                                    .aspectRatio(contentMode: .fill) // 填充整个区域
                                    .edgesIgnoringSafeArea(.all) // 忽略所有安全区域
                            )
                            .frame(height: safeAreaTopInset())
                        Spacer()
                    }
                }
                
                VStack(spacing: 5) {
                    ImageNavigationBarView()
                    
                    ScrollView(.vertical, showsIndicators: false) {
                        VStack(spacing: 20) {
                            //更多模型提示
                            modelsCards
                            
                            entryCardsSection
                            
                            //作品推荐
                            ShowImageRecommended(controller: imageRecController)
                        }
                        .padding(.horizontal,15)
                        .frame(maxWidth: .infinity,maxHeight: .infinity)
                        .background(DesignSystem.Colors.backgroundPage)
                    }
                }
                
                if let artworkToShow = imageRecController.selectedArtwork {
                    ImageArtworkPopupView(artwork: artworkToShow, isPresented: $imageRecController.selectedArtwork)
                        .transition(.opacity.combined(with: .scale(scale: 0.9))) // 添加漂亮的出现/消失动画
                }
                
                
            }
            .onAppear{
                print("页面加载了 111123123")
                Task {
                    await imageRecController.loadData()
                    
                    if authStore.getAccessToken() != nil {
                        await controller.getCreditsItemsData()
                    }
                }
            }
        }
        .background(DesignSystem.Colors.backgroundPage)
    }
    
    // MARK: - 私有视图组件
    @ViewBuilder
    private var modelsCards: some View {
        VStack(alignment: .leading){
            ZStack{
                RoundedRectangle(cornerRadius: 12)
                    .fill(DesignSystem.Colors.backgroundCard)
                
                if colorScheme == .light {
                    Image("ModelsBackground") // 1. 使用你的图片名称
                        .resizable()           // 2. 使图片可调整大小
                        .scaledToFill()
                }
                
                HStack {
                    Spacer()
                    Image("Models") // 1. 使用你的图片名称
                        .resizable()           // 2. 使图片可调整大小
                        .scaledToFill()
                        .frame(width:100,height: 100)
                        .padding(.trailing,10)
                }
                
                VStack(alignment: .leading){
                    Text("9+ 模型任你选择")
                    HStack(spacing: -8){
                        ForEach(ImageModelsIcon.allCases, id: \.self) { Icon in
                            VStack{
                                VStack{
                                    if colorScheme == .dark, Icon.darkTheme.hasSuffix("__customWhite") {
                                        Image(Icon.rawValue) // <-- 确保这里的名字和你在 Assets 中设置的一致
                                            .resizable()
                                            .renderingMode(.template)
                                            .foregroundColor(.white)
                                            .aspectRatio(contentMode: .fill)
                                            .frame(width: 14, height: 14)
                                    } else {
                                        Image(Icon.rawValue) // <-- 确保这里的名字和你在 Assets 中设置的一致
                                            .resizable()
                                            .aspectRatio(contentMode: .fill)
                                            .frame(width: 14, height: 14)
                                    }
                                }
                                .padding(6) // 1. 向内添加边距，给背景留出空间
                                .background(DesignSystem.Colors.iconBackground) // 2. 设置背景颜色
                                .clipShape(Circle()) // 3. 将背景裁剪成圆形
                            }
                            .padding(2) // 1. 向内添加边距，给背景留出空间
                            .background(DesignSystem.Colors.backgroundCard) // 2. 设置背景颜色
                            .clipShape(Circle()) // 3. 将背景裁剪成圆形
                        }
                    }
                    
                    Button(action: {
                        // 在这里处理按钮点击事件
                        print("按钮被点击了！")
                    }) {
                        // 文字
                        Text("立即查看")
                            .font(.system(size: 12))
                            .foregroundColor(DesignSystem.Colors.primary)
                    }
                    .padding(.vertical,8)
                    .padding(.horizontal,10)
                    .frame(height: 25)
                    .background( colorScheme == .light ? DesignSystem.Colors.backgroundCard : DesignSystem.Colors.backgroundInput)
                    .cornerRadius(12)
                }
                .padding(15)
                .frame(
                    maxWidth:.infinity,
                    alignment: .leading
                )
            }
            .clipShape(RoundedRectangle(cornerRadius: 12))
        }
    }
    
    
    @ViewBuilder
    private var entryCardsSection: some View {
        VStack(alignment: .leading){
            HStack(spacing: 15){
                ImageEntryCard(
                    title: "Chat GPT",
                    subtitle: "",
                    iconName: "ImageToChat"
                ) {
                    if authStore.getAccessToken() == nil {
                        //显示登录弹窗(页面)
                        globalAuthManager.requestAuthentication()
                    }else {
                        navigator.push(Route.imageGeneratorChat())
                    }
                    
                }
                
                ImageEntryCard(
                    title: "AI 生图",
                    subtitle: "",
                    iconName: "ImageToAi"
                ) {
                    //进入生图页面
                    if authStore.getAccessToken() == nil {
                        //显示登录弹窗(页面)
                        globalAuthManager.requestAuthentication()
                    }else {
                        navigator.push(Route.createImage(detail: nil))
                    }
                    
                }
            }
            .frame(height:100)
        }
    }
}



// MARK: - 图片入口卡片

struct ImageEntryCard: View {
    let title: String
    let subtitle: String
    let iconName: String
    let onTap: () -> Void

    init(title: String, subtitle: String, iconName: String, onTap: @escaping () -> Void) {
        self.title = title
        self.subtitle = subtitle
        self.iconName = iconName
        self.onTap = onTap
    }

    var body: some View {
        VStack{
            ZStack(alignment: .topLeading) { // 默认对齐方式设为左上角
                // 使用另一个ZStack来精确定位图片
                ZStack(alignment: .bottomTrailing) {
                    // 这个空的Color视图是为了让ZStack占据整个父视图空间
                    Color.clear

                    Image(iconName)
                        .resizable()
                        .scaledToFit()
                        .frame(width: 80) // 限制图片宽度
                    // 图片不需要额外padding，因为它已经被对齐到右下角
                }

                Text(title)
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                .padding([.top, .leading], 15) // 给文本添加内边距，使其离开角落
            }
            .frame(maxWidth:.infinity,maxHeight: .infinity)
            .background(DesignSystem.Colors.backgroundCard) // 背景色
            .clipShape(RoundedRectangle(cornerRadius: 12)) // 圆角
        }
        .onTapGesture {
            onTap()
        }
        .frame(maxWidth:.infinity,maxHeight: .infinity)
    }
}

// MARK: - 自定义导航
struct ImageNavigationBarView: View {
    @EnvironmentObject var navigator: FlowPathNavigator
    var body: some View {
        VStack(spacing: 0) {
            // 自定义导航栏主体
            HStack {
                Text("图片")
                    .font(.system(size: 16,weight: .medium))
                Spacer()
                Button(action: {
                    navigator.push(Route.creditsHistory)
                }) {
                    HStack(spacing: 3) {
                        // 图标
                        Image("IconStarPoints") // <-- 确保这里的名字和你在 Assets 中设置的一致
                            .resizable()
                            .aspectRatio(contentMode: .fit)
                            .frame(width: 14, height: 14)
                        
                        // 文字
                        Text("60")
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(DesignSystem.Colors.textPrimary)
                    }
                }
                .padding(8)
                .frame(height: 25)
                .background(DesignSystem.Colors.backgroundCard)
                .cornerRadius(12)
                
            }
            .frame(maxWidth: .infinity)
            .padding(.bottom,20)
            .padding(.horizontal,15)
        }
    }
}
