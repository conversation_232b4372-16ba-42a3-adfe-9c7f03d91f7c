import FlowStacks
import SwiftUI

// MARK: - 创建知识库分类页面

struct CreateKnowledgeCategoryScreen: View {
    @EnvironmentObject var controller: KnowledgeTabController
    @EnvironmentObject var navigator: FlowPathNavigator

    @State private var title = ""
    @State private var content = ""
    @State private var isSaving = false
    @State private var showError = false
    @State private var errorMessage = ""
    @State private var hasChanges = false
    @State private var showExitConfirmation = false
    @State private var debounceTimer: Timer?

    private var canSave: Bool {
        !title.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
    }

    var body: some View {
        VStack(spacing: 0) {
            formContent
                .padding(.horizontal, DesignSystem.Spacing.lg)
                .padding(.top, DesignSystem.Spacing.lg)

            Spacer()
        }
        .hideNavigationBarLine(backgroundColor: DesignSystem.Colors.backgroundPage)
        .background(DesignSystem.Colors.backgroundPage)
        .navigationBarBackButtonHidden()
        .navigationBarTitleDisplayMode(.inline)
        .toolbar {
            ToolbarItem(placement: .topBarLeading) {
                BackButton(onBack: handleBackAction)
            }
            ToolbarItem(placement: .topBarTrailing) {
                LoadingButton(
                    isLoading: isSaving,
                    text: "保存",
                    isEnabled: hasChanges && canSave,
                    onTap: saveCategory
                )
            }
            ToolbarItem(placement: .principal) {
                Text("创建知识库")
                    .font(.system(size: DesignSystem.FontSize.lg, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.textPrimary)
            }
        }
        .onDisappear {
            ToastManager.shared.clearToast()
        }
        .alert("错误", isPresented: $showError) {
            Button("确定") {
                showError = false
            }
        } message: {
            Text(errorMessage)
        }
        .confirmationDialog(
            "确认退出",
            isPresented: $showExitConfirmation,
            titleVisibility: .visible
        ) {
            Button("保存并退出") {
                saveCategory()
            }
            Button("放弃修改", role: .destructive) {
                navigator.pop()
            }
            Button("取消", role: .cancel) {}
        } message: {
            Text("您有未保存的修改，是否要保存？")
        }
        .onDisappear {
            debounceTimer?.invalidate()
            debounceTimer = nil
        }
    }

    // MARK: - 表单内容

    @ViewBuilder
    private var formContent: some View {
        VStack(spacing: DesignSystem.Spacing.lg) {
            titleInputCard

            descriptionInputCard
        }
    }

    // MARK: - 标题输入卡片

    @ViewBuilder
    private var titleInputCard: some View {
        VStack(alignment: .leading, spacing: DesignSystem.Spacing.sm) {
            SectionHeader(title: "标题")

            TextField("请输入", text: $title)
                .font(DesignSystem.Typography.content)
                .onChange(of: title) { _ in
                    debouncedCheckForChanges()
                }
                .padding(DesignSystem.Spacing.md)
                .background(DesignSystem.Colors.backgroundCard)
                .cornerRadius(DesignSystem.Rounded.md)
        }
    }

    // MARK: - 描述输入卡片

    @ViewBuilder
    private var descriptionInputCard: some View {
        VStack(alignment: .leading, spacing: DesignSystem.Spacing.sm) {
            SectionHeader(title: "知识库描述")

            TextEditor(text: $content)
                .frame(height: 120)
                .font(DesignSystem.Typography.content)
                .onChange(of: content) { _ in
                    debouncedCheckForChanges()
                }
                .scrollContentBackground(.hidden)
                .padding(DesignSystem.Spacing.sm)
                .background(DesignSystem.Colors.backgroundCard)
                .cornerRadius(DesignSystem.Rounded.md)
        }
    }

    // MARK: - 私有方法

    /// 检查是否有变更
    private func checkForChanges() {
        let trimmedTitle = title.trimmingCharacters(in: .whitespacesAndNewlines)
        let trimmedContent = content.trimmingCharacters(in: .whitespacesAndNewlines)

        hasChanges = !trimmedTitle.isEmpty || !trimmedContent.isEmpty
    }

    /// 防抖动检查变更
    private func debouncedCheckForChanges() {
        debounceTimer?.invalidate()

        debounceTimer = Timer.scheduledTimer(withTimeInterval: 0.3, repeats: false) { _ in
            checkForChanges()
        }
    }

    /// 处理返回操作
    private func handleBackAction() {
        if hasChanges {
            showExitConfirmation = true
        } else {
            navigator.pop()
        }
    }

    /// 保存分类
    private func saveCategory() {
        guard !title.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            errorMessage = "标题不能为空"
            showError = true
            return
        }

        isSaving = true

        Task {
            do {
                try await controller.createCategory(
                    title: title.trimmingCharacters(in: .whitespacesAndNewlines),
                    favicon: nil,
                    content: content.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty ? nil : content
                        .trimmingCharacters(in: .whitespacesAndNewlines)
                )

                await MainActor.run {
                    isSaving = false
                    hasChanges = false
                    ToastManager.shared.showSuccess("创建成功")

                    // 发送通知，让主页面刷新数据
                    NotificationCenter.default.post(name: .knowledgeCategoryUpdated, object: nil)

                    navigator.pop()
                }

            } catch {
                await MainActor.run {
                    isSaving = false

                    if let businessError = error as? BusinessError {
                        errorMessage = businessError.message
                    } else if let networkError = error as? NetworkError {
                        errorMessage = networkError.localizedDescription
                    } else {
                        errorMessage = error.localizedDescription
                    }

                    showError = true
                }
            }
        }
    }
}
