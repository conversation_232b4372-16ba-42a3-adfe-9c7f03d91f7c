import FlowStacks
import SwiftUI

struct PersonalKnowledgeBaseView: View {
    @ObservedObject private var filesController = KnowledgeBaseFilesController.personalInstance
    @EnvironmentObject var navigator: FlowPathNavigator

    var body: some View {
        PersonalKnowledgeFilesView(
            files: filesController.files,
            isLoading: filesController.isLoading,
            errorMessage: filesController.errorMessage,
            onRefresh: {
                try await filesController.performFilesRefresh()
            }
        )
        .navigationTitle("个人知识库")
        .onAppear {
            if filesController.files.isEmpty, !filesController.isLoading {
                filesController.loadFiles(loadType: .initial)
            }
        }
        .requireAuthenticationWithNavigation("请先登录以访问个人知识库") {
            navigator.pop()
        }
    }
}
