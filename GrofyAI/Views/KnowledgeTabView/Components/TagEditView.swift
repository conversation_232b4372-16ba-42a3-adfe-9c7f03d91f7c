import SwiftUI

// MARK: - 标签编辑视图

struct TagEditView: View {
    let file: KnowledgeFile
    @Binding var isPresented: Bool
    let onTagsUpdated: ([String]) -> Void

    @State private var tags: [String] = []
    @State private var originalTags: [String] = []
    @State private var newTagText = ""
    @State private var isLoading = false
    @State private var showError = false
    @State private var errorMessage = ""
    @State private var hasChanges = false
    @State private var showExitConfirmation = false

    @State private var sortedOriginalTags: [String] = []

    @State private var debounceTimer: Timer?

    private let fileService = FileService()

    // MARK: - 计算属性

    /// 是否有输入文本
    private var hasInputText: Bool {
        !newTagText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
    }

    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                fileInfoHeader

                Divider()
                    .background(DesignSystem.Colors.separator)

                addNewTagSection
                    .padding(DesignSystem.Spacing.lg)
                    .background(DesignSystem.Colors.backgroundPage)

                Divider()
                    .background(DesignSystem.Colors.separator)

                ScrollView {
                    scrollableTagContent
                        .padding(.bottom, DesignSystem.Spacing.xl)
                }
            }
            .background(DesignSystem.Colors.backgroundPage)
            .navigationTitle("")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .principal) {
                    Text("编辑标签")
                        .font(DesignSystem.Typography.navigationTitle)
                        .foregroundColor(DesignSystem.Colors.textPrimary)
                }
            }
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        handleCancelAction()
                    }
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    if isLoading {
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle(tint: DesignSystem.Colors.primary))
                            .scaleEffect(0.8)
                    } else {
                        Button("保存") {
                            saveTagsAction()
                        }
                        .fontWeight(.medium)
                        .disabled(!hasChanges || isLoading)
                        .foregroundColor(
                            hasChanges ? DesignSystem.Colors.primary : DesignSystem.Colors.textTertiary
                        )
                    }
                }
            }
        }
        .onAppear {
            let originalTagsArray = file.tags ?? []
            originalTags = originalTagsArray
            tags = originalTagsArray
            sortedOriginalTags = originalTagsArray.sorted()
            hasChanges = false
        }
        .alert("错误", isPresented: $showError) {
            Button("确定") {
                showError = false
            }
        } message: {
            Text(errorMessage)
        }
        .confirmationDialog(
            "确认退出",
            isPresented: $showExitConfirmation,
            titleVisibility: .visible
        ) {
            Button("保存并退出") {
                saveTagsAction()
            }
            Button("放弃修改", role: .destructive) {
                isPresented = false
            }
            Button("取消", role: .cancel) {}
        } message: {
            Text("您有未保存的修改，是否要保存？")
        }
        .onDisappear {
            // 清理防抖动Timer，避免内存泄漏
            debounceTimer?.invalidate()
            debounceTimer = nil
        }
    }

    // MARK: - 文件信息头部

    @ViewBuilder
    private var fileInfoHeader: some View {
        HStack(spacing: DesignSystem.Spacing.md) {
            let (iconName, iconColor) = file.typeIcon
            Image(systemName: iconName)
                .font(.title2)
                .foregroundColor(iconColor)
                .frame(width: 40, height: 40)
                .background(iconColor.opacity(0.1))
                .cornerRadius(DesignSystem.Rounded.sm)

            VStack(alignment: .leading, spacing: DesignSystem.Spacing.xs) {
                Text(file.title ?? "未知文件")
                    .font(DesignSystem.Typography.headline)
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                    .lineLimit(2)

                Text(file.typeDisplayName)
                    .font(DesignSystem.Typography.caption)
                    .foregroundColor(DesignSystem.Colors.textSecondary)
            }

            Spacer()
        }
        .padding(DesignSystem.Spacing.lg)
    }

    // MARK: - 可滚动的标签内容

    @ViewBuilder
    private var scrollableTagContent: some View {
        VStack(alignment: .leading, spacing: DesignSystem.Spacing.lg) {
            if !tags.isEmpty {
                existingTagsSection
            } else {
                emptyTagsPlaceholder
            }

            usageTipsSection
        }
        .padding(DesignSystem.Spacing.lg)
    }

    // MARK: - 空标签占位符

    @ViewBuilder
    private var emptyTagsPlaceholder: some View {
        EmptyStateView(
            title: "暂无标签",
            description: "输入标签名称，点击“添加”按钮来创建新标签",
            style: .compact
        )
        .padding(.vertical, DesignSystem.Spacing.lg)
    }

    // MARK: - 添加新标签区域

    @ViewBuilder
    private var addNewTagSection: some View {
        VStack(alignment: .leading, spacing: DesignSystem.Spacing.sm) {
            Text("名称")
                .font(DesignSystem.Typography.headline)
                .foregroundColor(DesignSystem.Colors.textPrimary)

            HStack(spacing: DesignSystem.Spacing.sm) {
                TextField("请输入", text: $newTagText)
                    .font(DesignSystem.Typography.content)
                    .padding(DesignSystem.Spacing.md)
                    .background(DesignSystem.Colors.backgroundCard)
                    .cornerRadius(DesignSystem.Rounded.md)
                    .onSubmit {
                        addNewTag()
                    }
                    .onChange(of: newTagText) { _ in
                        debouncedCheckForChanges()
                    }
                    .submitLabel(.done)
                    .autocorrectionDisabled()
                    .textInputAutocapitalization(.never)

                addTagButton
            }
        }
    }

    // MARK: - 添加标签按钮

    @ViewBuilder
    private var addTagButton: some View {
        Button(action: addNewTag) {
            Image(systemName: "plus.circle.fill")
                .font(.title2)
                .foregroundColor(hasInputText ? DesignSystem.Colors.primary : DesignSystem.Colors.textTertiary)
        }
        .disabled(!hasInputText)
        .buttonStyle(.plain)
        .contentShape(Rectangle())
    }

    // MARK: - 现有标签区域

    @ViewBuilder
    private var existingTagsSection: some View {
        VStack(alignment: .leading, spacing: DesignSystem.Spacing.sm) {
            Text("当前标签")
                .font(DesignSystem.Typography.headline)
                .foregroundColor(DesignSystem.Colors.textPrimary)

            FlowTagLayout(tags: tags, spacing: DesignSystem.Spacing.sm) { index, tag in
                tagChip(tag: tag, index: index)
            }
        }
    }

    // MARK: - 标签芯片

    @ViewBuilder
    private func tagChip(tag: String, index: Int) -> some View {
        HStack(spacing: DesignSystem.Spacing.xs) {
            Image(systemName: "tag")
                .fontXS(weight: DesignSystem.FontWeight.medium)
                .foregroundColor(DesignSystem.Colors.primary)

            Text(tag)
                .font(DesignSystem.Typography.content)
                .foregroundColor(DesignSystem.Colors.textPrimary)
                .lineLimit(1)
                .fixedSize()

            Button(action: {
                removeTag(at: index)
            }) {
                Image(systemName: "xmark.circle.fill")
                    .fontMD()
                    .foregroundColor(DesignSystem.Colors.textTertiary)
            }
            .buttonStyle(.plain)
            .contentShape(Circle())
        }
        .padding(.horizontal, DesignSystem.Spacing.md)
        .padding(.vertical, DesignSystem.Spacing.sm)
        .background(DesignSystem.Colors.primary.opacity(0.1))
        .cornerRadius(DesignSystem.Rounded.lg)
    }

    // MARK: - 使用提示区域

    @ViewBuilder
    private var usageTipsSection: some View {
        VStack(alignment: .leading, spacing: DesignSystem.Spacing.md) {
            HStack(spacing: DesignSystem.Spacing.sm) {
                Image(systemName: "lightbulb")
                    .fontLG(weight: DesignSystem.FontWeight.medium)
                    .foregroundColor(DesignSystem.Colors.info)

                Text("使用提示")
                    .font(DesignSystem.Typography.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(DesignSystem.Colors.textPrimary)
            }

            VStack(alignment: .leading, spacing: DesignSystem.Spacing.sm) {
                tipItem(
                    icon: "folder.badge.plus",
                    text: "标签可以帮助您更好地组织和查找文件"
                )
                tipItem(
                    icon: "textformat.size",
                    text: "每个标签最多20个字符"
                )
                tipItem(
                    icon: "key",
                    text: "建议使用简洁明了的关键词"
                )
                tipItem(
                    icon: "xmark.circle",
                    text: "点击标签右侧的 × 可以删除标签"
                )
            }
        }
        .padding(DesignSystem.Spacing.lg)
        .background(
            RoundedRectangle(cornerRadius: DesignSystem.Rounded.lg)
                .fill(DesignSystem.Colors.backgroundCard)
                .overlay(
                    RoundedRectangle(cornerRadius: DesignSystem.Rounded.lg)
                        .stroke(DesignSystem.Colors.info.opacity(0.2), lineWidth: 1)
                )
        )
    }

    @ViewBuilder
    private func tipItem(icon: String, text: String) -> some View {
        HStack(spacing: DesignSystem.Spacing.sm) {
            Image(systemName: icon)
                .fontSM(weight: DesignSystem.FontWeight.medium)
                .foregroundColor(DesignSystem.Colors.info)
                .frame(width: 16, height: 16)

            Text(text)
                .font(DesignSystem.Typography.caption)
                .foregroundColor(DesignSystem.Colors.textSecondary)
                .fixedSize(horizontal: false, vertical: true)

            Spacer(minLength: 0)
        }
    }

    // MARK: - 私有方法

    /// 添加新标签
    private func addNewTag() {
        let trimmedTag = newTagText.trimmingCharacters(in: .whitespacesAndNewlines)

        // 验证标签
        guard !trimmedTag.isEmpty else { return }
        guard trimmedTag.count <= 20 else {
            showErrorMessage("标签长度不能超过20个字符")
            return
        }
        guard !tags.contains(trimmedTag) else {
            showErrorMessage("标签已存在")
            return
        }

        // 添加标签
        tags.append(trimmedTag)
        newTagText = ""
        checkForChanges()
    }

    /// 显示错误信息
    private func showErrorMessage(_ message: String) {
        errorMessage = message
        showError = true
    }

    /// 删除标签
    private func removeTag(at index: Int) {
        guard index < tags.count else { return }
        tags.remove(at: index)
        checkForChanges()
    }

    /// 检查是否有变更
    private func checkForChanges() {
        let sortedCurrentTags = tags.sorted()
        let tagsChanged = sortedCurrentTags != sortedOriginalTags

        let hasUncommittedInput = hasInputText

        hasChanges = tagsChanged || hasUncommittedInput
    }

    /// 防抖动检查变更
    private func debouncedCheckForChanges() {
        // 取消之前的Timer
        debounceTimer?.invalidate()

        // 设置新的延迟Timer
        debounceTimer = Timer.scheduledTimer(withTimeInterval: 0.3, repeats: false) { _ in
            checkForChanges()
        }
    }

    /// 处理取消操作
    private func handleCancelAction() {
        if hasChanges {
            showExitConfirmation = true
        } else {
            isPresented = false
        }
    }

    /// 提交输入框中的待处理内容
    private func commitPendingInputIfValid() {
        let trimmedTag = newTagText.trimmingCharacters(in: .whitespacesAndNewlines)

        guard !trimmedTag.isEmpty else { return }
        guard trimmedTag.count <= 20 else { return }
        guard !tags.contains(trimmedTag) else { return }

        tags.append(trimmedTag)
        newTagText = ""
    }

    /// 保存标签
    private func saveTagsAction() {
        guard !isLoading else { return }

        commitPendingInputIfValid()

        isLoading = true

        Task {
            do {
                try await fileService.updateKnowledgeFileTags(
                    fileId: file.id,
                    tags: tags
                )

                await MainActor.run {
                    isLoading = false
                    hasChanges = false
                    onTagsUpdated(tags)
                    isPresented = false
                }

            } catch {
                await MainActor.run {
                    isLoading = false

                    if let businessError = error as? BusinessError {
                        errorMessage = "保存失败: \(businessError.message)"
                    } else if let networkError = error as? NetworkError {
                        errorMessage = "网络错误: \(networkError.localizedDescription)"
                    } else {
                        errorMessage = "保存失败: \(error.localizedDescription)"
                    }

                    showError = true
                }
            }
        }
    }
}

// MARK: - 智能流式标签布局

struct FlowTagLayout<Content: View>: View {
    let tags: [String]
    let spacing: CGFloat
    let content: (Int, String) -> Content

    init(tags: [String], spacing: CGFloat = 8, @ViewBuilder content: @escaping (Int, String) -> Content) {
        self.tags = tags
        self.spacing = spacing
        self.content = content
    }

    var body: some View {
        LazyVStack(alignment: .leading, spacing: spacing) {
            ForEach(computeRows(), id: \.self) { row in
                HStack(spacing: spacing) {
                    ForEach(row, id: \.self) { index in
                        content(index, tags[index])
                    }
                    Spacer(minLength: 0)
                }
            }
        }
    }

    private func computeRows() -> [[Int]] {
        var rows: [[Int]] = []
        var currentRow: [Int] = []
        var currentRowWidth: CGFloat = 0

        let screenWidth = UIScreen.main.bounds.width
        let safeAreaInsets: CGFloat = 32 // 左右安全区域
        let scrollViewPadding: CGFloat = 32 // ScrollView的padding
        let availableWidth = screenWidth - safeAreaInsets - scrollViewPadding

        for (index, tag) in tags.enumerated() {
            let tagWidth = calculateTagWidth(for: tag)
            let itemSpacing = currentRow.isEmpty ? 0 : spacing

            let requiredWidth = currentRowWidth + itemSpacing + tagWidth

            if requiredWidth <= availableWidth - 16 { // 额外16pt缓冲空间
                currentRow.append(index)
                currentRowWidth += itemSpacing + tagWidth
            } else {
                // 如果当前行为空但单个标签仍然太宽，强制添加以避免无限循环
                if currentRow.isEmpty {
                    currentRow.append(index)
                    currentRowWidth = tagWidth
                } else {
                    rows.append(currentRow)
                    currentRow = [index]
                    currentRowWidth = tagWidth
                }
            }
        }

        if !currentRow.isEmpty {
            rows.append(currentRow)
        }

        return rows.isEmpty ? [[]] : rows
    }

    private func calculateTagWidth(for tag: String) -> CGFloat {
        let font = UIFont.systemFont(ofSize: 16)
        let textSize = (tag as NSString).size(withAttributes: [.font: font])

        // 根据tagChip的实际实现计算宽度：
        // - 标签图标宽度：10pt (fontXS)
        // - 文字宽度（已计算）
        // - HStack内部间距：DesignSystem.Spacing.xs = 4pt * 2 = 8pt (图标-文字, 文字-删除按钮)
        // - 删除按钮图标：14pt
        // - 左右padding：DesignSystem.Spacing.md = 16pt * 2 = 32pt

        let textWidth = textSize.width
        let tagIconWidth: CGFloat = 10
        let internalSpacing: CGFloat = 8 // 两个间距
        let deleteButtonWidth: CGFloat = 14
        let horizontalPadding: CGFloat = 32

        let bufferSpace: CGFloat = 8

        return tagIconWidth + textWidth + internalSpacing + deleteButtonWidth + horizontalPadding + bufferSpace
    }
}
