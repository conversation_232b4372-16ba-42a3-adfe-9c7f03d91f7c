import SwiftUI

/// 知识库分类选择视图
/// 用于文件移动时选择目标知识库分类
struct KnowledgeCategorySelectionView: View {
    // MARK: - 属性

    let fileToMove: KnowledgeFile
    let currentCategoryId: Int?
    let sourceCategory: KnowledgeCategory?
    let onCategorySelected: (KnowledgeCategory) -> Void
    let onCancel: () -> Void

    @StateObject private var categoryController = KnowledgeTabController()
    // TODO: 应该使用共享的KnowledgeTabController实例而不是创建新实例
    // 这违反了当前的共享控制器架构，但由于该组件可能在多个上下文中使用，
    // 暂时保持独立实例，未来需要重构为使用环境对象

    @EnvironmentObject var filesController: KnowledgeBaseFilesController

    @State private var searchText = ""

    @State private var selectedTargetCategory: KnowledgeCategory?

    // MARK: - 计算属性

    /// 过滤后的分类列表
    private var filteredCategories: [KnowledgeCategory] {
        var allCategories = categoryController.categories

        let shouldIncludePersonalKnowledgeBase: Bool = {
            if let currentCategoryId, currentCategoryId == 0 {
                return false
            }
            return true
        }()

        if shouldIncludePersonalKnowledgeBase {
            allCategories.insert(KnowledgeCategory.personalKnowledgeBase, at: 0)
        }

        let availableCategories = allCategories.filter { category in
            if let currentCategoryId {
                return category.id != currentCategoryId
            }
            return true
        }

        if searchText.isEmpty {
            return availableCategories
        } else {
            return availableCategories.filter { category in
                category.title.localizedCaseInsensitiveContains(searchText) ||
                    (category.content?.localizedCaseInsensitiveContains(searchText) ?? false)
            }
        }
    }

    // MARK: - 视图主体

    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                moveInfoSection

                searchBarView

                categoryListView
            }
            .background(DesignSystem.Colors.backgroundPage)
            .navigationTitle("移动文件")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        onCancel()
                    }
                    .foregroundColor(filesController.isMovingFile ?
                        DesignSystem.Colors.disabled : DesignSystem.Colors.textSecondary
                    )
                    .disabled(filesController.isMovingFile)
                }
            }
        }
        .task {
            await categoryController.loadKnowledgeCategoriesIfNeeded()
        }
    }

    // MARK: - 搜索栏视图

    @ViewBuilder
    private var searchBarView: some View {
        VStack(spacing: 0) {
            HStack(spacing: DesignSystem.Spacing.md) {
                Image(systemName: "magnifyingglass")
                    .foregroundColor(DesignSystem.Colors.textSecondary)
                    .fontSM()

                TextField("搜索知识库", text: $searchText)
                    .bodyCompactFixedStyle()
                    .foregroundColor(filesController.isMovingFile ?
                        DesignSystem.Colors.disabled : DesignSystem.Colors.textPrimary
                    )
                    .disabled(filesController.isMovingFile)

                if !searchText.isEmpty {
                    Button(action: {
                        searchText = ""
                    }) {
                        Image(systemName: "xmark.circle.fill")
                            .foregroundColor(filesController.isMovingFile ?
                                DesignSystem.Colors.disabled : DesignSystem.Colors.textSecondary
                            )
                            .fontSM()
                    }
                    .buttonStyle(.plain)
                    .disabled(filesController.isMovingFile)
                }
            }
            .padding(DesignSystem.Spacing.md)
            .background(DesignSystem.Colors.backgroundCard)
            .cornerRadius(DesignSystem.Rounded.md)
            .padding(.horizontal, DesignSystem.Spacing.lg)
            .padding(.vertical, DesignSystem.Spacing.md)

            // 分隔线
            Rectangle()
                .fill(DesignSystem.Colors.border)
                .frame(height: DesignSystem.BorderWidth.thin)
        }
    }

    // MARK: - 分类列表视图

    @ViewBuilder
    private var categoryListView: some View {
        if categoryController.isLoading {
            loadingView
        } else if filteredCategories.isEmpty {
            emptyStateView
        } else {
            ScrollView {
                LazyVStack(spacing: DesignSystem.Spacing.md) {
                    ForEach(filteredCategories) { category in
                        CategorySelectionRowView(
                            category: category,
                            isSelected: selectedTargetCategory?.id == category.id,
                            isMoving: filesController.isMovingFile && selectedTargetCategory?.id == category.id,
                            onTap: {
                                selectedTargetCategory = category
                            },
                            onConfirm: {
                                onCategorySelected(category)
                            }
                        )
                    }
                }
                .padding(.horizontal, DesignSystem.Spacing.lg)
                .padding(.vertical, DesignSystem.Spacing.md)
            }
        }
    }

    // MARK: - 加载状态视图

    @ViewBuilder
    private var loadingView: some View {
        VStack(spacing: DesignSystem.Spacing.lg) {
            ProgressView()
                .scaleEffect(1.2)
                .tint(DesignSystem.Colors.primary)

            Text("加载知识库列表...")
                .captionStyle()
                .foregroundColor(DesignSystem.Colors.textSecondary)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(DesignSystem.Colors.backgroundPage)
    }

    // MARK: - 空状态视图

    @ViewBuilder
    private var emptyStateView: some View {
        EmptyStateView(
            title: searchText.isEmpty ? "暂无可用知识库" : "未找到匹配的知识库",
            description: searchText.isEmpty ? nil : "尝试使用其他关键词搜索",
            style: .fullScreen
        )
        .background(DesignSystem.Colors.backgroundPage)
    }

    // MARK: - 文件移动信息展示区域

    @ViewBuilder
    private var moveInfoSection: some View {
        VStack(spacing: DesignSystem.Spacing.md) {
            compactFileInfoView

            moveRelationshipIndicator
        }
        .padding(.horizontal, DesignSystem.Spacing.lg)
        .padding(.vertical, DesignSystem.Spacing.md)
        .background(DesignSystem.Colors.backgroundPage)
    }

    // MARK: - 简化的文件信息展示

    @ViewBuilder
    private var compactFileInfoView: some View {
        HStack(spacing: DesignSystem.Spacing.md) {
            let (iconName, iconColor) = fileToMove.typeIcon
            Image(systemName: iconName)
                .font(.title3)
                .foregroundColor(iconColor)
                .frame(width: 32, height: 32)
                .background(iconColor.opacity(0.1))
                .cornerRadius(DesignSystem.Rounded.sm)

            VStack(alignment: .leading, spacing: DesignSystem.Spacing.xs) {
                Text(fileToMove.title ?? "未知文件")
                    .font(DesignSystem.Typography.body)
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                    .lineLimit(1)

                Text("\(fileToMove.formattedSize) • \(fileToMove.formattedCreateDate)")
                    .font(DesignSystem.Typography.caption)
                    .foregroundColor(DesignSystem.Colors.textTertiary)
            }

            Spacer()
        }
        .padding(.vertical, DesignSystem.Spacing.sm)
    }

    // MARK: - 简化的移动关系指示器

    @ViewBuilder
    private var moveRelationshipIndicator: some View {
        HStack(spacing: DesignSystem.Spacing.lg) {
            sourceKnowledgeCompactView

            Image(systemName: "arrow.right")
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(DesignSystem.Colors.textTertiary)

            targetKnowledgePlaceholder
        }
        .padding(.horizontal, DesignSystem.Spacing.md)
    }

    // MARK: - 简化的源知识库视图

    @ViewBuilder
    private var sourceKnowledgeCompactView: some View {
        HStack(spacing: DesignSystem.Spacing.xs) {
            let sourceKnowledgeBase: KnowledgeCategory = {
                if let sourceCategory {
                    return sourceCategory
                } else {
                    // 如果没有源知识库信息，根据文件的categoryId推断
                    if let fileCategoryId = fileToMove.categoryId, fileCategoryId == 0 {
                        return KnowledgeCategory.personalKnowledgeBase
                    } else {
                        // 尝试从文件的categoryId查找知识库
                        if let fileCategoryId = fileToMove.categoryId,
                           let foundCategory = categoryController.categories.first(where: { $0.id == fileCategoryId })
                        {
                            return foundCategory
                        } else {
                            // 最后的回退选项
                            return KnowledgeCategory.personalKnowledgeBase
                        }
                    }
                }
            }()

            Image(systemName: sourceKnowledgeBase.displayIconName)
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(DesignSystem.Colors.textTertiary)

            Text(sourceKnowledgeBase.title)
                .font(DesignSystem.Typography.caption)
                .foregroundColor(DesignSystem.Colors.textTertiary)
                .lineLimit(1)
        }
    }

    // MARK: - 目标知识库占位符

    @ViewBuilder
    private var targetKnowledgePlaceholder: some View {
        HStack(spacing: DesignSystem.Spacing.xs) {
            if let selectedTargetCategory {
                Image(systemName: selectedTargetCategory.displayIconName)
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(selectedTargetCategory.displayIconColor)

                Text(selectedTargetCategory.title)
                    .font(DesignSystem.Typography.caption)
                    .foregroundColor(selectedTargetCategory.displayIconColor)
                    .lineLimit(1)
            } else {
                Text("待选择新知识库")
                    .font(DesignSystem.Typography.caption)
                    .foregroundColor(DesignSystem.Colors.textTertiary)
            }
        }
    }
}

// MARK: - 分类选择行视图

private struct CategorySelectionRowView: View {
    let category: KnowledgeCategory
    let isSelected: Bool
    let isMoving: Bool
    let onTap: () -> Void
    let onConfirm: () -> Void

    @State private var isPressed = false

    var body: some View {
        Button(action: isMoving ? {} : onTap) {
            HStack(spacing: DesignSystem.Spacing.md) {
                Image(systemName: category.displayIconName)
                    .font(.system(size: 20, weight: .medium))
                    .foregroundColor(category.displayIconColor)
                    .frame(width: 32, height: 32)

                VStack(alignment: .leading, spacing: DesignSystem.Spacing.xs) {
                    Text(category.title)
                        .cardTitleStyle()
                        .foregroundColor(DesignSystem.Colors.textPrimary)
                        .lineLimit(1)

                    if !category.contentSummary.isEmpty, category.contentSummary != "暂无描述" {
                        Text(category.contentSummary)
                            .captionStyle()
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                            .lineLimit(2)
                    }
                }

                Spacer()

                if isSelected {
                    Button(action: onConfirm) {
                        HStack(spacing: DesignSystem.Spacing.xs) {
                            if isMoving {
                                ProgressView()
                                    .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                    .scaleEffect(0.8)
                                    .frame(width: 12, height: 12)
                            } else {
                                Image(systemName: "checkmark")
                                    .font(.system(size: 12, weight: .bold))
                                    .foregroundColor(.white)
                            }

                            Text(isMoving ? "移动中..." : "确认移动")
                                .font(.system(size: 12, weight: .medium))
                                .foregroundColor(.white)
                        }
                        .padding(.horizontal, DesignSystem.Spacing.sm)
                        .padding(.vertical, DesignSystem.Spacing.xs)
                        .background(isMoving ? DesignSystem.Colors.primary.opacity(0.7) : DesignSystem.Colors.primary)
                        .cornerRadius(DesignSystem.Rounded.sm)
                    }
                    .buttonStyle(.plain)
                    .disabled(isMoving)
                } else {
                    Image(systemName: "chevron.right")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(DesignSystem.Colors.textTertiary)
                }
            }
            .padding(DesignSystem.Spacing.md)
            .background(
                isSelected
                    ? DesignSystem.Colors.primary.opacity(0.1)
                    : DesignSystem.Colors.backgroundCard
            )
            .cornerRadius(DesignSystem.Rounded.md)
            .overlay(
                RoundedRectangle(cornerRadius: DesignSystem.Rounded.md)
                    .stroke(
                        isSelected
                            ? DesignSystem.Colors.primary.opacity(0.3)
                            : Color.clear,
                        lineWidth: 1
                    )
            )
            .scaleEffect(isPressed ? 0.98 : 1.0)
            .opacity(isPressed ? 0.9 : 1.0)
        }
        .buttonStyle(.plain)
        .onLongPressGesture(
            minimumDuration: 0,
            maximumDistance: .infinity,
            pressing: { pressing in
                withAnimation(.easeInOut(duration: 0.1)) {
                    isPressed = pressing
                }
            },
            perform: {}
        )
    }
}
