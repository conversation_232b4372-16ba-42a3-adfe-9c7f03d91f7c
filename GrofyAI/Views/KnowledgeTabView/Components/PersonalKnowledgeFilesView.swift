import FlowStacks
import SwiftUI

// MARK: - 个人知识库文件视图

struct PersonalKnowledgeFilesView: View {
    let files: [KnowledgeFile]
    let isLoading: Bool
    let errorMessage: String?
    let onRefresh: () async throws -> Void

    @EnvironmentObject var navigator: FlowPathNavigator
    @State private var showLoadMoreButton = false
    @State private var tagUpdateTasks: [String: Task<Void, Never>] = [:]

    @ObservedObject private var personalFilesController = KnowledgeBaseFilesController.personalInstance

    var body: some View {
        VStack(spacing: 0) {
            contentView
        }
        .hideNavigationBarLine(backgroundColor: DesignSystem.Colors.backgroundPage)
        .background(DesignSystem.Colors.backgroundPage)
        .navigationBarBackButtonHidden()
        .navigationBarTitleDisplayMode(.inline)
        .toolbar {
            ToolbarItem(placement: .topBarLeading) {
                BackButton(onBack: { navigator.pop() })
            }
            ToolbarItem(placement: .principal) {
                Text("个人知识库")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.textPrimary)
            }
        }
        .nativeStyleRefreshable {
            do {
                try await onRefresh()
            } catch {
                ToastManager.shared.showError("刷新失败：\(error.localizedDescription)")
            }
        }
        .onDisappear {
            for task in tagUpdateTasks.values {
                task.cancel()
            }
            tagUpdateTasks.removeAll()
        }
        .sheet(isPresented: $personalFilesController.showCategorySelection) {
            if let fileToMove = personalFilesController.fileToMove {
                KnowledgeCategorySelectionView(
                    fileToMove: fileToMove,
                    currentCategoryId: 0,
                    sourceCategory: personalFilesController.sourceCategory,
                    onCategorySelected: { targetCategory in
                        personalFilesController.moveFile(fileToMove, to: targetCategory)
                    },
                    onCancel: {
                        personalFilesController.cancelMoveFile()
                    }
                )
                .environmentObject(personalFilesController)
            }
        }
    }

    // MARK: - 内容视图

    @ViewBuilder
    private var contentView: some View {
        if isLoading, files.isEmpty {
            loadingView
        } else if let errorMessage, files.isEmpty {
            errorView(errorMessage)
        } else if files.isEmpty {
            emptyStateView
        } else {
            fileListView
        }
    }

    // MARK: - 加载状态视图

    @ViewBuilder
    private var loadingView: some View {
        ScrollView {
            LazyVStack(spacing: DesignSystem.Spacing.md) {
                ForEach(0..<5, id: \.self) { _ in
                    SkeletonFileRowView()
                }
            }
            .padding()
        }
        .redacted(reason: .placeholder)
    }

    // MARK: - 错误状态视图

    @ViewBuilder
    private func errorView(_ message: String) -> some View {
        VStack(spacing: DesignSystem.Spacing.lg) {
            Spacer()

            Image(systemName: "exclamationmark.triangle")
                .font(.system(size: 48))
                .foregroundColor(.orange)

            Text("加载失败")
                .font(DesignSystem.Typography.headline)
                .foregroundColor(DesignSystem.Colors.textPrimary)

            Text(message)
                .font(DesignSystem.Typography.body)
                .foregroundColor(DesignSystem.Colors.textSecondary)
                .multilineTextAlignment(.center)
                .padding(.horizontal, DesignSystem.Spacing.xl)

            Button("重试") {
                Task {
                    do {
                        try await onRefresh()
                    } catch {
                        ToastManager.shared.showError("重试失败：\(error.localizedDescription)")
                    }
                }
            }
            .buttonStyle(.borderedProminent)

            Spacer()
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }

    // MARK: - 空状态视图

    @ViewBuilder
    private var emptyStateView: some View {
        EmptyStateView(
            title: "暂无文件",
            description: "这里将显示您在所有知识库中上传的文件",
            style: .fullScreen
        )
    }

    // MARK: - 文件列表视图

    @ViewBuilder
    private var fileListView: some View {
        ScrollView {
            LazyVStack(spacing: DesignSystem.Spacing.md) {
                ForEach(files) { file in
                    KnowledgeFileRowView(
                        file: file,
                        isSelectionMode: false,
                        isSelected: false,
                        onTap: {
                            handleFileTap(file)
                        },
                        onTagsUpdated: { updatedTags in
                            let taskKey = "tag_update_\(file.id)"
                            tagUpdateTasks[taskKey]?.cancel()

                            tagUpdateTasks[taskKey] = Task {
                                do {
                                    try await personalFilesController.updateFileTags(fileId: file.id, tags: updatedTags)
                                    DispatchQueue.main.async {
                                        ToastManager.shared.showSuccess("更新成功")
                                    }
                                } catch {
                                    if !Task.isCancelled {
                                        DispatchQueue.main.async {
                                            ToastManager.shared.showError("更新失败")
                                        }
                                    }
                                }
                                DispatchQueue.main.async {
                                    tagUpdateTasks.removeValue(forKey: taskKey)
                                }
                            }
                        },
                        onMoveFile: { fileToMove in
                            Task {
                                await personalFilesController.startMoveFile(fileToMove)
                            }
                        }
                    )
                }

                if files.count >= 5 {
                    loadMoreHintView
                }

                completionHintView
            }
            .padding()
        }
    }

    // MARK: - 查看更多提示

    @ViewBuilder
    private var loadMoreHintView: some View {
        VStack(spacing: DesignSystem.Spacing.sm) {
            Divider()
                .background(DesignSystem.Colors.separator)

            HStack {
                Image(systemName: "info.circle")
                    .foregroundColor(DesignSystem.Colors.primary)

                Text("这里只显示最近的5个文件")
                    .font(DesignSystem.Typography.caption)
                    .foregroundColor(DesignSystem.Colors.textSecondary)

                Spacer()

                Button("查看全部") {
                    navigator.push(Route.knowledgeBaseFiles(categoryId: 0))
                }
                .font(DesignSystem.Typography.caption)
                .foregroundColor(DesignSystem.Colors.primary)
            }
            .padding(.vertical, DesignSystem.Spacing.sm)
        }
    }

    // MARK: - 完成提示

    @ViewBuilder
    private var completionHintView: some View {
        HStack {
            Spacer()
            Text("已显示 \(files.count) 个文件")
                .font(DesignSystem.Typography.caption)
                .foregroundColor(DesignSystem.Colors.textTertiary)
            Spacer()
        }
        .padding(.top, DesignSystem.Spacing.md)
    }

    // MARK: - 事件处理

    private func handleFileTap(_ file: KnowledgeFile) {
        // 检查文件状态是否就绪
        guard file.isReady, file.isVectorReady else {
            ToastManager.shared.showError("文件正在处理中，请稍后再试")
            return
        }

        // 跳转到文件对话界面
        navigator.push(Route.fileChat(
            knowledgeId: file.id,
            fileName: file.title,
            initialMessage: nil,
            threadId: file.threadId
        ))
    }
}
