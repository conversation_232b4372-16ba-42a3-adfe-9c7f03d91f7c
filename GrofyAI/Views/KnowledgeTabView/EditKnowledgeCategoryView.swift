import SwiftUI

// MARK: - 编辑知识库视图

struct EditKnowledgeCategoryView: View {
    @Environment(\.dismiss) private var dismiss
    @ObservedObject var controller: KnowledgeTabController

    let category: KnowledgeCategory

    @State private var title: String
    @State private var content: String
    @State private var isLoading = false
    @State private var showError = false
    @State private var errorMessage = ""

    init(category: KnowledgeCategory, controller: KnowledgeTabController) {
        self.category = category
        self.controller = controller
        _title = State(initialValue: category.title)
        _content = State(initialValue: category.content ?? "")
    }

    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                formContent

                Spacer()

                saveButton
            }
            .padding()
            .background(DesignSystem.Colors.backgroundPage)
            .navigationTitle("编辑知识库")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        dismiss()
                    }
                }
            }
            .onDisappear {
                ToastManager.shared.clearToast()
            }
        }
        .alert("错误", isPresented: $showError) {
            But<PERSON>("确定") {
                showError = false
            }
        } message: {
            Text(errorMessage)
        }
    }

    // MARK: - 表单内容

    @ViewBuilder
    private var formContent: some View {
        VStack(spacing: DesignSystem.Spacing.lg) {
            titleInputCard

            descriptionInputCard
        }
    }

    // MARK: - 标题输入卡片

    @ViewBuilder
    private var titleInputCard: some View {
        VStack(alignment: .leading, spacing: DesignSystem.Spacing.sm) {
            SectionHeader(title: "标题")

            TextField("请输入标题", text: $title)
                .font(DesignSystem.Typography.content)
                .padding(DesignSystem.Spacing.md)
                .background(DesignSystem.Colors.backgroundCard)
                .cornerRadius(DesignSystem.Rounded.md)
        }
    }

    // MARK: - 描述输入卡片

    @ViewBuilder
    private var descriptionInputCard: some View {
        VStack(alignment: .leading, spacing: DesignSystem.Spacing.sm) {
            SectionHeader(title: "描述信息")

            TextEditor(text: $content)
                .frame(height: 120)
                .font(DesignSystem.Typography.content)
                .padding(DesignSystem.Spacing.sm)
                .background(DesignSystem.Colors.backgroundCard)
                .cornerRadius(DesignSystem.Rounded.md)
        }
    }

    // MARK: - 保存按钮

    @ViewBuilder
    private var saveButton: some View {
        Button(action: saveCategory) {
            HStack {
                if isLoading {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                        .scaleEffect(0.8)
                }

                Text(isLoading ? "保存中..." : "保存")
                    .font(DesignSystem.Typography.cardTitle)
                    .foregroundColor(.white)
            }
            .frame(maxWidth: .infinity)
            .frame(height: 50)
            .background(
                title.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty || isLoading
                    ? DesignSystem.Colors.textTertiary
                    : DesignSystem.Colors.primary
            )
            .cornerRadius(DesignSystem.Rounded.md)
        }
        .disabled(title.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty || isLoading)
    }

    // MARK: - 保存知识库

    private func saveCategory() {
        guard !title.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            return
        }

        isLoading = true

        Task {
            do {
                try await controller.updateCategory(
                    id: category.id,
                    title: title.trimmingCharacters(in: .whitespacesAndNewlines),
                    favicon: nil, // 不再支持图标编辑
                    content: content.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty ? nil : content
                        .trimmingCharacters(in: .whitespacesAndNewlines)
                )

                await MainActor.run {
                    isLoading = false
                    ToastManager.shared.showSuccess("更新成功")

                    NotificationCenter.default.post(name: .knowledgeCategoryUpdated, object: nil)

                    dismiss()
                }

            } catch {
                await MainActor.run {
                    isLoading = false

                    if let businessError = error as? BusinessError {
                        errorMessage = businessError.message
                    } else if let networkError = error as? NetworkError {
                        errorMessage = networkError.localizedDescription
                    } else {
                        errorMessage = error.localizedDescription
                    }

                    showError = true
                }
            }
        }
    }
}
