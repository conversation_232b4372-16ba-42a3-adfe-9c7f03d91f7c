import FlowStacks
import SwiftUI

// MARK: - 编辑知识库分类页面

struct EditKnowledgeCategoryScreen: View {
    @EnvironmentObject var controller: KnowledgeTabController
    @EnvironmentObject var navigator: FlowPathNavigator

    let categoryId: Int

    @State private var category: KnowledgeCategory?
    @State private var title = ""
    @State private var content = ""
    @State private var isLoading = false
    @State private var isSaving = false
    @State private var showError = false
    @State private var errorMessage = ""
    @State private var hasChanges = false
    @State private var showExitConfirmation = false
    @State private var debounceTimer: Timer?

    private var canSave: Bool {
        !title.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
    }

    var body: some View {
        VStack(spacing: 0) {
            if category != nil {
                formContent
                    .padding(.horizontal, DesignSystem.Spacing.lg)
                    .padding(.top, DesignSystem.Spacing.lg)
            } else if isLoading {
                loadingView
            } else {
                errorView
            }

            Spacer()
        }
        .hideNavigationBarLine(backgroundColor: DesignSystem.Colors.backgroundPage)
        .background(DesignSystem.Colors.backgroundPage)
        .navigationBarBackButtonHidden()
        .navigationBarTitleDisplayMode(.inline)
        .toolbar {
            ToolbarItem(placement: .topBarLeading) {
                BackButton(onBack: handleBackAction)
            }
            ToolbarItem(placement: .topBarTrailing) {
                LoadingButton(
                    isLoading: isSaving,
                    text: "保存",
                    isEnabled: hasChanges && canSave,
                    onTap: saveCategory
                )
            }
            ToolbarItem(placement: .principal) {
                Text("编辑知识库")
                    .font(.system(size: DesignSystem.FontSize.lg, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.textPrimary)
            }
        }
        .task {
            await loadCategoryData()
        }
        .alert("错误", isPresented: $showError) {
            Button("确定") {
                showError = false
            }
        } message: {
            Text(errorMessage)
        }
        .confirmationDialog(
            "确认退出",
            isPresented: $showExitConfirmation,
            titleVisibility: .visible
        ) {
            Button("保存并退出") {
                saveCategory()
            }
            Button("放弃修改", role: .destructive) {
                navigator.pop()
            }
            Button("取消", role: .cancel) {}
        } message: {
            Text("您有未保存的修改，是否要保存？")
        }
        .onDisappear {
            debounceTimer?.invalidate()
            debounceTimer = nil
        }
    }

    // MARK: - 表单内容

    @ViewBuilder
    private var formContent: some View {
        VStack(spacing: DesignSystem.Spacing.lg) {
            titleInputCard

            descriptionInputCard
        }
    }

    // MARK: - 标题输入卡片

    @ViewBuilder
    private var titleInputCard: some View {
        VStack(alignment: .leading, spacing: DesignSystem.Spacing.sm) {
            SectionHeader(title: "标题")

            TextField("请输入标题", text: $title)
                .font(DesignSystem.Typography.content)
                .onChange(of: title) { _ in
                    debouncedCheckForChanges()
                }
                .padding(DesignSystem.Spacing.md)
                .background(DesignSystem.Colors.backgroundCard)
                .cornerRadius(DesignSystem.Rounded.md)
        }
    }

    // MARK: - 描述输入卡片

    @ViewBuilder
    private var descriptionInputCard: some View {
        VStack(alignment: .leading, spacing: DesignSystem.Spacing.sm) {
            SectionHeader(title: "描述信息")

            TextEditor(text: $content)
                .frame(height: 120)
                .font(DesignSystem.Typography.content)
                .onChange(of: content) { _ in
                    debouncedCheckForChanges()
                }
                .padding(DesignSystem.Spacing.sm)
                .background(DesignSystem.Colors.backgroundCard)
                .cornerRadius(DesignSystem.Rounded.md)
        }
    }

    // MARK: - 加载视图

    @ViewBuilder
    private var loadingView: some View {
        VStack(spacing: DesignSystem.Spacing.lg) {
            ProgressView()
                .progressViewStyle(CircularProgressViewStyle(tint: DesignSystem.Colors.primary))
                .scaleEffect(1.2)

            Text("加载中...")
                .font(DesignSystem.Typography.content)
                .foregroundColor(DesignSystem.Colors.textSecondary)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }

    // MARK: - 错误视图

    @ViewBuilder
    private var errorView: some View {
        VStack(spacing: DesignSystem.Spacing.lg) {
            Image(systemName: "exclamationmark.triangle")
                .font(.system(size: 48))
                .foregroundColor(DesignSystem.Colors.textTertiary)

            Text("加载失败")
                .font(DesignSystem.Typography.headline)
                .foregroundColor(DesignSystem.Colors.textPrimary)

            Button(action: {
                Task {
                    await loadCategoryData()
                }
            }) {
                Text("重试")
                    .font(DesignSystem.Typography.cardTitle)
                    .foregroundColor(DesignSystem.Colors.primary)
                    .padding(.horizontal, DesignSystem.Spacing.lg)
                    .padding(.vertical, DesignSystem.Spacing.sm)
                    .background(DesignSystem.Colors.backgroundCard)
                    .cornerRadius(DesignSystem.Rounded.sm)
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }

    // MARK: - 方法

    /// 加载分类数据
    private func loadCategoryData() async {
        // 如果controller中已有数据，直接使用
        if !controller.categories.isEmpty {
            loadCategory()
            return
        }

        // 否则先加载分类列表
        isLoading = true

        await controller.loadKnowledgeCategories()

        isLoading = false

        // 检查是否有错误或数据为空
        if let errorMessage = controller.errorMessage {
            self.errorMessage = "加载知识库数据失败: \(errorMessage)"
            showError = true
        } else if controller.categories.isEmpty {
            errorMessage = "未找到任何知识库数据"
            showError = true
        } else {
            loadCategory()
        }
    }

    /// 从controller中查找分类数据
    private func loadCategory() {
        guard let foundCategory = controller.categories.first(where: { $0.id == categoryId }) else {
            errorMessage = "未找到指定的知识库"
            showError = true
            return
        }

        category = foundCategory
        title = foundCategory.title
        content = foundCategory.content ?? ""
        hasChanges = false
    }

    /// 检查是否有变更
    private func checkForChanges() {
        guard let category else { return }

        let titleChanged = title != category.title
        let contentChanged = content != (category.content ?? "")

        hasChanges = titleChanged || contentChanged
    }

    /// 防抖动检查变更
    private func debouncedCheckForChanges() {
        debounceTimer?.invalidate()

        debounceTimer = Timer.scheduledTimer(withTimeInterval: 0.3, repeats: false) { _ in
            checkForChanges()
        }
    }

    /// 处理返回操作
    private func handleBackAction() {
        if hasChanges {
            showExitConfirmation = true
        } else {
            navigator.pop()
        }
    }

    /// 保存分类
    private func saveCategory() {
        guard let category,
              !title.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
        else {
            return
        }

        isSaving = true

        Task {
            do {
                try await controller.updateCategory(
                    id: category.id,
                    title: title.trimmingCharacters(in: .whitespacesAndNewlines),
                    favicon: nil,
                    content: content.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty ? nil : content
                        .trimmingCharacters(in: .whitespacesAndNewlines)
                )

                await MainActor.run {
                    isSaving = false
                    hasChanges = false
                    ToastManager.shared.showSuccess("更新成功")

                    NotificationCenter.default.post(name: .knowledgeCategoryUpdated, object: nil)

                    navigator.pop()
                }

            } catch {
                await MainActor.run {
                    isSaving = false

                    if let businessError = error as? BusinessError {
                        errorMessage = businessError.message
                    } else if let networkError = error as? NetworkError {
                        errorMessage = networkError.localizedDescription
                    } else {
                        errorMessage = error.localizedDescription
                    }

                    showError = true
                }
            }
        }
    }
}
