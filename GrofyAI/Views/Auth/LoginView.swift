import SwiftUI

struct LoginView: View {
    @ObservedObject var viewModel: AuthenticationController
    let onShowWebView: (String, String) -> Void

    var body: some View {
        VStack(spacing: DesignSystem.Spacing.lg) {
            VStack(spacing: DesignSystem.Spacing.md) {
                AuthTextField(
                    title: "邮箱",
                    placeholder: "请输入邮箱地址",
                    text: $viewModel.loginEmail,
                    isEnabled: !isLoading,
                    errorMessage: viewModel.loginEmailError,
                    onEditingChanged: { editing in
                        if !editing {
                            viewModel.validateLoginEmailForUI()
                        }
                    }
                )
                .textContentType(.emailAddress)
                .keyboardType(.emailAddress)
                .autocapitalization(.none)

                AuthTextField(
                    title: "密码",
                    placeholder: "请输入密码",
                    text: $viewModel.loginPassword,
                    isSecure: true,
                    isEnabled: !isLoading,
                    errorMessage: viewModel.loginPasswordError,
                    onEditingChanged: { editing in
                        if !editing {
                            viewModel.validateLoginPasswordForUI()
                        }
                    }
                )
                .textContentType(.password)
            }

            agreementCheckbox

            AuthButton(
                title: "登录",
                action: {
                    Task {
                        await viewModel.performLogin()
                    }
                },
                isEnabled: canLogin,
                isLoading: viewModel.isLoginLoading
            )

            divider

            HStack(spacing: DesignSystem.Spacing.lg) {
                SocialAuthButton(
                    provider: .apple,
                    action: {
                        Task {
                            await viewModel.performSocialAuth(provider: .apple)
                        }
                    },
                    isLoading: viewModel.isSocialAuthLoading(.apple),
                    isDisabled: viewModel.isAnyLoading && !viewModel.isSocialAuthLoading(.apple)
                )

                SocialAuthButton(
                    provider: .google,
                    action: {
                        Task {
                            await viewModel.performSocialAuth(provider: .google)
                        }
                    },
                    isLoading: viewModel.isSocialAuthLoading(.google),
                    isDisabled: viewModel.isAnyLoading && !viewModel.isSocialAuthLoading(.google)
                )
            }
        }
    }

    // MARK: - 计算属性

    private var isLoading: Bool {
        viewModel.isAnyLoading
    }

    private var canLogin: Bool {
        viewModel.isLoginFormValid && !viewModel.isAnyLoading
    }

    private var agreementCheckbox: some View {
        HStack(alignment: .center, spacing: DesignSystem.Spacing.sm) {
            Button(action: {
                viewModel.isAgreementAccepted.toggle()
            }) {
                Image(systemName: viewModel.isAgreementAccepted ? "checkmark.circle.fill" : "circle")
                    .font(.system(size: DesignSystem.FontSize.lg))
                    .foregroundColor(viewModel.isAgreementAccepted ? DesignSystem.Colors.primary : DesignSystem.Colors
                        .textSecondary
                    )
            }
            .buttonStyle(.plain)
            .disabled(isLoading)

            VStack(alignment: .leading, spacing: DesignSystem.Spacing.xs) {
                HStack(spacing: 0) {
                    Text("我已阅读并同意")
                        .contentTextStyle()
                        .foregroundColor(DesignSystem.Colors.textSecondary)

                    Text("《用户协议》")
                        .bodyCompactFixedStyle()
                        .foregroundColor(isLoading ? DesignSystem.Colors.textSecondary : DesignSystem.Colors.primary)
                        .onTapGesture {
                            if !isLoading {
                                onShowWebView(AppConfig.Web.termsOfServiceURL, "服务条款")
                            }
                        }

                    Text("和")
                        .contentTextStyle()
                        .foregroundColor(DesignSystem.Colors.textSecondary)

                    Text("《隐私协议》")
                        .bodyCompactFixedStyle()
                        .foregroundColor(isLoading ? DesignSystem.Colors.textSecondary : DesignSystem.Colors.primary)
                        .onTapGesture {
                            if !isLoading {
                                onShowWebView(AppConfig.Web.privacyPolicyURL, "隐私政策")
                            }
                        }
                }
            }

            Spacer()
        }
        .padding(.horizontal, DesignSystem.Spacing.sm)
    }

    private var divider: some View {
        HStack {
            Rectangle()
                .fill(DesignSystem.Colors.border)
                .frame(height: DesignSystem.BorderWidth.thin)

            Text("或")
                .captionStyle()
                .foregroundColor(DesignSystem.Colors.textSecondary)
                .padding(.horizontal, DesignSystem.Spacing.md)

            Rectangle()
                .fill(DesignSystem.Colors.border)
                .frame(height: DesignSystem.BorderWidth.thin)
        }
    }
}
