import SwiftUI

// MARK: - 认证界面导航栏组件

struct AuthNavigationBar: View {
    let onClose: () -> Void
    var backgroundColor: Color = DesignSystem.Colors.backgroundPage

    var body: some View {
        HStack {
            Spacer()

            But<PERSON>(action: onClose) {
                Image(systemName: "xmark")
                    .font(.system(size: 20, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
            }
            .buttonStyle(.plain)
            .accessibilityLabel("关闭")
        }
        .padding(.horizontal, DesignSystem.Spacing.lg)
        .padding(.vertical, DesignSystem.Spacing.md)
        .background(backgroundColor)
    }
}
