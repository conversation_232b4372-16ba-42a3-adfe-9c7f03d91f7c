import SwiftUI

// MARK: - 社交登录按钮

struct SocialAuthButton: View {
    let provider: SocialAuthProvider
    let action: () -> Void
    var isLoading = false
    var isDisabled = false

    var body: some View {
        Button(action: action) {
            Group {
                if isLoading {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: DesignSystem.Colors.textSecondary))
                        .scaleEffect(1.0)
                } else {
                    Group {
                        if provider.isSystemIcon {
                            Image(systemName: provider.iconName)
                                .font(.system(size: 24, weight: .medium))
                        } else {
                            Image(provider.iconName)
                                .resizable()
                                .aspectRatio(contentMode: .fit)
                                .frame(width: 24, height: 24)
                        }
                    }
                    .foregroundColor(provider.iconColor)
                }
            }
            .frame(width: 44, height: 44)
            .background(DesignSystem.Colors.backgroundCard)
            .clipShape(Circle())
            .overlay(
                Circle()
                    .stroke(DesignSystem.Colors.border, lineWidth: DesignSystem.BorderWidth.thin)
            )
            .opacity(isDisabled ? 0.6 : 1.0)
        }
        .disabled(isLoading || isDisabled)
        .buttonStyle(AuthButtonPressStyle())
    }
}

extension SocialAuthProvider {
    fileprivate var iconColor: Color {
        switch self {
        case .google:
            return Color(.systemBlue)
        case .apple:
            return DesignSystem.Colors.textPrimary
        }
    }
}
