import SwiftUI

struct AuthTextField: View {
    let title: String
    let placeholder: String
    @Binding var text: String
    var isSecure = false
    var isEnabled = true
    var errorMessage: String? = nil
    var onEditingChanged: ((Bool) -> Void)? = nil

    @FocusState private var isFocused: Bool
    @State private var isPasswordVisible = true

    private var displayError: String? {
        errorMessage
    }

    var body: some View {
        VStack(alignment: .leading, spacing: DesignSystem.Spacing.sm) {
            Text(title)
                .font(.system(size: DesignSystem.FontSize.md, weight: DesignSystem.FontWeight.medium))
                .foregroundColor(DesignSystem.Colors.textSecondary)

            ZStack(alignment: .trailing) {
                Group {
                    if isSecure {
                        customPasswordField
                    } else {
                        regularTextField
                    }
                }
                .textFieldStyle(AuthTextFieldStyle(
                    hasError: displayError != nil,
                    isFocused: isFocused,
                    isSecure: isSecure
                ))
                .disabled(!isEnabled)
                .accessibilityLabel(title)
                .accessibilityHint(placeholder)
                .accessibilityValue(displayError != nil ? "错误: \(displayError!)" : "")
                .onChange(of: isFocused) { focused in
                    onEditingChanged?(focused)
                }

                if isSecure {
                    Button(action: {
                        isPasswordVisible.toggle()
                    }) {
                        Image(systemName: isPasswordVisible ? "eye" : "eye.slash")
                            .font(.system(size: DesignSystem.FontSize.md, weight: DesignSystem.FontWeight.medium))
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                            .frame(width: 44, height: 44)
                    }
                    .buttonStyle(.plain)
                    .disabled(!isEnabled)
                    .opacity(isEnabled ? 1.0 : 0.6)
                    .padding(.trailing, DesignSystem.Spacing.xs)
                }
            }

            if let displayError {
                Text(displayError)
                    .captionStyle()
                    .foregroundColor(DesignSystem.Colors.error)
                    .transition(.opacity.combined(with: .move(edge: .top)))
                    .animation(.easeInOut(duration: 0.2), value: displayError)
                    .accessibilityIdentifier("error-message")
            }
        }
    }

    // MARK: - 自定义密码输入字段

    private var customPasswordField: some View {
        ZStack {
            TextField("", text: $text)
                .opacity(0)
                .disabled(true)

            Group {
                if isPasswordVisible {
                    TextField(placeholder, text: $text)
                        .textContentType(.password)
                        .autocapitalization(.none)
                        .disableAutocorrection(true)
                } else {
                    SecureField(placeholder, text: $text)
                        .textContentType(.oneTimeCode)
                        .autocapitalization(.none)
                        .disableAutocorrection(true)
                }
            }
            .focused($isFocused)
        }
    }

    private var regularTextField: some View {
        TextField(placeholder, text: $text)
            .textContentType(isEmailField ? .emailAddress : .none)
            .keyboardType(isEmailField ? .emailAddress : .default)
            .autocapitalization(isEmailField ? .none : .sentences)
            .disableAutocorrection(isEmailField)
            .focused($isFocused)
    }

    private var isEmailField: Bool {
        title.contains("邮箱") || title.lowercased().contains("email")
    }
}

struct AuthTextFieldStyle: TextFieldStyle {
    let hasError: Bool
    let isFocused: Bool
    let isSecure: Bool

    func _body(configuration: TextField<Self._Label>) -> some View {
        configuration
            .contentTextStyle()
            .padding(.leading, DesignSystem.Spacing.md)
            .padding(.trailing, isSecure ? 52 : DesignSystem.Spacing.md)
            .padding(.vertical, DesignSystem.Spacing.md)
            .frame(minHeight: 44, maxHeight: 44)
            .background(DesignSystem.Colors.backgroundInput)
            .cornerRadius(DesignSystem.Rounded.md)
            .overlay(
                RoundedRectangle(cornerRadius: DesignSystem.Rounded.md)
                    .stroke(borderColor, lineWidth: DesignSystem.BorderWidth.regular)
                    .animation(.easeInOut(duration: 0.2), value: borderColor)
            )
    }

    private var borderColor: Color {
        if hasError {
            return DesignSystem.Colors.error
        } else if isFocused {
            return DesignSystem.Colors.primary
        } else {
            return DesignSystem.Colors.border
        }
    }
}
