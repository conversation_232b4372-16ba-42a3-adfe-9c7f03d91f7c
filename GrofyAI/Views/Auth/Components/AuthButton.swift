import SwiftUI

struct AuthButton: View {
    let title: String
    let action: () -> Void
    var style: AuthButtonStyle = .primary
    var isEnabled = true
    var isLoading = false

    var body: some View {
        Button(action: action) {
            HStack(spacing: DesignSystem.Spacing.sm) {
                if isLoading {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: style.foregroundColor))
                        .scaleEffect(0.8)
                }

                Text(title)
                    .buttonTextStyle()
                    .foregroundColor(style.foregroundColor)
            }
            .frame(maxWidth: .infinity)
            .frame(height: 48)
            .background(style.backgroundColor)
            .cornerRadius(DesignSystem.Rounded.md)
            .opacity(isEnabled ? 1.0 : 0.6)
        }
        .disabled(!isEnabled || isLoading)
        .buttonStyle(AuthButtonPressStyle())
    }
}

enum AuthButtonStyle {
    case primary
    case secondary
    case outline

    var backgroundColor: Color {
        switch self {
        case .primary:
            return DesignSystem.Colors.primary
        case .secondary:
            return DesignSystem.Colors.backgroundCard
        case .outline:
            return Color.clear
        }
    }

    var foregroundColor: Color {
        switch self {
        case .primary:
            return DesignSystem.Colors.whiteBlack
        case .secondary:
            return DesignSystem.Colors.textPrimary
        case .outline:
            return DesignSystem.Colors.primary
        }
    }
}

struct AuthButtonPressStyle: ButtonStyle {
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .scaleEffect(configuration.isPressed ? 0.98 : 1.0)
            .opacity(configuration.isPressed ? 0.8 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: configuration.isPressed)
    }
}
