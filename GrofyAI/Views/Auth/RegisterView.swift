import SwiftUI

struct RegisterView: View {
    @ObservedObject var viewModel: AuthenticationController
    let onShowWebView: (String, String) -> Void

    var body: some View {
        VStack(spacing: DesignSystem.Spacing.lg) {
            VStack(spacing: DesignSystem.Spacing.md) {
                AuthTextField(
                    title: "邮箱",
                    placeholder: "请输入邮箱地址",
                    text: $viewModel.registerEmail,
                    isEnabled: !isLoading,
                    errorMessage: viewModel.registerEmailError,
                    onEditingChanged: { editing in
                        if !editing {
                            viewModel.validateRegisterEmailForUI()
                        }
                    }
                )
                .textContentType(.emailAddress)
                .keyboardType(.emailAddress)
                .autocapitalization(.none)

                AuthTextField(
                    title: "密码",
                    placeholder: "请输入密码（至少8个字符）",
                    text: $viewModel.registerPassword,
                    isSecure: true,
                    isEnabled: !isLoading,
                    errorMessage: viewModel.registerPasswordError,
                    onEditingChanged: { editing in
                        if !editing {
                            viewModel.validateRegisterPasswordForUI()
                        }
                    }
                )
                .textContentType(.newPassword)

                VStack(alignment: .leading, spacing: DesignSystem.Spacing.sm) {
                    Text("邮箱验证码")
                        .contentTextStyle()
                        .foregroundColor(DesignSystem.Colors.textSecondary)

                    HStack(spacing: DesignSystem.Spacing.md) {
                        TextField("请输入验证码", text: $viewModel.verificationCode)
                            .contentTextStyle()
                            .padding(DesignSystem.Spacing.md)
                            .frame(minHeight: 44, maxHeight: 44) // 与 AuthTextField 保持一致的高度
                            .background(DesignSystem.Colors.backgroundInput)
                            .cornerRadius(DesignSystem.Rounded.md)
                            .overlay(
                                RoundedRectangle(cornerRadius: DesignSystem.Rounded.md)
                                    .stroke(DesignSystem.Colors.border, lineWidth: DesignSystem.BorderWidth.regular)
                            )
                            .keyboardType(.numberPad)
                            .disabled(isLoading)
                            .frame(maxWidth: .infinity)

                        AuthButton(
                            title: "发送验证码",
                            action: {
                                Task {
                                    await viewModel.checkUserExistsAndSendVerificationCode()
                                }
                            },
                            style: .outline,
                            isEnabled: canSendVerificationCode,
                            isLoading: viewModel.isSendCodeLoading || viewModel.isCheckUserExistsLoading
                        )
                        .fixedSize(horizontal: true, vertical: false)
                    }
                }
            }

            agreementCheckbox

            AuthButton(
                title: "注册",
                action: {
                    Task {
                        await viewModel.performRegister()
                    }
                },
                isEnabled: canRegister,
                isLoading: viewModel.isRegisterLoading
            )

            divider

            HStack(spacing: DesignSystem.Spacing.lg) {
                SocialAuthButton(
                    provider: .apple,
                    action: {
                        Task {
                            await viewModel.performSocialAuth(provider: .apple)
                        }
                    },
                    isLoading: viewModel.isSocialAuthLoading(.apple),
                    isDisabled: viewModel.isAnyLoading && !viewModel.isSocialAuthLoading(.apple)
                )

                SocialAuthButton(
                    provider: .google,
                    action: {
                        Task {
                            await viewModel.performSocialAuth(provider: .google)
                        }
                    },
                    isLoading: viewModel.isSocialAuthLoading(.google),
                    isDisabled: viewModel.isAnyLoading && !viewModel.isSocialAuthLoading(.google)
                )
            }
        }
    }

    // MARK: - 计算属性

    private var isLoading: Bool {
        viewModel.isAnyLoading
    }

    private var canSendVerificationCode: Bool {
        viewModel.canSendVerificationCode && !viewModel.isAnyLoading
    }

    private var canRegister: Bool {
        viewModel.isRegisterFormValid && !viewModel.isAnyLoading
    }

    private var agreementCheckbox: some View {
        HStack(alignment: .center, spacing: DesignSystem.Spacing.sm) {
            Button(action: {
                viewModel.isAgreementAccepted.toggle()
            }) {
                Image(systemName: viewModel.isAgreementAccepted ? "checkmark.circle.fill" : "circle")
                    .font(.system(size: DesignSystem.FontSize.lg))
                    .foregroundColor(viewModel.isAgreementAccepted ? DesignSystem.Colors.primary : DesignSystem.Colors
                        .textSecondary
                    )
            }
            .buttonStyle(.plain)
            .disabled(isLoading)

            VStack(alignment: .leading, spacing: DesignSystem.Spacing.xs) {
                HStack(spacing: 0) {
                    Text("我已阅读并同意")
                        .contentTextStyle()
                        .foregroundColor(DesignSystem.Colors.textSecondary)

                    Text("《用户协议》")
                        .bodyCompactFixedStyle()
                        .foregroundColor(isLoading ? DesignSystem.Colors.textSecondary : DesignSystem.Colors.primary)
                        .onTapGesture {
                            if !isLoading {
                                onShowWebView(AppConfig.Web.termsOfServiceURL, "服务条款")
                            }
                        }

                    Text("和")
                        .contentTextStyle()
                        .foregroundColor(DesignSystem.Colors.textSecondary)

                    Text("《隐私协议》")
                        .bodyCompactFixedStyle()
                        .foregroundColor(isLoading ? DesignSystem.Colors.textSecondary : DesignSystem.Colors.primary)
                        .onTapGesture {
                            if !isLoading {
                                onShowWebView(AppConfig.Web.privacyPolicyURL, "隐私政策")
                            }
                        }
                }
            }

            Spacer()
        }
        .padding(.horizontal, DesignSystem.Spacing.sm)
    }

    private var divider: some View {
        HStack {
            Rectangle()
                .fill(DesignSystem.Colors.border)
                .frame(height: DesignSystem.BorderWidth.thin)

            Text("或")
                .captionStyle()
                .foregroundColor(DesignSystem.Colors.textSecondary)
                .padding(.horizontal, DesignSystem.Spacing.md)

            Rectangle()
                .fill(DesignSystem.Colors.border)
                .frame(height: DesignSystem.BorderWidth.thin)
        }
    }
}
