//
//  EffectVideoView.swift
//  GrofyAI
//
//  Created by kissy on 2025/7/2.
//

import SwiftUI
import FlowStacks
import Kingfisher
import SDWebImageSwiftUI
import SDWebImageWebPCoder


struct EffectVideoView: View {

    @EnvironmentObject var navigator: FlowPathNavigator
    @Environment(\.colorScheme) private var colorScheme
    private let authStore = AuthStore.shared
    @EnvironmentObject private var globalAuthManager: GlobalAuthManager

    private let columns: [GridItem] = [
            GridItem(.adaptive(minimum: 100), spacing: 8)
        ]
    var body: some View {
        ZStack{
            if colorScheme == .light {
                VStack{
                    Color.clear
                        .background( // 使用全屏背景层替代Spacer方案
                            Image("ImageNavBackground")
                                .resizable() // 关键：使图片可缩放
                                .aspectRatio(contentMode: .fill) // 填充整个区域
                                .edgesIgnoringSafeArea(.all) // 忽略所有安全区域
                        )
                        .frame(height: safeAreaTopInset())
                    Spacer()
                }
            }
            
            VStack(spacing: 0) {
                EffectNavigationBarView()
                // 顶部的可滚动标签栏
                ScrollView {
                    // 2. 创建懒加载网格，行间距为16
                    LazyVGrid(columns: columns, spacing: 16) {
                        // 3. 遍历 EffectType 的所有 case
                        // CaseIterable 协议让我们能用 .allCases 获取所有 case
                        ForEach(EffectVideoAi.EffectType.allCases) { effect in
                            // 为每个 effect 创建一个 EffectCell
                            EffectCell(effect: effect)
                                .onTapGesture {
                                    if authStore.getAccessToken() == nil {
                                        //显示登录弹窗（页面）
                                        globalAuthManager.requestAuthentication()
                                    }else {
                                        navigator.push(Route.createEffectVideo(type: effect))
                                    }
                                    
                                }
                        }
                    }
                    .padding(.horizontal) // 给整个网格添加内边距
                }
                .scrollIndicators(.hidden)
                
                Spacer()
                    .frame(height:35)
            }
        }
        .ignoresSafeArea(edges: .bottom)
        .navigationBarHidden(true)
        .background(DesignSystem.Colors.backgroundPage)
    }
    
    // 辅助函数：获取顶部安全区域高度
    private func safeAreaTopInset() -> CGFloat {
        let keyWindow = UIApplication.shared.connectedScenes
            .filter({ $0.activationState == .foregroundActive })
            .map({ $0 as? UIWindowScene })
            .compactMap({ $0 })
            .first?.windows
            .filter({ $0.isKeyWindow }).first
        return keyWindow?.safeAreaInsets.top ?? 0
    }

}

//MARK: - EffectNavigationBarView
struct EffectNavigationBarView: View {
    @EnvironmentObject var navigator: FlowPathNavigator
    private let authStore = AuthStore.shared

    var body: some View {
        VStack(spacing: 0) {
            // 自定义导航栏主体
            HStack {
                Image(systemName: "chevron.backward")
                    .foregroundStyle(DesignSystem.Colors.textPrimary)
                    .font(.title3)
                    .onTapGesture {
                        navigator.popToRoot()
                    }
                Spacer()
                if authStore.getAccessToken() != nil {
                    Image(systemName: "clock")
                        .foregroundStyle(DesignSystem.Colors.textPrimary)
                        .font(.title3)
                        .onTapGesture {
                            navigator.push(Route.artWorkHistory(defaultMode: .effect))
                        }
                }
            }
            .frame(height: 10)
            
        }
        .frame(maxWidth: .infinity)
        .padding(.top,10)
        .padding(.bottom,20)
        .padding(.horizontal,15)
    }
}

//MARK: - EffectCell
struct EffectCell: View {
    let effect: EffectVideoAi.EffectType
    let cellWidth:CGFloat?
    private let gifUrl: URL?
    
    init(effect: EffectVideoAi.EffectType,cellWidth:CGFloat? = 100) {
        self.effect = effect
        self.cellWidth = cellWidth
        self.gifUrl = Bundle.main.url(forResource: effect.imageName, withExtension: "webp")
    }
    
    var body: some View {
        // 使用 VStack 实现垂直布局，图片在上，文字在下
        VStack(spacing: 8) {
            // 图片部分
            ZStack{
                RoundedRectangle(cornerRadius: 8, style: .continuous)
                    .fill(
                        LinearGradient(
                            gradient: Gradient(colors: [Color.purple.opacity(0.8), Color.blue.opacity(0.8)]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                        
                    )
                VStack{
                    if let url = gifUrl {
                        AnimatedImage(url: url)
                            .indicator(SDWebImageActivityIndicator.medium)
                            .resizable()
                            .scaledToFill()
                            .frame(width: cellWidth)
                        //                        .aspectRatio(1, contentMode: .fit)
                        //                        .scaledToFit()
                    } else {
                        Image(systemName: "photo.fill")
                            .font(.largeTitle)
                            .foregroundColor(.white.opacity(0.7))
                    }
                }
                .aspectRatio(1, contentMode: .fit)
            }
            .clipShape(RoundedRectangle(cornerRadius: 12, style: .continuous))// 圆角矩形裁切
            .aspectRatio(1, contentMode: .fit)
            
            // 文字部分
            Text(effect.description)
                .font(.footnote) // 使用小号字体
                .fontWeight(.medium)
                .foregroundColor(.primary) // 使用主要文字颜色
                .lineLimit(1) // 确保文字只显示一行
        }
    }
}
