//
//  ShowEffectVideo.swift
//  GrofyAI
//
//  Created by kissy on 2025/7/14.
//

import SwiftUI
import Kingfisher
import FlowStacks

struct ShowEffectVideo :View {
    
    @EnvironmentObject var navigator: FlowPathNavigator
    @EnvironmentObject private var globalAuthManager: GlobalAuthManager

    private let authStore = AuthStore.shared
    
    var body: some View {
        VStack{
            HStack {
                Text("特效视频")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                Spacer()
                

                Button(action: { navigator.push(Route.effectVideo)}) {
                    // 文字
                    Text("更多 >")
                        .font(.system(size: 12))
                        .foregroundColor(DesignSystem.Colors.primary)
                }
            }
            
            ScrollView(.horizontal, showsIndicators: false) {
                HStack{
                    ForEach(EffectVideoAi.EffectType.allCases.filter{ $0.hot == "HOT"}) { effect in
                        // 为每个 effect 创建一个 EffectCell
                        EffectCell(effect: effect)
                            .onTapGesture {
                                if authStore.getAccessToken() == nil {
                                    //显示登录弹窗（页面）
                                    globalAuthManager.requestAuthentication()
                                }else {
                                    navigator.push(Route.createEffectVideo(type: effect))
                                }
                                
                            }
                    }
                }
                .frame(height: 130)
                .fontWeight(.light)
                
                
            }
        }
    }
}
