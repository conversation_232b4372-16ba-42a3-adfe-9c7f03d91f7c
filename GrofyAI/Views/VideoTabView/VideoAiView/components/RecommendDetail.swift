//
//  RecommendDetail.swift
//  GrofyAI
//
//  Created by kissy on 2025/7/14.
//

import SwiftUI

struct WaterfallGrid<Data, Content>: View where Data: RandomAccessCollection, Data.Element: Identifiable & Hashable, Content: View {

    // MARK: - 属性
    let data: Data
    let columns: Int
    let spacing: CGFloat
    let content: (Data.Element) -> Content

    // MARK: - 初始化
    init(
        _ data: Data,
        columns: Int = 2,
        spacing: CGFloat = 8,
        @ViewBuilder content: @escaping (Data.Element) -> Content
    ) {
        self.data = data
        self.columns = columns
        self.spacing = spacing
        self.content = content
    }

    // MARK: - 主体视图
    var body: some View {
        if #available(iOS 16.0, *) {
            modernBody
        } else {
            fallbackBody
        }
    }

    // MARK: - iOS 16+ 实现 (无需改动)
    @available(iOS 16.0, *)
    private var modernBody: some View {
        ScrollView(.vertical, showsIndicators: false) {
            WaterfallLayout(columns: columns, spacing: spacing) {
                ForEach(data, id: \.self) { item in
                    content(item)
                }
            }
//            .padding(.horizontal, spacing)
        }
    }

    // MARK: - 旧版本回退实现 (逻辑更新)
    private var fallbackBody: some View {
        ScrollView(.vertical, showsIndicators: false) {
            HStack(alignment: .top, spacing: spacing) {
                // 调用新的分发方法
                ForEach(distributeItemsByIndex(), id: \.self) { columnItems in
                    LazyVStack(spacing: spacing) {
                        ForEach(columnItems) { item in
                            content(item)
                        }
                    }
                }
            }
//            .padding(.horizontal, spacing)
        }
    }

    // MARK: - 【新】辅助方法 (用于回退方案，按索引分发)
    private func distributeItemsByIndex() -> [[Data.Element]] {
        guard columns > 0 else { return [] }
        
        var columnsData: [[Data.Element]] = Array(repeating: [], count: columns)
        
        // 使用 enumerated 获取每个元素的索引
        for (index, item) in data.enumerated() {
            // 通过取模运算决定当前 item 属于哪一列
            let columnIndex = index % columns
            columnsData[columnIndex].append(item)
        }
        
        return columnsData
    }
}


@available(iOS 16.0, *)
struct WaterfallLayout: Layout {
    
    // MARK: - 属性
    var columns: Int
    var spacing: CGFloat
    
    // MARK: - 初始化
    init(columns: Int = 2, spacing: CGFloat = 8) {
        self.columns = columns
        self.spacing = spacing
    }
    
    // MARK: - Layout 协议核心方法
    
    // 1. 计算布局容器的总尺寸
    func sizeThatFits(proposal: ProposedViewSize, subviews: Subviews, cache: inout ()) -> CGSize {
        // 我们只需要计算总高度，宽度由提议的尺寸确定
        let width = proposal.replacingUnspecifiedDimensions().width
        let viewFrames = calculateFrames(for: subviews, in: width)
        
        // 总高度 = 最高的那个列的高度
        let totalHeight = viewFrames.max { $0.maxY < $1.maxY }?.maxY ?? 0
        
        return CGSize(width: width, height: totalHeight)
    }
    
    // 2. 放置每一个子视图
    func placeSubviews(in bounds: CGRect, proposal: ProposedViewSize, subviews: Subviews, cache: inout ()) {
        let viewFrames = calculateFrames(for: subviews, in: bounds.width)
        
        for index in subviews.indices {
            let frame = viewFrames[index]
            let position = CGPoint(x: bounds.minX + frame.minX, y: bounds.minY + frame.minY)
            // 将子视图放置在计算好的位置上
            subviews[index].place(at: position, proposal: ProposedViewSize(frame.size))
        }
    }
    
    // MARK: - 辅助方法
    
    // 计算所有子视图的 Frame
    private func calculateFrames(for subviews: Subviews, in containerWidth: CGFloat) -> [CGRect] {
        // 如果没有子视图或列数不合法，返回空
        guard !subviews.isEmpty, columns > 0 else { return [] }
        
        // 计算每列的宽度
        let totalSpacing = spacing * CGFloat(columns - 1)
        let columnWidth = (containerWidth - totalSpacing) / CGFloat(columns)
        
        // 如果列宽不合法，返回空
        guard columnWidth > 0 else { return [] }
        
        // 用一个数组记录每一列的当前总高度
        var columnHeights = Array(repeating: CGFloat.zero, count: columns)
        
        // 存储每个视图的最终 Frame
        var frames: [CGRect] = []
        
        for subview in subviews {
            // 获取子视图在给定列宽下的理想高度
            let idealSize = subview.sizeThatFits(ProposedViewSize(width: columnWidth, height: nil))
            
            // 找到当前最短的列
            // `miminumColumnIndex` 是最短列的索引
            let minimumColumnIndex = columnHeights.indices.min { columnHeights[$0] < columnHeights[$1] } ?? 0
            
            // 计算视图的 x, y 坐标
            let xOffset = CGFloat(minimumColumnIndex) * (columnWidth + spacing)
            let yOffset = columnHeights[minimumColumnIndex]
            
            // 创建该视图的 Frame
            let frame = CGRect(x: xOffset, y: yOffset, width: columnWidth, height: idealSize.height)
            frames.append(frame)
            
            // 更新该列的总高度
            columnHeights[minimumColumnIndex] += idealSize.height + spacing
        }
        
        return frames
    }
}
