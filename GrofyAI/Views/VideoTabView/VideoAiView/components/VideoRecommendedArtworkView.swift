import AVKit
import Combine
import FlowStacks
import Kingfisher
import Photos
import SwiftUI

struct VideoRecommendedArtworkView: View {
    let fileService = FileService()
    // 视图的展示和消失由导航栈控制
    let artwork: ArtWorksCell
    @EnvironmentObject var navigator: FlowPathNavigator
    @Environment(\.dismiss) private var dismiss // 使用 SwiftUI 内置的 dismiss
    @EnvironmentObject private var globalAuthManager: GlobalAuthManager

    @State private var player: AVPlayer?
    @State private var isPlaying = false
    @State private var showPlayPauseIcon = false

    // 1. 用于监听播放器状态的订阅者
    @State private var playerStatusObserver: AnyCancellable?
    // 2. 标记视频是否已准备好播放，用于控制封面和视频的切换
    @State private var isVideoReady = false

    private let authStore = AuthStore.shared

    var body: some View {
        ZStack {
            Color.black.ignoresSafeArea()

            VStack(alignment: .leading, spacing: 5) {
                topBar

                ZStack {
                    if let coverUrl = artwork.cover, let url = URL(string: coverUrl) {
                        ZStack {
                            KFImage(url)
                                .resizable()
                                .scaledToFit()
                                .clipShape(RoundedRectangle(cornerRadius: 12))
                                .ignoresSafeArea()
                                .opacity(isVideoReady ? 0 : 1) // 视频准备好后，封面图淡出

                            ProgressView()
                                .scaleEffect(2.0)
                                .tint(.white)
                        }
                    }

                    // 视频播放器
                    if let player {
                        VideoPlayer(player: player)
                            .disabled(true)
                            .opacity(isVideoReady ? 1 : 0) // 只有视频准备好后，才淡入显示
                    }

                    Color.clear
                        .contentShape(Rectangle()) // 让整个透明区域都可以响应点击
                        .onTapGesture {
                            togglePlayPause()
                        }

                    if showPlayPauseIcon {
                        Image(systemName: isPlaying ? "play.fill" : "pause.fill")
                            .font(.system(size: 60, weight: .bold))
                            .foregroundColor(.white.opacity(0.8))
                            .transition(.opacity.animation(.easeInOut))
                            // 允许点击穿透图标，防止它挡住下面的UI层
                            .allowsHitTesting(false)
                    }

                    VStack(alignment: .leading) {
                        Spacer()
                        // 底部信息和交互按钮区域
                        Text("@MoonvyAI")
                            .font(.headline)
                            .fontWeight(.bold)

                        Text(artwork.subTitle ?? "dwasdwadw")
                            .font(.subheadline)
                            .lineLimit(2) // 限制描述行数
                    }
                    .frame(width: UIScreen.main.bounds.width, alignment: .leading)
                    .padding(5)
                    .padding(.horizontal, 15)
                }
                .padding(.bottom, 15)

                bottomSection
            }
        }
        .foregroundColor(.white) // 为覆盖层上的所有文本和图标设置默认颜色
        .navigationBarBackButtonHidden(true) // 隐藏系统默认的返回按钮，使用自定义按钮
        .onAppear(perform: setupVideoPlayer)
        .onDisappear {
            // 离开页面时，取消状态监听并暂停视频
            playerStatusObserver?.cancel()
            player?.pause()
            // 移除循环播放的通知监听
            NotificationCenter.default.removeObserver(self)
        }
    }

    // MARK: - UI 元素

    /// 顶部导航栏 (返回按钮和更多按钮)
    private var topBar: some View {
        VStack(spacing: 0) {
            // 自定义导航栏主体
            HStack {
                Button(action: {
                    // 使用 dismiss 或 navigator.pop() 返回上一页
                    navigator.pop()
                }) {
                    Image(systemName: "chevron.left")
                        .font(.title2.weight(.medium))
                        .padding()
                }
                Spacer()

                if isVideoReady {
                    Button(action: {
                        Task {
                            // 点击按钮时，调用保存图片的函数
                            await download()
                        }

                    }) {
                        Image(systemName: "arrow.down.circle")
                            .font(.title2.weight(.medium))
                            .padding()
                    }
                    .padding(8)
                }

//                Button(action: {
//                    // “更多”操作
//                }) {
//                    Image(systemName: "ellipsis")
//                        .font(.title2.weight(.medium))
//                        .padding()
//                }
            }
            .frame(maxWidth: .infinity)
            .padding(.horizontal, 10)
        }
    }

    /// 底部区域 (包含左侧信息和右侧操作按钮)
    private var bottomSection: some View {
        // 左侧：作者信息、描述和“做同款”按钮
        VStack(alignment: .leading, spacing: 12) {
            Button(action: {
                if authStore.getAccessToken() == nil {
                    // 显示登录弹窗（页面）
                    globalAuthManager.requestAuthentication()
                } else {
                    navigator.push(Route.createVideo(detail: artwork, createType: nil))
                }
            }) {
                Text("做同款")
                    .font(.headline)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 14)
                    .background(Color(red: 0.3, green: 0.3, blue: 0.9)) // 提取图片中的蓝色
                    .cornerRadius(12)
            }
        }
        .padding(.horizontal, 20)
        .padding(.bottom, 30)
    }

    private func togglePlayPause() {
        guard player != nil else { return }

        // 切换播放状态
        isPlaying.toggle()

        if isPlaying {
            player?.play()
        } else {
            player?.pause()
        }

        // 显示反馈图标，并在片刻后让它消失
        showPlayPauseIcon = true
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.8) {
            withAnimation {
                showPlayPauseIcon = false
            }
        }
    }

    // 初始化播放器并播放
    private func setupVideoPlayer() {
        guard player == nil, let urlString = artwork.links?.first, let url = URL(string: urlString) else {
            return
        }

        let playerItem = AVPlayerItem(url: url)
        player = AVPlayer(playerItem: playerItem)
        player?.isMuted = true

        // 4. 监听 playerItem 的状态
        playerStatusObserver = playerItem.publisher(for: \.status)
            .receive(on: DispatchQueue.main)
            .sink { [weak playerItem] status in
                switch status {
                case .readyToPlay:
                    // 当状态变为 readyToPlay 时，更新我们的状态变量
                    withAnimation { isVideoReady = true }
                    player?.play() // 准备好后自动开始播放
                case .failed:
                    // 可以在这里处理加载失败的情况
                    print("视频加载失败: \(playerItem?.error?.localizedDescription ?? "未知错误")")
                    isVideoReady = false // 确保视频不显示
                default:
                    // .unknown 状态，什么都不做
                    break
                }
            }

        NotificationCenter.default.addObserver(
            forName: .AVPlayerItemDidPlayToEndTime, // 监听视频播放结束的通知
            object: player?.currentItem, // 只关心当前这个播放项
            queue: .main // 在主线程执行响应
        ) { _ in
            // 当通知触发时，让播放器回到时间的起点
            player?.seek(to: .zero)
            // 然后再次播放
            player?.play()
        }
    }

    // 暂停视频
    private func pauseVideo() {
        player?.pause()
        isPlaying = false // 确保状态同步
    }

    private func download() async {
        do {
            guard let url = artwork.links?.first else { return }
            print("开始准备下载文件: \(url)")
            let req = DownloadReq(url: url)

            // 1. 调用服务，下载文件并获取本地临时文件的 URL
            // FileService 内部处理了下载和写入临时文件的逻辑
            let data = try await fileService.download(req: req)
            let localVideoURL = FileManager.default.temporaryDirectory.appendingPathComponent(UUID().uuidString)
                .appendingPathExtension("mp4")
            try data.write(to: localVideoURL)

            print("文件临时保存在: \(localVideoURL.path)")

            // 将本地的视频文件保存到系统相册
            try await saveVideoToPhotos(from: localVideoURL)

            // 清理临时文件
            try? FileManager.default.removeItem(at: localVideoURL)
            print("临时文件已清理。")
            ToastManager.shared.showSuccess("下载成功")
        } catch {
            // 捕获整个过程中任何地方抛出的错误
            print("错误详情: \(error)")
            ToastManager.shared.showError("下载失败")
        }
    }

    private func saveVideoToPhotos(from videoURL: URL) async throws {
        // 请求“仅添加”权限
        let status = await PHPhotoLibrary.requestAuthorization(for: .addOnly)

        switch status {
        case .authorized, .limited:
            // 用户已授权，可以执行保存
            try await PHPhotoLibrary.shared().performChanges {
                // 创建一个从文件 URL 添加视频的请求
                PHAssetChangeRequest.creationRequestForAssetFromVideo(atFileURL: videoURL)
            }
            print("PHPhotoLibrary.performChanges 已成功执行。")

        case .denied, .restricted:
            // 用户拒绝了权限
            throw PhotoLibraryError.accessDenied

        case .notDetermined:
            // 这种情况理论上在 await 之后不会发生，但为了完整性加上
            throw PhotoLibraryError.accessNotDetermined

        @unknown default:
            throw PhotoLibraryError.unknown
        }
    }
}
