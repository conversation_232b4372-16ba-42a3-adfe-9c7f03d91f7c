//
//  VideoCreation.swift
//  GrofyAI
//
//  Created by kissy on 2025/6/24.
//

import SwiftUI
import FlowStacks

enum CreateType:String {
    case textToVideo = "TEXT_VIDEO"
    case imageToVideo =  "IMAGE_VIDEO"
}

struct VideoCreationView: View {
    
    @State private var detail: ArtWorksCell?
    var createType:CreateType?
    // 新增一个状态，用于判断是否是初次加载
    @State private var isInitialLoad = true
    
    
    @EnvironmentObject var navigator: FlowPathNavigator
    //    @Environment(\.activeTab) private var activeTab //当前活动路由
    @StateObject private var controller = VideoAiController()
    @Environment(\.colorScheme) private var colorScheme
    
    init(detail: ArtWorksCell? = nil, createType:CreateType? = nil) {
        // 使用 _detail 访问底层的 State 结构体，并用传入的值来初始化它
        self._detail = State(initialValue: detail)
        self.createType = createType
    }
    
    var body: some View {
        ZStack{
            if colorScheme == .light {
                VStack{
                    Color.clear
                        .background( // 使用全屏背景层替代Spacer方案
                            Image("ImageNavBackground")
                                .resizable() // 关键：使图片可缩放
                                .aspectRatio(contentMode: .fill) // 填充整个区域
                                .edgesIgnoringSafeArea(.all) // 忽略所有安全区域
                        )
                        .frame(height: safeAreaTopInset())
                    Spacer()
                }
            }
            
            VStack(spacing: 5) {
                CreateVideoNavigationBarView()
                // 顶部的可滚动标签栏
                ScrollViewReader { scrollViewProxy in
                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack(spacing: 20) {
                            ForEach(VideoAiModel.allCases, id: \.self) { tab in
                                TabButton(title: tab.description, isSelected: controller.selectedModel == tab) {
                                    // 这里的动画只影响顶部标签栏（下划线和滚动）
                                    withAnimation(.spring()) {
                                        controller.selectedModel = tab
                                    }
                                }
                                .id(tab)
                            }
                        }
                        .padding(.horizontal)
                    }
                    .frame(height: 35)
                    .background(Color.clear)
                    //                .background(Color(red: 246/255, green: 246/255, blue: 246/255))
                    //                .background(Color(.systemGray6).opacity(0.3))
                    .onChange(of: controller.selectedModel) { newTab in
                        hideKeyboard()
                        
                        withAnimation {
                            scrollViewProxy.scrollTo(newTab, anchor: .center)
                        }
                        if !isInitialLoad {
                            self.detail = nil
                        }
                    }
                    .padding(.bottom,5)
                }
                
                // 下方的可滑动内容区域 (已优化)
                TabView(selection: $controller.selectedModel) {
                    ForEach(VideoAiModel.allCases, id: \.self) { tab in
                        //                    ScrollView {
                        ZStack {
                            getColorForTab(tab, info: detail)
                                .ignoresSafeArea()
                                .onAppear{
                                    FormDataStorage.saveModel(model: tab)
                                }
                        }
                        //                    }
                        .scrollContentBackground(.hidden) // 隐藏默认背景
                        .background(DesignSystem.Colors.backgroundPage)
                        //                    .background(Color(red: 173/255, green: 169/255, blue: 255/255))
                        .tag(tab)
                    }
                }
                .tabViewStyle(.page(indexDisplayMode: .never))
                // ✅ 当 selectedTab 改变时，禁用 TabView 的动画
                .animation(nil, value: controller.selectedModel)
                
            }
        }
        .ignoresSafeArea(edges: .bottom)
        .navigationBarHidden(true)
        .background(DesignSystem.Colors.backgroundPage)
        .onAppear{
            if let type = detail?.agentType {
                if let videoModel = VideoAiModel(rawValue: detail?.provider ?? FormDataStorage.loadModel(as: VideoAiModel.self )?.rawValue ?? "Wanx"), type == .video {
                    // 成功找到了枚举项
                    controller.selectedModel = videoModel
                }
                DispatchQueue.main.async {
                    // 使用 DispatchQueue.main.async 是一个好习惯
                    // 确保 onAppear 中的状态设置完成后，再更新 isInitialLoad
                    self.isInitialLoad = false
                }
            }
        }
    }
    

    
    
    
    // 辅助函数：获取顶部安全区域高度
    private func safeAreaTopInset() -> CGFloat {
        let keyWindow = UIApplication.shared.connectedScenes
            .filter({ $0.activationState == .foregroundActive })
            .map({ $0 as? UIWindowScene })
            .compactMap({ $0 })
            .first?.windows
            .filter({ $0.isKeyWindow }).first
        return keyWindow?.safeAreaInsets.top ?? 0
    }
    
    @ViewBuilder
    private func getColorForTab(_ tab: VideoAiModel,info detail:ArtWorksCell?) -> some View {
        switch tab {
        case .kling:
            KlingView(detail: detail)
        case .minimax:
            MinimaxView(detail: detail)
        case .runway:
            RunwayView(detail: detail)
        case .wanx:
            WanxView(detail: detail,createType: createType)
        case .pixverse:
            PixverseView(detail: detail)
        }
    }
}


struct CreateVideoNavigationBarView: View {
    @EnvironmentObject var navigator: FlowPathNavigator
    var body: some View {
        VStack(spacing: 0) {
            // 自定义导航栏主体
            HStack {
                Image(systemName: "chevron.backward")
                    .foregroundStyle(DesignSystem.Colors.textPrimary)
                    .font(.title3)
                    .onTapGesture {
                        navigator.popToRoot()
                    }
                Spacer()
                Image(systemName: "clock")
                    .foregroundStyle(DesignSystem.Colors.textPrimary)
                    .font(.title3)
                    .onTapGesture {
                        navigator.push(Route.artWorkHistory(defaultMode: .video))
                    }
            }
            .frame(height: 10)
            
        }
        .frame(maxWidth: .infinity)
        .padding(.top,10)
        .padding(.bottom,20)
        .padding(.horizontal,15)
    }
}
