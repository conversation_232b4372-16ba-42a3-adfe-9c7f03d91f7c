//
//  MinimaxView.swift
//  GrofyAI
//
//  Created by kissy on 2025/6/24.
//

import SwiftUI

struct MinimaxView : View {
    var detail: ArtWorksCell?
    
    @State private var minimaxMode:MinimaxVideoAi.Mode
    
    init(detail: ArtWorksCell? = nil) {
        func handleMode(type: CreateType?) -> MinimaxVideoAi.Mode?{
            switch type {
            case .textToVideo:
                return   .t2v
            case .imageToVideo:
                return  .i2v
            default:
                return nil
            }
        }
        
        //历史
        let historyCreateType: CreateType? = CreateType(rawValue: detail?.agentSubtype ?? "")
        let historyMode: MinimaxVideoAi.Mode? = handleMode(type: historyCreateType)
        
        self.detail = detail
        self._minimaxMode = State(initialValue: historyMode ?? FormDataStorage.loadModelType(as: MinimaxVideoAi.Mode.self) ?? .t2v)
    }

    var body: some View {
        ZStack{
            switch minimaxMode {
            case .t2v:
                MinimaxTextToVideoView(detail: detail){
                    modeContent
                }
            case .i2v:
                MinimaxImageToVideoView(detail: detail){
                    modeContent
                }
            }
        }
        .animation(.easeInOut(duration: 0.5), value: minimaxMode)
        .onChange(of: minimaxMode){ mode in
            FormDataStorage.saveModelType(modelType: mode)
        }
    }
    
    @ViewBuilder
    private var modeContent: some View {
        VStack(alignment:.leading){
            SectionHeader( title: "模式")
            HStack{
                GenericSelectorView(
                    options: Array(MinimaxVideoAi.Mode.allCases),
                    selectedOption: $minimaxMode
                )
                Spacer()
            }
            .padding(10)
            .background(
                RoundedRectangle(cornerRadius: 10)
                    .fill(DesignSystem.Colors.backgroundCard)
            )
        }
    }
}
