//
//  PixverseTextToVideoView.swift
//  GrofyAI
//
//  Created by kissy on 2025/5/1.
//

import SwiftUI
import FlowStacks

struct PixverseTextToVideoView<Content: View>: View {

    var detail: ArtWorksCell?
    @ViewBuilder var modeContent: () -> Content
    @StateObject private var controller = PixverseTextToVideoController()
    @EnvironmentObject var navigator: FlowPathNavigator

    @EnvironmentObject private var keyboardManager: KeyboardManager
    @FocusState private var focusedField: FormFocusField?
    
    private var bottomPadding: CGFloat {
        if keyboardManager.isVisible {
//            最终高度是要减去确认按钮高度
            return keyboardManager.height - 85
        } else {
            return 0
        }
    }
    
    init(detail: ArtWorksCell? = nil, @ViewBuilder content: @escaping () -> Content) {
        self.detail = detail
        self.modeContent = content
    }
    
    var body: some View {
        VStack(spacing: 10) {
            ScrollViewReader { proxy in
                ScrollView {
                    VStack(spacing: 15) {
                        modeContent ()
                        
                        //MARK: model
                        VStack(alignment: .leading,spacing: 10) {
                            SectionHeader( title: "选择模型" )
                            StatefulDropDownPicker(
                                placeholder: "选择生成模型...",
                                options: PixverseVideoAi.T2V.Model.allCases,
                                selection: $controller.model.toOptional()
                            ) { option in
                                ModelOptionCell(
                                    icon: option.icon,
                                    icon_darkTheme: option.icon_darkTheme,
                                    describe:option.describe,
                                    displayName: option.displayName
                                )
                            }
                        }
                        //MARK: prompt
                        VStack(alignment: .leading,spacing: 10) {
                            SectionHeader(
                                title: "视频描述",
                                helpTitle: "视频描述”？",
                                helpMessage: "请在这里输入您希望在画面中看到的主要元素、场景、氛围、光线和构图等。描述越详细，生成的效果越符合您的预期。"
                            )
                            RichTextEditor(
                                placeholder: "请详细描述你的问题或想法...",
                                text: $controller.prompt,
                                maxCount: 1000
                            )
                            .focused($focusedField, equals: .prompt)
                            .id(FormFocusField.prompt)
                        }
                        .zIndex(-1)
                        
                        
                        
                        VStack(alignment: .leading, spacing: 10) {
                            //MARK: style_type
                            SectionHeader( title: "图片风格")
                            StyleSelector(
                                items: PixverseVideoAi.T2V.Style.allCases,
                                selection: $controller.style.orDefault(.style_animation)
                                
                            ) { quality, isSelected in
                                // 在这里，你可以自由定义每个单元格的 UI
                                //  RadioOptionCell 默认单元个格项
                                StyleOptionCell(
                                    title: quality.description,
                                    imageName: quality.ImageName,
                                    isSelected: isSelected
                                )
                            }
                            .padding(10)
                            .background(
                                RoundedRectangle(cornerRadius: 10)
                                    .fill(DesignSystem.Colors.backgroundCard)
                            )
                        }
                        
                        
                        // MARK: size
                        VStack(alignment: .leading, spacing: 10) {
                            SectionHeader( title: "画面大小")
                            AspectRatioSelector(
                                items: PixverseVideoAi.T2V.AspectRatio.allCases,
                                selectedItem: $controller.aspect_ratio
                            )
                            .padding(10)
                            .background(
                                RoundedRectangle(cornerRadius: 10)
                                    .fill(DesignSystem.Colors.backgroundCard)
                            )
                        }
                        
                        
                        
                        VStack(alignment: .leading, spacing: 15) {
                            // MARK: quality
                            VStack(alignment: .leading, spacing: 10) {
                                SectionHeader(title: "分辨率",custom: true)
                                    .font(.system(size: 14))
                                RadioSelector(
                                    items: PixverseVideoAi.T2V.Quality.allCases,
                                    selection: $controller.quality,
                                    rowHeight: 30
                                ) { quality, isSelected in
                                    // 在这里，你可以自由定义每个单元格的 UI
                                    //  RadioOptionCell 默认单元个格项
                                    RadioOptionCell(
                                        title: quality.description,
                                        isSelected: isSelected
                                    )
                                }
                            }
                            
                            // MARK: motion_mode
                            VStack(alignment: .leading, spacing: 10) {
                                SectionHeader(title: "运动模式",custom: true)
                                    .font(.system(size: 14))
                                RadioSelector(
                                    items: PixverseVideoAi.T2V.MotionMode.allCases
                                        .filter{ motion_mode in
                                            motion_mode.supportedDuration.contains(controller.duration)
                                        }
                                        .filter{ motion_mode in
                                            motion_mode.supportedQuality.contains(controller.quality)
                                        },
                                    selection: $controller.motion_mode.orDefault(.normal),
                                    rowHeight: 30
                                ) { quality, isSelected in
                                    // 在这里，你可以自由定义每个单元格的 UI
                                    //  RadioOptionCell 默认单元个格项
                                    RadioOptionCell(
                                        title: quality.description,
                                        isSelected: isSelected
                                    )
                                }
                            }
                            
                            // MARK: duration
                            VStack(alignment: .leading, spacing: 10) {
                                SectionHeader(title: "时长",custom: true)
                                    .font(.system(size: 14))
                                RadioSelector(
                                    items: PixverseVideoAi.T2V.Duration.allCases.filter{ duration in
                                        duration.supportedQuality.contains(controller.quality)
                                    },
                                    selection: $controller.duration,
                                    rowHeight: 30
                                ) { quality, isSelected in
                                    // 在这里，你可以自由定义每个单元格的 UI
                                    //  RadioOptionCell 默认单元个格项
                                    RadioOptionCell(
                                        title: quality.description,
                                        isSelected: isSelected
                                    )
                                }
                            }
                            
                            
                            
                            //MARK: seed
                            VStack(alignment: .leading,spacing: 10) {
                                SectionHeader( title: "随机种子 (可选)",custom: true )
                                    .font(.system(size: 14))
                                NumberField(
                                    placeholder: "请输入随机种子",
                                    value: $controller.seed,
                                    range: 0...2147483647,
                                    showRandomButton: true
                                )
                                .multilineTextAlignment(.leading)
                                .focused($focusedField, equals: .seed)
                                .id(FormFocusField.seed)
                            }
                        }
                        .padding(.horizontal,10)
                        .padding(.vertical,15)
                        .background(
                            RoundedRectangle(cornerRadius: 10)
                                .fill(DesignSystem.Colors.backgroundCard)
                        )
                        
                        //MARK: negative_prompt
                        VStack(alignment: .leading,spacing: 10) {
                            SectionHeader(
                                title: "视频反向描述",
                                helpTitle: "视频反向描述",
                                helpMessage: "描述视频中的不需要的元素、场景、氛围、光线和构图等。"
                            )
                            RichTextEditor(
                                placeholder: "请详细描述你的视频中不需要的元素...",
                                text: $controller.negative_prompt,
                                maxCount: 1000
                            )
                            .focused($focusedField, equals: .negativePrompt)
                            .id(FormFocusField.negativePrompt)
                        }
                        
                        VStack {}
                        // 在显示 Alert 时关闭键盘
                            .alert("错误", isPresented: $controller.showError) {
                                Button("确定"){}
                            } message: {
                                Text(controller.errorMessage ?? "")
                            }
                    }
                }
                .onChange(of: focusedField) { newFocus in
                    guard let focus = newFocus else {return}
                    withAnimation(.easeInOut(duration: keyboardManager.animationDuration)) {
                        proxy.scrollTo(focus, anchor: .bottom)
                    }
                }
            }
            .scrollIndicators(.hidden)
            // 在这里可以添加分割线或其他元素
            //                Divider()
            VStack {
                // 在这里可以添加分割线或其他元素
                //                Divider()
                Button( action:controller.onSubmit) {
                    HStack{
                        Spacer()
                        HStack(spacing: 10){
                            Text("确认 \( controller.credits ?? 0 )")
                                .font(.headline)
                                .padding()
                            
                            if controller.isLoading {
                                ProgressView()
                                    .tint(DesignSystem.Colors.backgroundCard)
                            }
                        }
                        Spacer()
                    }
                    .frame(maxWidth: .infinity) // 使文本容器宽度最大化
                    .foregroundColor(.white)
                }
                .background(DesignSystem.Colors.primary) // 按钮背景色
                .cornerRadius(15) // 圆角
                .frame(width: UIScreen.main.bounds.width * 0.9) // 屏幕宽度的80%
                .padding(.bottom, 30) // 底部间距
            }
            .frame(height:80)
        }
        .padding(.horizontal,15)
        .background(DesignSystem.Colors.backgroundPage)
        .tint(DesignSystem.Colors.primary)
        //处理键盘高度
        .edgesIgnoringSafeArea(.bottom)
        .padding(.bottom,  bottomPadding)
        .animation(.easeOut(duration: keyboardManager.animationDuration), value: bottomPadding)
        .onTapGesture {
            // 当点击背景时，调用这个函数
            hideKeyboard()
        }
        
        // 关键：当 controller.transactionID 不为 nil 时，导航到 PollingResultView
        .onChange(of: controller.transactionID) { newID in
                        // 如果 newID 不是 nil，说明我们得到了一个有效的ID，应该导航
            if newID != nil {
                navigator.push(Route.videoArtWorkCreationResult(transactionID: newID!,detail: nil))
            }
        }
        .onAppear{
            controller.setThreadId()
            
            guard let workInfo = detail?.inputFormat else { return }
            // 直接赋值，如果无法解析，nil-coalescing 操作符 (??) 会保留 controller 的原值
            controller.prompt = (workInfo["prompt"] as? String) ?? controller.prompt
            controller.negative_prompt = (workInfo["negative_prompt"] as? String) ?? controller.negative_prompt
            controller.seed = (workInfo["seed"] as? Int) ?? controller.seed
            
            controller.model = workInfo.decode(key: "model") ?? controller.model
            controller.duration = workInfo.decodeInt(key: "duration") ?? controller.duration
            controller.aspect_ratio = workInfo.decode(key: "aspect_ratio") ?? controller.aspect_ratio
            controller.motion_mode = workInfo.decode(key: "motion_mode") ?? controller.motion_mode
            controller.quality = workInfo.decode(key: "quality") ?? controller.quality
            controller.style = workInfo.decode(key: "style") ?? controller.style
        }
        .onDisappear {
            // 当视图消失时（例如用户返回上一页），停止轮询
            if detail?.inputFormat != nil {
                controller.resetToDefaults()
            }
        }
    }
}
