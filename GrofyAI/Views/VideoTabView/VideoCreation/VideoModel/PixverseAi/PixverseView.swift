//
//  PixverseView.swift
//  GrofyAI
//
//  Created by kissy on 2025/6/24.
//

import SwiftUI

struct PixverseView : View {
    var detail: ArtWorksCell?
    
    @State private var pixverseMode: PixverseVideoAi.Mode
    
    init(detail: ArtWorksCell? = nil) {
        func handleMode(type: CreateType?) -> PixverseVideoAi.Mode?{
            switch type {
            case .textToVideo:
                return   .t2v
            case .imageToVideo:
                return  .i2v
            default:
                return nil
            }
        }
        
        //历史
        let historyCreateType: CreateType? = CreateType(rawValue: detail?.agentSubtype ?? "")
        let historyMode: PixverseVideoAi.Mode? = handleMode(type: historyCreateType)
        
        self.detail = detail
        self._pixverseMode = State(initialValue: historyMode ?? FormDataStorage.loadModelType(as: PixverseVideoAi.Mode.self) ?? .t2v)
    }
    
    var body: some View {
        ZStack{
            switch pixverseMode {
            case .t2v:
                PixverseTextToVideoView(detail: detail){
                    modeContent
                }
            case .i2v:
                PixverseImageToVideoView(detail: detail){
                    modeContent
                }
            }
        }
        .animation(.easeInOut(duration: 0.5), value: pixverseMode)
        .onChange(of: pixverseMode){ mode in
            FormDataStorage.saveModelType(modelType: mode)
        }
    }
    
    @ViewBuilder
    private var modeContent: some View {
        VStack(alignment:.leading){
            SectionHeader( title: "模式")
            HStack{
                GenericSelectorView(
                    options: Array(PixverseVideoAi.Mode.allCases),
                    selectedOption: $pixverseMode
                )
                Spacer()
            }
            .padding(10)
            .background(
                RoundedRectangle(cornerRadius: 10)
                    .fill(DesignSystem.Colors.backgroundCard)
            )
        }
    }
}
