//
//  Untitled.swift
//  GrofyAI
//
//  Created by kissy on 2025/5/1.
//

import SwiftUI
import FlowStacks

struct WanxTextToVideoView<Content: View>: View {
    var detail: ArtWorksCell?
    @ViewBuilder var modeContent: () -> Content
    @StateObject private var controller = WanxTextToVideoController()
    @EnvironmentObject var navigator: FlowPathNavigator
    
    @EnvironmentObject private var keyboardManager: KeyboardManager
    @FocusState private var focusedField: FormFocusField?
    
    private var bottomPadding: CGFloat {
        if keyboardManager.isVisible {
//            最终高度是要减去确认按钮高度
            return keyboardManager.height - 85
        } else {
            return 0
        }
    }
    
    init(detail: ArtWorksCell? = nil, @ViewBuilder content: @escaping () -> Content) {
        self.detail = detail
        self.modeContent = content
    }
    
    var body: some View {
        VStack(spacing: 10) {
            ScrollViewReader { proxy in
                ScrollView {
                    VStack(spacing: 15) {
                        modeContent ()
                        
                        //MARK: model
                        VStack(alignment: .leading,spacing: 10) {
                            SectionHeader( title: "选择模型" )
                            StatefulDropDownPicker(
                                placeholder: "选择生成模型...",
                                options: WanxVideoAi.T2V.Model.allCases,
                                selection: $controller.model.toOptional()
                            ) { option in
                                ModelOptionCell(
                                    icon: option.icon,
                                    icon_darkTheme: option.icon_darkTheme,
                                    describe:option.describe,
                                    displayName: option.displayName
                                )
                            }
                        }
                        //MARK: prompt
                        VStack(alignment: .leading,spacing: 10) {
                            SectionHeader(
                                title: "视频描述",
                                helpTitle: "视频描述",
                                helpMessage: "描述视频中的主要元素、场景、氛围、光线和构图等。"
                            )
                            RichTextEditor(
                                placeholder: "请详细描述你的视频...",
                                text: $controller.prompt,
                                maxCount: 1000
                            )
                            .focused($focusedField, equals: .prompt)
                            .id(FormFocusField.prompt)
                        }
                        .zIndex(-1)
                        // MARK: size
                        VStack(alignment: .leading, spacing: 10) {
                            SectionHeader( title: "画面大小")
                            AspectRatioSelector(
                                items: WanxVideoAi.T2V.Size.allCases.filter{$0.supportedResolution == controller.resolution},
                                selectedItem: $controller.size
                            )
                            .padding(10)
                            .background(
                                RoundedRectangle(cornerRadius: 10)
                                    .fill(DesignSystem.Colors.backgroundCard)
                            )
                        }
                        
                        
                        
                        VStack(alignment: .leading, spacing: 15) {
                            // MARK: resolution
                            VStack(alignment: .leading, spacing: 10) {
                                SectionHeader(title: "分辨率",custom: true)
                                    .font(.system(size: 14))
                                RadioSelector(
                                    items: WanxVideoAi.T2V.Resolution.allCases.filter{ resolution in
                                        resolution.supportedModels.contains(controller.model)
                                    },
                                    selection: $controller.resolution,
                                    rowHeight: 30
                                ) { quality, isSelected in
                                    // 在这里，你可以自由定义每个单元格的 UI
                                    //  RadioOptionCell 默认单元个格项
                                    RadioOptionCell(
                                        title: quality.description,
                                        isSelected: isSelected
                                    )
                                }
                            }
                            
                            //MARK: prompt_upsampling
                            HStack{
                                SectionHeader( title: "提示词拓展",custom: true )
                                    .font(.system(size:14))
                                Spacer()
                                Toggle("", isOn: $controller.prompt_extend.orDefault(false))
                                    .foregroundColor(DesignSystem.Colors.textSecondary)
                                    .scaleEffect(0.65)
                                    .padding(.trailing,10)
                                    .frame(width:25)
                            }
                            
                            //MARK: seed
                            VStack(alignment: .leading,spacing: 10) {
                                SectionHeader( title: "随机种子 (可选)",custom: true )
                                    .font(.system(size: 14))
                                NumberField(
                                    placeholder: "请输入随机种子",
                                    value: $controller.seed,
                                    range: 0...2147483647,
                                    showRandomButton: true
                                )
                                .multilineTextAlignment(.leading)
                                .padding(.bottom,1)
                                .focused($focusedField, equals: .seed)
                                .id(FormFocusField.seed)
                            }
                        }
                        .padding(.horizontal,10)
                        .padding(.vertical,15)
                        .background(
                            RoundedRectangle(cornerRadius: 10)
                                .fill(DesignSystem.Colors.backgroundCard)
                        )
                        
                        
                        VStack {}
                        // 在显示 Alert 时关闭键盘
                            .alert("错误", isPresented: $controller.showError) {
                                Button("确定"){}
                            } message: {
                                Text(controller.errorMessage ?? "")
                            }
                    }
                }
                .onChange(of: focusedField) { newFocus in
                    guard let focus = newFocus else {return}
                    withAnimation(.easeInOut(duration: keyboardManager.animationDuration)) {
                        proxy.scrollTo(focus, anchor: .bottom)
                    }
                }
            }
            .scrollIndicators(.hidden)
            .onTapGesture {
                // 当点击背景时，调用这个函数
                hideKeyboard()
            }
            

            VStack {
                Button( action:controller.onSubmit) {
                    HStack{
                        Spacer()
                        HStack(spacing: 10){
                            Text("确认 \( controller.credits ?? 0 )")
                                .font(.headline)
                                .padding()
                            
                            if controller.isLoading {
                                ProgressView()
                                    .tint(DesignSystem.Colors.backgroundCard)
                            }
                        }
                        Spacer()
                    }
                    .frame(maxWidth: .infinity) // 使文本容器宽度最大化
                    .foregroundColor(.white)
                }
                .background(DesignSystem.Colors.primary) // 按钮背景色
                .cornerRadius(15) // 圆角
                .frame(width: UIScreen.main.bounds.width * 0.9) // 屏幕宽度的80%
                .padding(.bottom, 30) // 底部间距
            }
            .frame(height:80)
        }
        .padding(.horizontal,15)
        .background(DesignSystem.Colors.backgroundPage)
        .tint(DesignSystem.Colors.primary)
        
        //处理键盘高度
        .edgesIgnoringSafeArea(.bottom)
        .padding(.bottom,  bottomPadding)
        .animation(.easeOut(duration: keyboardManager.animationDuration), value: bottomPadding)

        
        // 关键：当 controller.transactionID 不为 nil 时，导航到 PollingResultView
        .onChange(of: controller.transactionID) { newID in
                        // 如果 newID 不是 nil，说明我们得到了一个有效的ID，应该导航
            if newID != nil {
                navigator.push(Route.videoArtWorkCreationResult(transactionID: newID!,detail: nil))
            }
        }
        .onAppear{
            controller.setThreadId()
            
            guard let workInfo = detail?.inputFormat else { return }
            // 直接赋值，如果无法解析，nil-coalescing 操作符 (??) 会保留 controller 的原值
            controller.prompt = (workInfo["prompt"] as? String) ?? controller.prompt
            controller.prompt_extend = (workInfo["prompt_extend"] as? Bool) ?? controller.prompt_extend
            controller.seed = (workInfo["seed"] as? Int) ?? controller.seed
            
            controller.model = workInfo.decode(key: "model") ?? controller.model
            controller.duration = workInfo.decodeInt(key: "duration") ?? controller.duration
            controller.size = workInfo.decode(key: "size") ?? controller.size
            controller.resolution = workInfo.decode(key: "resolution") ?? controller.resolution
        }
        .onDisappear {
            // 当视图消失时（例如用户返回上一页），停止轮询
            if detail?.inputFormat != nil {
                controller.resetToDefaults()
            }
        }
    }
}
