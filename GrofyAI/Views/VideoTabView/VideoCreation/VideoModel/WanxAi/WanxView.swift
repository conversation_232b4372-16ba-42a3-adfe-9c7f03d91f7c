//
//  WanxView.swift
//  GrofyAI
//
//  Created by kissy on 2025/6/24.
//

import SwiftUI
//import FlowStacks

struct WanxView : View {
    var detail: ArtWorksCell?
    var createType: CreateType?
    
    @State private var wanxMode:WanxVideoAi.Mode
    
    init(detail: ArtWorksCell? = nil, createType: CreateType? = nil) {
        func handleMode(type: CreateType?) -> WanxVideoAi.Mode?{
            switch type {
            case .textToVideo:
                return   .t2v
            case .imageToVideo:
                return  .i2v
            default:
                return nil
            }
        }
        
        //初始值
        let initialMode: WanxVideoAi.Mode = handleMode(type: createType) ?? .t2v
        
        //历史
        let historyCreateType: CreateType? = CreateType(rawValue: detail?.agentSubtype ?? "")
        let historyMode: WanxVideoAi.Mode? = handleMode(type: historyCreateType)
        
        self.detail = detail
        self._wanxMode = State(initialValue: historyMode ?? FormDataStorage.loadModelType(as: WanxVideoAi.Mode.self) ?? initialMode)
    }
    


    var body: some View {
        ZStack{
            switch wanxMode {
            case .t2v:
                WanxTextToVideoView(detail: detail){
                    modeContent
                }
                
            case .i2v:
                WanxImageToVideoView(detail: detail){
                    modeContent
                }
            }
        }
        .animation(.easeInOut(duration: 0.5), value: wanxMode)
        .onChange(of: wanxMode){ mode in
            FormDataStorage.saveModelType(modelType: mode)
        }
    }
    
    @ViewBuilder
    private var modeContent: some View {
        VStack(alignment:.leading){
            SectionHeader( title: "模式")
            HStack{
                GenericSelectorView(
                    options: Array(WanxVideoAi.Mode.allCases),
                    selectedOption: $wanxMode
                )
                Spacer()
            }
            .padding(10)
            .background(
                RoundedRectangle(cornerRadius: 10)
                    .fill(DesignSystem.Colors.backgroundCard)
            )
        }
    }
}
