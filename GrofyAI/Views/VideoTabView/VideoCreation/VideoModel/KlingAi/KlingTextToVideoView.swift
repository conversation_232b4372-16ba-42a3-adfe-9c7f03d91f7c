//
//  KlingTextToVideoView.swift
//  GrofyAI
//
//  Created by kissy on 2025/5/1.
//

import SwiftUI
import FlowStacks

struct KlingTextToVideoView<Content: View>: View {
    var detail: ArtWorksCell?
    @ViewBuilder var modeContent: () -> Content
    @StateObject private var controller = KlingTextToVideoController()
    @EnvironmentObject var navigator: FlowPathNavigator
    
    @EnvironmentObject private var keyboardManager: KeyboardManager
    @FocusState private var focusedField: FormFocusField?
    
    private var bottomPadding: CGFloat {
        if keyboardManager.isVisible {
//            最终高度是要减去确认按钮高度
            return keyboardManager.height - 85
        } else {
            return 0
        }
    }

    init(detail: ArtWorksCell? = nil, @ViewBuilder content: @escaping () -> Content) {
        self.detail = detail
        self.modeContent = content
    }
    
    var body: some View {
        VStack(spacing: 10) {
            ScrollViewReader { proxy in
                ScrollView {
                    VStack(spacing: 15) {
                        modeContent ()
                        
                        //MARK: model
                        VStack(alignment: .leading,spacing: 10) {
                            SectionHeader( title: "选择模型" )
                            StatefulDropDownPicker(
                                placeholder: "选择生成模型...",
                                options: KlingVideoAi.T2V.Model.allCases,
                                selection: $controller.model_name.toOptional()
                            ) { option in
                                ModelOptionCell(
                                    icon: option.icon,
                                    icon_darkTheme: option.icon_darkTheme,
                                    describe:option.describe,
                                    displayName: option.displayName
                                )
                            }
                        }
                        //MARK: prompt
                        VStack(alignment: .leading,spacing: 10) {
                            SectionHeader(
                                title: "视频描述",
                                helpTitle: "视频描述",
                                helpMessage: "描述视频中的主要元素、场景、氛围、光线和构图等。"
                            )
                            RichTextEditor(
                                placeholder: "请详细描述你的视频...",
                                text: $controller.prompt,
                                maxCount: 1000
                            )
                            .focused($focusedField, equals: .prompt)
                            .id(FormFocusField.prompt)
                        }
                        .zIndex(-1)
                        
                        
                        
                        // MARK: size
                        VStack(alignment: .leading, spacing: 10) {
                            SectionHeader( title: "画面大小")
                            AspectRatioSelector(
                                items: KlingVideoAi.T2V.AspectRatio.allCases,
                                selectedItem: $controller.aspect_ratio.orDefault(.ratio_1_1)
                            )
                            .padding(10)
                            .background(
                                RoundedRectangle(cornerRadius: 10)
                                    .fill(DesignSystem.Colors.backgroundCard)
                            )
                        }
                        
                        //MARK: type
                        VStack(alignment: .leading, spacing: 15) {
                            // MARK: mode
                            VStack(alignment: .leading, spacing: 10) {
                                SectionHeader(title: "模式",custom: true)
                                    .font(.system(size: 14))
                                RadioSelector(
                                    items: KlingVideoAi.T2V.Mode.allCases.filter{ mode in
                                        mode.supportedModel.contains(controller.model_name)
                                    },
                                    selection: $controller.mode.orDefault(.std),
                                    rowHeight: 30
                                ) { quality, isSelected in
                                    // 在这里，你可以自由定义每个单元格的 UI
                                    //  RadioOptionCell 默认单元个格项
                                    RadioOptionCell(
                                        title: quality.description,
                                        isSelected: isSelected
                                    )
                                }
                            }
                            
                            // MARK: duration
                            VStack(alignment: .leading, spacing: 10) {
                                SectionHeader(title: "时长",custom: true)
                                    .font(.system(size: 14))
                                RadioSelector(
                                    items: KlingVideoAi.T2V.Duration.allCases,
                                    selection: $controller.duration.orDefault(.five),
                                    rowHeight: 30
                                ) { quality, isSelected in
                                    // 在这里，你可以自由定义每个单元格的 UI
                                    //  RadioOptionCell 默认单元个格项
                                    RadioOptionCell(
                                        title: quality.description,
                                        isSelected: isSelected
                                    )
                                }
                            }
                            
                            if controller.mode == .std ,
                               controller.duration == .five,
                               controller.model_name == .kling_v1{
                                VStack(alignment: .leading,spacing: 10) {
                                    SectionHeader( title: "运镜方式" )
                                    StatefulDropDownPicker(
                                        placeholder: "选择运镜方式...",
                                        options: KlingVideoAi.T2V.CameraMovementType.allCases,
                                        selection: $controller.type
                                    ) { option in
                                        HStack(spacing: 15) {
                                            Text(option.description)
                                                .font(.system(size: 14, weight: .medium))
                                                .fontWeight(.medium)
                                            
                                            Spacer()
                                        }
                                    }
                                }.zIndex(9999)
                                
                                //MAKR
                                PixCameraMovementConfig(show: controller.type == .simple)
                            }
                            
                            // MARK: cfg_scale
                            VStack(alignment: .leading,spacing: 10) {
                                TitledSliderWithInfo(
                                    title: "自由度",
                                    value: $controller.cfg_scale.orDefault(0.0), // 绑定到 Double
                                    in: 0.0...1.0,       // 范围是 Double
                                    step: 0.1,         // 步长是 Double
                                    infoTitle:"自由度",
                                    infoMessage:"值越大，模型自由度越小，与用户输入的提示词相关性越强"
                                )
                            }
                        }
                        .padding(.horizontal,10)
                        .padding(.vertical,15)
                        .background(
                            RoundedRectangle(cornerRadius: 10)
                                .fill(DesignSystem.Colors.backgroundCard)
                        )
                        
                        //MARK: negative_prompt
                        VStack(alignment: .leading,spacing: 10) {
                            SectionHeader(
                                title: "反向提示词",
                                helpTitle: "反向提示词",
                                helpMessage: "描述视频中的不需要的元素、场景、氛围、光线和构图等。"
                            )
                            RichTextEditor(
                                placeholder: "请详细描述你的视频中不需要的元素...",
                                text: $controller.negative_prompt,
                                maxCount: 1000
                            )
                            .focused($focusedField, equals: .negativePrompt)
                            .id(FormFocusField.negativePrompt)
                        }
                        .zIndex(-1)
                        
                        VStack {}
                        // 在显示 Alert 时关闭键盘
                            .alert("错误", isPresented: $controller.showError) {
                                Button("确定"){}
                            } message: {
                                Text(controller.errorMessage ?? "")
                            }
                    }
                }
                .onChange(of: focusedField) { newFocus in
                    guard let focus = newFocus else {return}
                    withAnimation(.easeInOut(duration: keyboardManager.animationDuration)) {
                        proxy.scrollTo(focus, anchor: .bottom)
                    }
                }
            }
            .scrollIndicators(.hidden)
            
            VStack {
                Button( action:controller.onSubmit) {
                    HStack{
                        Spacer()
                        HStack(spacing: 10){
                            Text("确认 \( controller.credits ?? 0 )")
                                .font(.headline)
                                .padding()
                            
                            if controller.isLoading {
                                ProgressView()
                                    .tint(DesignSystem.Colors.backgroundCard)
                            }
                        }
                        Spacer()
                    }
                    .frame(maxWidth: .infinity) // 使文本容器宽度最大化
                    .foregroundColor(.white)
                }
                .background(DesignSystem.Colors.primary) // 按钮背景色
                .cornerRadius(15) // 圆角
                .frame(width: UIScreen.main.bounds.width * 0.9) // 屏幕宽度的80%
                .padding(.bottom, 30) // 底部间距
            }
            .frame(height:80)
        }
        .padding(.horizontal,15)
        .background(DesignSystem.Colors.backgroundPage)
        .tint(DesignSystem.Colors.primary)
        //处理键盘高度
        .edgesIgnoringSafeArea(.bottom)
        .padding(.bottom,  bottomPadding)
        .animation(.easeOut(duration: keyboardManager.animationDuration), value: bottomPadding)

        .onTapGesture {
            // 当点击背景时，调用这个函数
            hideKeyboard()
        }
        // 关键：当 controller.transactionID 不为 nil 时，导航到 PollingResultView
        .onChange(of: controller.transactionID) { newID in
                        // 如果 newID 不是 nil，说明我们得到了一个有效的ID，应该导航
            if newID != nil {
                navigator.push(Route.videoArtWorkCreationResult(transactionID: newID!,detail: nil))
            }
        }
        .onAppear{
            controller.setThreadId()
            
            guard let workInfo = detail?.inputFormat else { return }
            // 直接赋值，如果无法解析，nil-coalescing 操作符 (??) 会保留 controller 的原值
            controller.prompt = (workInfo["prompt"] as? String) ?? controller.prompt
            controller.negative_prompt = (workInfo["negative_prompt"] as? String) ?? controller.negative_prompt
            controller.cfg_scale = (workInfo["prompt"] as? Double) ?? controller.cfg_scale
            controller.callback_url = (workInfo["callback_url"] as? String) ?? controller.callback_url

            controller.model_name = workInfo.decode(key: "model_name") ?? controller.model_name
            controller.mode = workInfo.decode(key: "mode") ?? controller.mode
            controller.type = workInfo.decode(key: "type") ?? controller.type
            controller.aspect_ratio = workInfo.decode(key: "aspect_ratio") ?? controller.aspect_ratio
            controller.duration = workInfo.decodeInt(key: "duration") ?? controller.duration
            controller.config = workInfo.decodeKlingConfig(key: "config") ?? controller.config
            
            updateCameraOption(from: controller.config )
            //根据cameraConfig中不为零的项确定CameraConfigOption的
        }
        .onDisappear {
            // 当视图消失时（例如用户返回上一页），停止轮询
            if detail?.inputFormat != nil {
                controller.resetToDefaults()
            }
        }
        
    }
    
    private func updateCameraOption(from config: KlingConfig) {
        
        print("ahhe",config)
        // 使用 if-else if 来找到第一个非默认值的选项
        if config.horizontal != 0 {
            controller.cameraConfigOption = .horizontal
        } else if config.vertical != 0 {
            controller.cameraConfigOption = .vertical
        } else if config.pan != 0 {
            controller.cameraConfigOption = .pan
        } else if config.tilt != 0 {
            controller.cameraConfigOption = .tilt
        } else if config.roll != 0 {
            controller.cameraConfigOption = .roll
        } else if config.zoom != 0 {
            controller.cameraConfigOption = .zoom
        } else {
            // 如果所有值都是默认值，设置一个 'none' 状态
            controller.cameraConfigOption = .none
        }
    }
    
    @ViewBuilder
    private func PixCameraMovementConfig (show isShow: Bool) -> some View {
        if isShow {
            VStack(alignment: .leading,spacing: 10) {
                SectionHeader( 
                    title: "运镜",
                    helpTitle: "运镜",
                    helpMessage: "未指定运镜方式时，模型将根据图片或描述智能匹配。"
                )
                StatefulDropDownPicker(
                    placeholder: "选择运镜...",
                    options: CameraConfigOption.allCases,
                    selection: $controller.cameraConfigOption.toOptional()
                ) { option in
                    HStack(spacing: 15) {
                        Text(option.description)
                            .font(.system(size: 14, weight: .medium))
                            .fontWeight(.medium)
                        
                        Spacer()
                    }
                    
                }
            }.zIndex(999)
            
            if controller.cameraConfigOption != .none {
                CameraMovementView(
                    mode: controller.cameraConfigOption,
                    offsetWidth: $controller.config.horizontal.orDefault(0),
                    offsetHeight: $controller.config.vertical.orDefault(0),
                    scale: $controller.config.pan.orDefault(0),
                    rotationX: $controller.config.tilt.orDefault(0),
                    rotationY: $controller.config.roll.orDefault(0),
                    rotationZ: $controller.config.zoom.orDefault(0)
                )
            }
            
        }
    }
}

