//
//  KingView.swift
//  GrofyAI
//
//  Created by kissy on 2025/6/24.
//


import SwiftUI

struct KlingView : View {
    var detail: ArtWorksCell?
    
    @State private var klingMode: KlingVideoAi.Mode

    init(detail: ArtWorksCell? = nil) {
        func handleMode(type: CreateType?) -> KlingVideoAi.Mode?{
            switch type {
            case .textToVideo:
                return   .t2v
            case .imageToVideo:
                return  .i2v
            default:
                return nil
            }
        }
        
        //历史
        let historyCreateType: CreateType? = CreateType(rawValue: detail?.agentSubtype ?? "")
        let historyMode: KlingVideoAi.Mode? = handleMode(type: historyCreateType)
        
        self.detail = detail
        self._klingMode = State(initialValue: historyMode ?? FormDataStorage.loadModelType(as: KlingVideoAi.Mode.self) ?? .t2v)
    }
    
    var body: some View {
        ZStack{
            switch klingMode {
            case .t2v:
                KlingTextToVideoView(detail: detail){
                    modeContent
                }
            case .i2v:
                KlingImageToVideoView(detail: detail){
                    modeContent
                }
            }
 
        }
        .animation(.easeInOut(duration: 0.5), value: klingMode)
        .onChange(of: klingMode){ mode in
            FormDataStorage.saveModelType(modelType: mode)
        }
    }
    
    @ViewBuilder
    private var modeContent: some View {
        VStack(alignment:.leading){
            SectionHeader( title: "模式")
            HStack{
                GenericSelectorView(
                    options: Array(KlingVideoAi.Mode.allCases),
                    selectedOption: $klingMode
                )
                Spacer()
            }
            .padding(10)
            .background(
                RoundedRectangle(cornerRadius: 10)
                    .fill(DesignSystem.Colors.backgroundCard)
            )
        }
    }
}


