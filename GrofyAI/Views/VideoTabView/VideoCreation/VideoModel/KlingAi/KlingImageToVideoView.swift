//
//  KlingImageToVideoView.swift
//  GrofyAI
//
//  Created by kissy on 2025/5/1.
//

import SwiftUI
import FlowStacks

struct KlingImageToVideoView<Content: View>: View {
    var detail: ArtWorksCell?
    @ViewBuilder var modeContent: () -> Content
    @StateObject private var controller = KlingImageToVideoController()
    @EnvironmentObject var navigator: FlowPathNavigator
    
    @EnvironmentObject private var keyboardManager: KeyboardManager
    @FocusState private var focusedField: FormFocusField?
    
    private var bottomPadding: CGFloat {
        if keyboardManager.isVisible {
//            最终高度是要减去确认按钮高度
            return keyboardManager.height - 85
        } else {
            return 0
        }
    }

    init(detail: ArtWorksCell? = nil, @ViewBuilder content: @escaping () -> Content) {
        self.detail = detail
        self.modeContent = content
    }
    
    var body: some View {
        VStack(spacing: 10) {
            ScrollViewReader { proxy in
                ScrollView {
                    VStack(spacing: 15) {
                        modeContent ()
                        
                        //MARK: model
                        VStack(alignment: .leading,spacing: 10) {
                            SectionHeader( title: "选择模型" )
                            StatefulDropDownPicker(
                                placeholder: "选择生成模型...",
                                options: KlingVideoAi.I2V.Model.allCases,
                                selection: $controller.model_name.toOptional()
                            ) { option in
                                ModelOptionCell(
                                    icon: option.icon,
                                    icon_darkTheme: option.icon_darkTheme,
                                    describe:option.describe,
                                    displayName: option.displayName
                                )
                            }
                        }
                        //MARK: prompt
                        VStack(alignment: .leading,spacing: 10) {
                            SectionHeader(
                                title: "视频描述",
                                helpTitle: "视频描述",
                                helpMessage: "描述视频中的主要元素、场景、氛围、光线和构图等。"
                            )
                            RichTextEditor(
                                placeholder: "请详细描述你的视频...",
                                text: $controller.prompt,
                                maxCount: 1000
                            )
                            .focused($focusedField, equals: .prompt)
                            .id(FormFocusField.prompt)
                        }
                        .zIndex(-1)
                        
                        // MARK: size
                        VStack(alignment: .leading, spacing: 10) {
                            SectionHeader( title: "画面大小")
                            AspectRatioSelector(
                                items: KlingVideoAi.I2V.AspectRatio.allCases,
                                selectedItem: $controller.aspect_ratio.orDefault(.ratio_1_1)
                            )
                            .padding(10)
                            .background(
                                RoundedRectangle(cornerRadius: 10)
                                    .fill(DesignSystem.Colors.backgroundCard)
                            )
                            
                        }
                        
                        //MARK: type
                        VStack(alignment: .leading, spacing: 15) {
                            
                            // MARK: cfg_scale
                            VStack(alignment: .leading,spacing: 10) {
                                TitledSliderWithInfo(
                                    title: "自由度",
                                    value: $controller.cfg_scale.orDefault(0.0), // 绑定到 Double
                                    in: 0.0...1.0,       // 范围是 Double
                                    step: 0.1,         // 步长是 Double
                                    infoTitle:"自由度",
                                    infoMessage:"值越大，模型自由度越小，与用户输入的提示词相关性越强"
                                )
                            }
                            
                            // MARK: mode
                            VStack(alignment: .leading, spacing: 10) {
                                SectionHeader(title: "模式",custom: true)
                                    .font(.system(size: 14))
                                RadioSelector(
                                    items: KlingVideoAi.I2V.Mode.allCases,
                                    selection: $controller.mode.orDefault(.std),
                                    rowHeight: 30
                                ) { quality, isSelected in
                                    // 在这里，你可以自由定义每个单元格的 UI
                                    //  RadioOptionCell 默认单元个格项
                                    RadioOptionCell(
                                        title: quality.description,
                                        isSelected: isSelected
                                    )
                                }
                            }
                            
                            // MARK: duration
                            VStack(alignment: .leading, spacing: 10) {
                                SectionHeader(title: "时长",custom: true)
                                    .font(.system(size: 14))
                                RadioSelector(
                                    items: KlingVideoAi.I2V.Duration.allCases,
                                    selection: $controller.duration.orDefault(.five),
                                    rowHeight: 30
                                ) { quality, isSelected in
                                    // 在这里，你可以自由定义每个单元格的 UI
                                    //  RadioOptionCell 默认单元个格项
                                    RadioOptionCell(
                                        title: quality.description,
                                        isSelected: isSelected
                                    )
                                }
                            }
                            
                        }
                        .padding(.horizontal,10)
                        .padding(.vertical,15)
                        .background(
                            RoundedRectangle(cornerRadius: 10)
                                .fill(DesignSystem.Colors.backgroundCard)
                        )
                        
                        
                        //MARK: 参考图
                        if (controller.model_name == .kling_v1 && controller.duration != .ten) || (controller.model_name == .kling_v1_6 && controller.mode != .std ) {
                            VStack(alignment: .leading,spacing: 10) {
                                SectionHeader( title: "参考图" )
                                HStack{
                                    Spacer()
                                    VStack{
                                        SectionHeader( title: "首帧图" )
                                        SingleImageUploader(
                                            imageUrl: $controller.image,
                                            threadId: controller.threadId
                                        )
                                    }
                                    Spacer()
                                    VStack{
                                        SectionHeader( title: "尾帧图" )
                                        SingleImageUploader(
                                            imageUrl: $controller.image_tail,
                                            threadId: controller.threadId
                                        )
                                    }
                                    Spacer()
                                }
                                .padding(10)
                                .background(
                                    RoundedRectangle(cornerRadius: 10)
                                        .fill(DesignSystem.Colors.backgroundCard)
                                )
                            }
                        }
                        
                        
                        //MARK: prompt
                        VStack(alignment: .leading,spacing: 10) {
                            SectionHeader(
                                title: "反向提示词",
                                helpTitle: "反向提示词",
                                helpMessage: "描述视频中的不需要的元素、场景、氛围、光线和构图等。"
                            )
                            RichTextEditor(
                                placeholder: "请详细描述你的视频中不需要的元素...",
                                text: $controller.negative_prompt,
                                maxCount: 1000
                            )
                            .focused($focusedField, equals: .negativePrompt)
                            .id(FormFocusField.negativePrompt)
                        }
                        
                        
                        VStack {}
                        // 在显示 Alert 时关闭键盘
                            .alert("错误", isPresented: $controller.showError) {
                                Button("确定"){}
                            } message: {
                                Text(controller.errorMessage ?? "")
                            }
                    }
                }
                .onChange(of: focusedField) { newFocus in
                    guard let focus = newFocus else {return}
                    withAnimation(.easeInOut(duration: keyboardManager.animationDuration)) {
                        proxy.scrollTo(focus, anchor: .bottom)
                    }
                }
            }
            .scrollIndicators(.hidden)
            
            VStack {
                Button( action:controller.onSubmit) {
                    HStack{
                        Spacer()
                        HStack(spacing: 10){
                            Text("确认 \( controller.credits ?? 0 )")
                                .font(.headline)
                                .padding()
                            
                            if controller.isLoading {
                                ProgressView()
                                    .tint(DesignSystem.Colors.backgroundCard)
                            }
                        }
                        Spacer()
                    }
                    .frame(maxWidth: .infinity) // 使文本容器宽度最大化
                    .foregroundColor(.white)
                }
                .background(DesignSystem.Colors.primary) // 按钮背景色
                .cornerRadius(15) // 圆角
                .frame(width: UIScreen.main.bounds.width * 0.9) // 屏幕宽度的80%
                .padding(.bottom, 30) // 底部间距
            }
            .frame(height:80)
        }
        .padding(.horizontal,15)
        .background(DesignSystem.Colors.backgroundPage)
        .tint(DesignSystem.Colors.primary)
        //处理键盘高度
        .edgesIgnoringSafeArea(.bottom)
        .padding(.bottom,  bottomPadding)
        .animation(.easeOut(duration: keyboardManager.animationDuration), value: bottomPadding)
        .onTapGesture {
            // 当点击背景时，调用这个函数
            hideKeyboard()
        }

        // 关键：当 controller.transactionID 不为 nil 时，导航到 PollingResultView
        .onChange(of: controller.transactionID) { newID in
                        // 如果 newID 不是 nil，说明我们得到了一个有效的ID，应该导航
            if newID != nil {
                navigator.push(Route.videoArtWorkCreationResult(transactionID: newID!,detail: nil))
            }
        }
        .onAppear{
            controller.setThreadId()
            
            guard let workInfo = detail?.inputFormat else { return }
            // 直接赋值，如果无法解析，nil-coalescing 操作符 (??) 会保留 controller 的原值
            controller.prompt = (workInfo["prompt"] as? String) ?? controller.prompt
            controller.negative_prompt = (workInfo["negative_prompt"] as? String) ?? controller.negative_prompt
            controller.cfg_scale = (workInfo["cfg_scale"] as? Double) ?? controller.cfg_scale
            controller.callback_url = (workInfo["callback_url"] as? String) ?? controller.callback_url
            controller.image = (workInfo["image"] as? String) ?? controller.image
            controller.image_tail = (workInfo["image_tail"] as? String) ?? controller.image_tail
            
            controller.model_name = workInfo.decode(key: "model_name") ?? controller.model_name
            controller.mode = workInfo.decode(key: "mode") ?? controller.mode
            controller.type = workInfo.decode(key: "type") ?? controller.type
            controller.aspect_ratio = workInfo.decode(key: "aspect_ratio") ?? controller.aspect_ratio
            controller.duration = workInfo.decodeInt(key: "duration") ?? controller.duration
            controller.config = workInfo.decodeKlingConfig(key: "config") ?? controller.config
        }
        .onDisappear {
            // 当视图消失时（例如用户返回上一页），停止轮询
            if detail?.inputFormat != nil {
                controller.resetToDefaults()
            }
        }
    }
}

