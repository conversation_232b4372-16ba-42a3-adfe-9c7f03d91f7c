//
//  EffectVideoView.swift
//  GrofyAI
//
//  Created by kissy on 2025/5/1.
//

import SwiftUI
import FlowStacks

struct CreateEffectView: View {
    var detail: ArtWorksCell?
    var type: EffectVideoAi.EffectType?
    
    @Environment(\.colorScheme) private var colorScheme
    @StateObject private var controller = EffectAiController()
    @EnvironmentObject var navigator: FlowPathNavigator
    
    init(type: EffectVideoAi.EffectType? = nil,detail: ArtWorksCell? = nil) {
        self.detail = detail
        self.type = type
    }
    
    // 辅助函数：获取顶部安全区域高度
    private func safeAreaTopInset() -> CGFloat {
        let keyWindow = UIApplication.shared.connectedScenes
            .filter({ $0.activationState == .foregroundActive })
            .map({ $0 as? UIWindowScene })
            .compactMap({ $0 })
            .first?.windows
            .filter({ $0.isKeyWindow }).first
        return keyWindow?.safeAreaInsets.top ?? 0
    }
    
    var body: some View {
        ZStack{
            if colorScheme == .light {
                VStack{
                    Color.clear
                        .background( // 使用全屏背景层替代Spacer方案
                            Image("ImageNavBackground")
                                .resizable() // 关键：使图片可缩放
                                .aspectRatio(contentMode: .fill) // 填充整个区域
                                .edgesIgnoringSafeArea(.all) // 忽略所有安全区域
                        )
                        .frame(height: safeAreaTopInset())
                    Spacer()
                }
            }
            
            VStack(spacing: 0) {
                //自定义导航栏
                VStack(spacing: 0) {
                    //导航栏主体
                    HStack {
                        Button(action: {
                            navigator.push(Route.effectVideo)
                        }) {
                            Image(systemName: "chevron.backward")
                                .font(.title3)
                        }
                        
                        Spacer()
                    }
                    .foregroundStyle(DesignSystem.Colors.textPrimary)
                    //使用 overlay 实现完美居中
                    .overlay(
                        // 标题视图在 HStack 的中间
                        Text(controller.type.description)
                            .font(.system(size: 18, weight: .medium))
                            // 这里可以覆盖父视图的样式
                            .foregroundStyle(DesignSystem.Colors.textPrimary)
                    )
                    // 4. 使用 padding 替代固定的 frame height
                    .padding(.top,10)
                    .padding(.bottom,20)
                    .padding(.horizontal,15)
                    .frame(maxWidth: .infinity) // 确保 HStack 占满宽度
                    
                }
                .frame(maxWidth: .infinity)
                
                // 顶部的可滚动标签栏
                createEffectContent()
            }
        }
        .ignoresSafeArea(edges: .bottom)
        .navigationBarHidden(true)
        .background(DesignSystem.Colors.backgroundPage)
    }
    
    
    private func createEffectContent() -> some View {
        VStack(spacing: 10) {
            ScrollView {
                VStack(spacing: 15) {
                    //MARK: prompt
                    switch controller.show {
                    case .single :
                        //MARK: image_url
                        VStack(alignment: .leading,spacing: 10) {
                            VStack{
                                SingleEffectImageUploader(
                                    imageUrl: $controller.image.orDefault(""),
                                    threadId: controller.threadId
                                )
                            }
                            .padding(10)
                            .background(
                                RoundedRectangle(cornerRadius: 10)
                                    .fill(DesignSystem.Colors.backgroundCard)
                            )
                        }
                    case .multiple:
                        VStack(alignment: .leading,spacing: 10) {
                            VStack{
                                SectionHeader( title: "左图" )
                                SingleEffectImageUploader(
                                    imageUrl: $controller.images_left.orDefault(""),
                                    threadId: controller.threadId
                                )
                                
                            }
                            .padding(10)
                            .background(
                                RoundedRectangle(cornerRadius: 10)
                                    .fill(DesignSystem.Colors.backgroundCard)
                            )
                            
                            VStack{
                                SectionHeader( title: "右图" )
                                SingleEffectImageUploader(
                                    imageUrl: $controller.images_right.orDefault(""),
                                    threadId: controller.threadId
                                )
                                
                            }
                            .padding(10)
                            .background(
                                RoundedRectangle(cornerRadius: 10)
                                    .fill(DesignSystem.Colors.backgroundCard)
                            )
                        }
                        
                    }
                    Spacer()
                    
                    VStack {}
                    // 在显示 Alert 时关闭键盘
                        .alert("错误", isPresented: $controller.showError) {
                            Button("确定"){}
                        } message: {
                            Text(controller.errorMessage ?? "")
                        }
                }
                .padding(.horizontal)
            }
            .scrollIndicators(.hidden)
            
            
            VStack {
                // 在这里可以添加分割线或其他元素
                //                Divider()
                Button( action:controller.onSubmit) {
                    Text("确认")
                        .font(.headline)
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity) // 使文本容器宽度最大化
                        .padding()
                    if controller.isLoading {
                        ProgressView()
                            .tint(DesignSystem.Colors.backgroundCard)
                    }
                }
                .background(DesignSystem.Colors.primary) // 按钮背景色
                .cornerRadius(15) // 圆角
                .frame(width: UIScreen.main.bounds.width * 0.9) // 屏幕宽度的80%
                .padding(.bottom, 30) // 底部间距
            }
        }
        .onChange(of: controller.transactionID) { newID in
            // 关键：当 controller.transactionID 不为 nil 时，导航到 PollingResultView
            if newID != nil {
                navigator.push(Route.videoArtWorkCreationResult(transactionID: newID!,detail: nil))
            }
        }
        .onAppear{
            controller.setThreadId()
            
            if let effectType = type {
                controller.type = effectType
            }
            
            
            guard let workInfo = detail?.inputFormat else { return }
            // 直接赋值，如果无法解析，nil-coalescing 操作符 (??) 会保留 controller 的原值
            controller.images = (workInfo["images"] as? [String]) ?? controller.images
            controller.image = (workInfo["image"] as? String) ?? controller.image
            controller.type = workInfo.decode(key: "type") ?? controller.type
            
            if let images = (workInfo["images"] as? [String]),images.count > 1 {
                controller.images_left = images[0]
                controller.images_right = images[1]
            }

        }
        .onDisappear {
            // 当视图消失时（例如用户返回上一页），停止轮询
            if detail?.inputFormat != nil {
                controller.resetControllerState()
            }
        }
    }
}


