//
//  CameraView.swift
//  GrofyAI
//
//  Created by kissy on 2025/7/7.
//

// MARK: - CustomCameraView.swift

import SwiftUI

struct CameraView: View {
    @StateObject private var service = ImagePickerCameraService()
    @EnvironmentObject var controller: ImageRecognitionChatController
    
    @Binding var capturedImages: [UIImage]
    @Environment(\.presentationMode) private var presentationMode
    
    @State private var pickerResultImages: [UIImage] = []
    @State private var showLibraryMultiPicker = false
    
    @State private var selectedParentType:VisionTypeParentType = .recognition
    
    //跟踪内部滚动视图是否在拖动
    @State private var isSearchViewDragging = false
    
    private var flashButtonIconName: String {
        switch service.flashMode {
        case .on: return "bolt.fill"
        case .off: return "bolt.slash.fill"
        case .auto: return "bolt.badge.a.fill"
        @unknown default: return "bolt.slash.fill"
        }
    }
    
    private var visionTypes: [VisionType] {
        return VisionType.allCases.filter { $0.parentType == selectedParentType }
    }
    
    var body: some View {
        ZStack {
            Color.black.ignoresSafeArea()
            
            if service.isPhotoTaken {
                photoPreviewView()
            } else if service.isSessionRunning {
                VStack {
                    topControlsView()
                    
                    ZStack {
                        ImagePickerCameraPreview(service: service)
                        visionTypeControlsView()
                        VStack{
                            Spacer()
                            ZStack {
                                SearchView(
                                    selectedParentType: selectedParentType,
                                    selectedVisionType: $controller.selectedVisionType
                                )
                                .frame(height:85)
                                if visionTypes.count > 1 {
                                    VStack {
                                        Spacer()
                                        RoundedRectangle(cornerRadius: DesignSystem.Rounded.sm)
                                        // .stroke 创建一个描边（边框）视图
                                            .stroke(Color.white, lineWidth: 2)
                                        // 设置矩形框的尺寸
                                            .frame(width: 53, height: 53)
                                        // 确保这个视图不会接收任何手势，让下层的 TabView 可以滑动
                                            .allowsHitTesting(false)
                                    }
                                    .frame(height:85)
                                    .padding(.bottom,8)
                                    
                                }
                            }
                            .frame(height: 85)
                        }
                    }
                    
                    parentTypeControlsView()
                    
                    bottomControlsView()
                }
                .frame(maxWidth:.infinity)
            } else {
                ProgressView().progressViewStyle(CircularProgressViewStyle(tint: .white))
            }
        }
        .onChange(of: isSearchViewDragging) { _ in
            print(isSearchViewDragging,"sdasd")
        }
        .onAppear(perform: service.setup)
        .onDisappear(perform: service.stopSession)
        
        // [!!] NEW: 当从相册选择图片后，处理该图片
        .sheet(isPresented: $showLibraryMultiPicker, onDismiss: {
            // 这个闭包会在 MultiImagePicker 完全关闭后执行
            
            // 1. 检查临时变量中是否有图片
            if !pickerResultImages.isEmpty {
                // 2. 如果有，将结果赋值给真正的绑定，传回给 ImageRecognition
                self.capturedImages = pickerResultImages
                
                // 3. 然后，安全地关闭 CameraView
                presentationMode.wrappedValue.dismiss()
            }
            // 如果 pickerResultImages 为空，说明用户取消了选择，我们什么都不做。
        }) {
            // sheet 的内容：将结果绑定到我们的临时状态变量
            MultiImagePicker{ images in
                self.pickerResultImages  = images
            }
        }
    }
    
    // MARK: - topControlsView
    @ViewBuilder private func topControlsView() -> some View {
        HStack {
            Button(action: { service.toggleFlash() }) {
                Image(systemName: flashButtonIconName)
            }
            Spacer()
            Button(action: {
                presentationMode.wrappedValue.dismiss()
            }) {
                Image(systemName: "xmark.circle.fill")
                    .font(.title)
                    .foregroundColor(.white)
                    .background(Circle().fill(Color.black.opacity(0.5)))
                    .padding()
            }
        }
        .font(.title2)
        .foregroundColor(.white)
        .padding(.horizontal)
        .padding(.top, 15)
    }
    
    // MARK: - parentTypeControlsView
    @ViewBuilder private func parentTypeControlsView() -> some View {
        HStack(spacing: 20) {
            ForEach(VisionTypeParentType.allCases, id: \.self) { tab in
                CameraTabButton(title: tab.description, isSelected: selectedParentType == tab) {
                    // 这里的动画只影响顶部标签栏（下划线和滚动）
                    withAnimation(.spring()) {
                        selectedParentType = tab
                    }
                }
                .id(tab)
            }
        }
        .frame(width:UIScreen.main.bounds.width,height: 35)
        .padding(.horizontal)
    }
    
    // MARK: - visionTypeControlsView
    @ViewBuilder private func visionTypeControlsView() -> some View {
        TabView(selection: $selectedParentType) {
            ForEach(VisionTypeParentType.allCases, id: \.self) { tab in
                Color.clear
                    .tag(tab)
            }
        }
        .tabViewStyle(.page(indexDisplayMode: .never))
        // ✅ 当 selectedTab 改变时，禁用 TabView 的动画
        .animation(nil, value: selectedParentType)
    }
    
    // MARK: - bottomControlsView
    @ViewBuilder private func bottomControlsView() -> some View {
        HStack(alignment: .center, spacing: 0) {
            Button(action: {
                // 打开相册选择器
                self.pickerResultImages = []
                self.showLibraryMultiPicker = true
            }) {
                Image(systemName: "photo.on.rectangle.angled") // 使用相册图标
                    .font(.title)
                    .foregroundColor(.white)
                    .frame(width: 60, height: 60)
            }.frame(maxWidth: .infinity)
            
            
            Button(action: { service.capturePhoto() }) {
                ZStack {
                    Circle().fill(Color.white)
                    Circle().stroke(Color.black, lineWidth: 4)
                }
                .frame(width: 75, height: 75)
            }.frame(maxWidth: .infinity)
            
            Button(action: { service.switchCamera() }) {
                Image(systemName: "arrow.triangle.2.circlepath")
                    .font(.title)
                    .foregroundColor(.white)
                    .frame(width: 60, height: 60)
                    .background(Color.white.opacity(0.2))
                    .clipShape(Circle())
            }.frame(maxWidth: .infinity)
        }
        .padding(.bottom, 30)
    }
    
    // MARK: - photoPreviewView
    @ViewBuilder private func photoPreviewView() -> some View {
        VStack {
            Spacer()
            if let image = service.capturedImage { Image(uiImage: image).resizable().scaledToFit() }
            Spacer()
            HStack {
                Button("重拍") { service.isPhotoTaken = false; service.capturedImage = nil; service.startSession() }
                Spacer()
                Button("使用照片") {
                    if let img = service.capturedImage {
                        // [!!] MODIFIED: 将单张拍摄的照片放入数组中返回
                        self.capturedImages = [img]
                        presentationMode.wrappedValue.dismiss()
                    }
                }
            }.font(.headline).foregroundColor(.white).padding()
            Spacer()
        }
    }
    
}


// MARK: - CameraTabButton
struct CameraTabButton: View {
    let title: String
    let isSelected: Bool
    let action: () -> Void
    @Environment(\.colorScheme) private var colorScheme
    
    var body: some View {
        VStack(spacing: 5) {
            // 模型名称
            Button(action: action) {
                Text(title)
                    .font(.system(size: 16,weight: .medium))
                    .scaleEffect(isSelected ? 16/16 : 1.0) // 16 / 14 ≈ 1.14
                    .animation(.easeInOut(duration: 0.2), value: isSelected)
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                    .multilineTextAlignment(.center)
                    .preferredColorScheme(.dark)
            }
            
            RoundedRectangle(cornerRadius: 2)
                .frame( maxWidth:(isSelected ? 20 :0), maxHeight: 2)
                .foregroundColor(isSelected ? DesignSystem.Colors.textPrimary :  .clear)
                .preferredColorScheme(.dark)
        }
    }
}

//MARK: - VisionTypeIcon
struct VisionTypeIcon: View {
    let visionType: VisionType
    let isSelected: Bool
    let width: CGFloat
    let action: () -> Void
    
    var body: some View {
        VStack(spacing: 5) {
            Text(visionType.description)
                .font(.system(size: 14,weight: .medium))
                .foregroundColor(.gray)
            
            Image(visionType.iconName) // 从 Assets 加载图片
                .resizable()
                .scaledToFit()
                .clipShape(RoundedRectangle(cornerRadius: DesignSystem.Rounded.sm, style: .continuous))
                .frame(width: width, height: 50)
        }
        .onTapGesture(perform: action)
    }
}


//MARK: - SearchView
struct SearchView: View {
    let selectedParentType: VisionTypeParentType
    @Binding var selectedVisionType: VisionType
    
    @State private var debounceWorkItem: DispatchWorkItem?
    @State private var isProgrammaticScrolling = false
    
    private let scrollViewCoordinateSpace = "scrollView"
    private let itemWidth: CGFloat = 60 // 单个图标的宽度
    private let itemSpacing: CGFloat = 15 // 图标之间的间距
    
    
    private var visionTypes: [VisionType] {
        return VisionType.allCases.filter { $0.parentType == selectedParentType }
    }
    
    var body: some View {
        GeometryReader { scrollViewGeo in
            let scrollViewMidX = scrollViewGeo.size.width / 2
            VStack{
                Spacer()
                if visionTypes.count > 1 {
                    ScrollViewReader { scrollProxy in
                        ScrollView(.horizontal, showsIndicators: false) {
                            HStack(spacing: 15) {
                                ForEach(visionTypes, id: \.self) { visionType in
                                    VisionTypeIcon(
                                        visionType: visionType,
                                        isSelected: selectedVisionType == visionType,
                                        width: itemWidth
                                    ) {
                                        selectedVisionType = visionType
                                    }
                                    .id(visionType)
                                }
                            }
                            .padding(.horizontal, scrollViewMidX - 30)
                            .padding(.bottom,10)
                            // 修复：使用正确的GeometryReader捕获偏移量
                            .background(
                                GeometryReader { contentGeo in
                                    Color.clear
                                        .onChange(of: contentGeo.frame(in: .named(scrollViewCoordinateSpace)).minX) { newValue in
                                            if isProgrammaticScrolling { return }
                                            // 取消上一个防抖任务
                                            debounceWorkItem?.cancel()
                                            
                                            // 创建新的防抖任务，在用户停止滚动 200ms 后执行
                                            let task = DispatchWorkItem {
                                                // 单元格宽度 = 图标宽度 + 间距
                                                let cellWidth = itemWidth + itemSpacing
                                                // 滚动的距离 = 偏移量的相反数。向左滑，offset为负，距离为正。
                                                let scrolledDistance = -newValue
                                                
                                                // 计算最接近中心的项的索引 (通过四舍五入)
                                                // 例如：滚动了 1.6 个单元格的距离，四舍五入后就是索引 2
                                                let closestIndex = Int(round(scrolledDistance / cellWidth))
                                                guard visionTypes.indices.contains(closestIndex) else { return }
                                                let newSelection = visionTypes[closestIndex]
                                                
                                                print("计算出的最近索引: \(closestIndex), 对应项: \(newSelection)")
                                                
                                                // 更新选中项。onChange(of: selectedVisionType) 会处理滚动动画。
                                                // 即使是同一项，也赋值，以防用户拖动后没有切换但位置不准，
                                                // onChange会负责将其校准回中心。
                                                if selectedVisionType != newSelection {
                                                    // 1. 如果是新的选项：更新状态，让 .onChange(of: selectedVisionType) 自动处理滚动
                                                    selectedVisionType = newSelection
                                                } else {
                                                    // 2. 如果选项没变（但位置可能偏了）：手动触发滚动来校准位置
                                                    //    因为 .onChange 不会触发，所以我们必须在这里直接调用 scrollProxy。
                                                    isProgrammaticScrolling = true
                                                    withAnimation(.spring()) {
                                                        scrollProxy.scrollTo(newSelection, anchor: .center)
                                                    }
                                                    // 同样，在动画结束后重置标记
                                                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.05) {
                                                        isProgrammaticScrolling = false
                                                    }
                                                }
                                            }
                                            debounceWorkItem = task
                                            DispatchQueue.main.asyncAfter(deadline: .now() + 0.05, execute: task)
                                        }
                                }
                            )
                        }
                        .coordinateSpace(name: scrollViewCoordinateSpace)
                        .onChange(of: selectedVisionType) { newValue in
                            // 1. 标记：即将开始自动滚动
                            isProgrammaticScrolling = true
                            
                            // 2. 执行：带动画滚动到中心
                            withAnimation(.spring()) {
                                scrollProxy.scrollTo(newValue, anchor: .center)
                            }
                            
                            // 3. 重置：在动画大致结束后，重置标记，以便响应下一次用户拖拽
                            DispatchQueue.main.asyncAfter(deadline: .now() + 0.05) {
                                isProgrammaticScrolling = false
                            }
                        }
                        .onAppear {
//                            if let firstItem = visionTypes.first(where: { $0 == selectedVisionType }) ?? visionTypes.first {
//                                selectedVisionType = firstItem
//                                scrollProxy.scrollTo(firstItem, anchor: .center)
//                            }
                            // 尝试找到当前已选中的类型
                            let currentSelection = visionTypes.first(where: { $0 == selectedVisionType })

                            // 2. 如果没找到，尝试获取默认的第二个。如果第二个也不存在，就退一步获取第一个。
                            let defaultSelection: VisionType? // 假设你的 visionTypes 元素类型是 VisionType
                            if visionTypes.count > 1 {
                                // 如果数组至少有两个元素，默认选第二个（索引为1）
                                defaultSelection = visionTypes[1]
                            } else {
                                // 否则，退一步，默认选第一个
                                defaultSelection = visionTypes.first
                            }

                            // 3. 组合逻辑：优先使用当前选中的，否则使用我们计算出的默认值
                            if let itemToSelect = currentSelection ?? defaultSelection {
                                // 只有当需要更新时才赋值，避免不必要的UI刷新
                                if selectedVisionType != itemToSelect {
                                    selectedVisionType = itemToSelect
                                }
                                // 滚动到目标位置
                                scrollProxy.scrollTo(itemToSelect, anchor: .center)
                            }
                        }
                    }
                } else {
                    HStack{
                        Image(systemName: "star.fill")
                            .font(.system(size: 14))
                        Text("拍照快速识别")
                        Spacer()
                    }
                    .frame(width: UIScreen.main.bounds.width,height: 30)
                    .padding(.horizontal)
                    .background(.black.opacity(0.8))
                }
            }
        }
        .frame(width: UIScreen.main.bounds.width)
        .onChange(of: selectedParentType) { parentType in
            print(parentType)
            // 1. 先获取所有符合条件的子类型
            let filteredTypes = VisionType.allCases.filter { $0.parentType == parentType }

            // 2. 根据子类型的数量来决定默认值
            let defaultType: VisionType
            if filteredTypes.count > 1 {
                // 如果至少有两个符合条件的类型，默认选第二个
                defaultType = filteredTypes[1]
            } else if let firstType = filteredTypes.first {
                // 如果只有一个，就选那一个
                defaultType = firstType
            } else {
                // 如果一个都没有，使用最终的备用值
                defaultType = .vision
            }

            // 3. 赋值
            selectedVisionType = defaultType
        }
    }
}
