//
//  ImageRecognition.swift
//  GrofyAI
//
//  Created by kissy on 2025/7/7.
//
import SwiftUI

//MARK: 图像识别--拍照
struct ImageRecognition: View {
    let fileService = FileService()
    @Environment(\.colorScheme) private var colorScheme
    @EnvironmentObject var controller: ImageRecognitionChatController
    private let authStore = AuthStore.shared
    @EnvironmentObject private var globalAuthManager: GlobalAuthManager
    // 绑定到外部的最终结果（例如图片URL）
    @Binding var pickedImages: [UIImage]
    // 最大图片数量限制
    let maxCount: Int?
    
    // 内部状态
    
    // [!!] MODIFIED: 状态变量现在是 [UIImage] 数组，用于接收 CameraView 的结果
    @State private var showCustomCamera = false
    @State private var capturedImages: [UIImage] = []
    
    
    
    init(
        pickedImages: Binding<[UIImage]>,
        maxCount: Int? = 9
    ) {
        self._pickedImages = pickedImages
        self.maxCount = maxCount
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            
            InputButton(iconName: "IconInputCamera", action: {
                
                if authStore.getAccessToken() == nil {
                    //显示登录弹窗（页面）
                    globalAuthManager.requestAuthentication()
                }else {
                    self.capturedImages = []
                    self.showCustomCamera = true
                    
                    controller.clearAllState()
                    ToastManager.shared.clearToast()
                }
                     
            })
        }
        .onChange(of: capturedImages) { newImages in
            // 如果相机返回了图片
            guard !newImages.isEmpty else { return }
            
            // 3. 将结果通过绑定传递给父视图
            // 只取 maxCount 限制的数量
            self.pickedImages = Array(newImages.prefix(maxCount ?? 9))
        }
        // 4. 以全屏方式呈现相机
        .fullScreenCover(isPresented: $showCustomCamera) {
            // 将临时的 capturedImages 数组绑定到 CameraView
            CameraView(capturedImages: $capturedImages)
        }
        
    }
}
