//
//  ImageRecognitionIconView.swift
//  GrofyAI
//
//  Created by kissy on 2025/7/8.
//
import SwiftUI
import FlowStacks


struct ImageRecognitionIconView: View {
    
    @EnvironmentObject var controller: ImageRecognitionChatController
    @EnvironmentObject var navigator: FlowPathNavigator
    
    @State private var pickedImages: [UIImage] = []
    var body: some View {
        ImageRecognition(
            pickedImages: $pickedImages
        )
        .onChange(of: pickedImages){ images in
            if !images.isEmpty {
                controller.newPickedImages = images
                navigator.push(Route.imageRecognitionChat(threadId: nil))
                pickedImages = []
            }
        }
    }
}

