import FlowStacks
import SwiftUI

// MARK: - 占位符聊天页面

struct PlaceholderChatView: View {
    let title: String
    let description: String
    let iconName: String
    let iconColor: Color

    @EnvironmentObject var navigator: FlowPathNavigator

    var body: some View {
        VStack(spacing: 24) {
            Image(systemName: iconName)
                .font(.system(size: 60))
                .foregroundColor(iconColor)

            VStack(spacing: 8) {
                Text(title)
                    .font(.title2)
                    .fontWeight(.semibold)

                Text(description)
                    .font(.body)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }

            Text("该功能正在开发中，敬请期待...")
                .font(.caption)
                .foregroundColor(.secondary)
                .padding(.top, 20)
        }
        .padding()
        .navigationTitle(title)
        .navigationBarTitleDisplayMode(.inline)
    }
}
