//
//  ImageArtworkCreationResult.swift
//  GrofyAI
//
//  Created by kissy on 2025/7/16.
//

import SwiftUI
import FlowStacks
import Kingfisher
import AVKit
import Photos
import SDWebImageSwiftUI
import SDWebImageWebPCoder

struct ImageArtworkCreationResult: View {
    var detail: ArtWorksCell?
    // 使用 @StateObject，因为这个视图拥有并管理 PollingViewModel 的生命周期
    @StateObject private var viewModel: UserArtWorkViewModel
    @EnvironmentObject var navigator: FlowPathNavigator
    @Environment(\.colorScheme) private var colorScheme
    
    // 用于控制弹窗的状态变量
    @State private var showResultAlert = false
    @State private var alertMessage = ""
    @State private var player: AVPlayer?

    
    init(transactionID: Int?,detail:ArtWorksCell?) {
        // _viewModel 的下划线语法用于在 init 中初始化 @StateObject
        _viewModel = StateObject(wrappedValue: UserArtWorkViewModel(transactionID: transactionID ?? nil))
        self.detail = detail
    }
    
    private func safeAreaTopInset() -> CGFloat {
        let keyWindow = UIApplication.shared.connectedScenes
            .filter({ $0.activationState == .foregroundActive })
            .map({ $0 as? UIWindowScene })
            .compactMap({ $0 })
            .first?.windows
            .filter({ $0.isKeyWindow }).first
        let safeAreaInsets = keyWindow?.safeAreaInsets.top  ?? 0
        
        if safeAreaInsets == 0 {
            return safeAreaInsets
        }
        return colorScheme == .light ? safeAreaInsets : (safeAreaInsets - 12)
    }
    
    var body: some View {
        
        GeometryReader { geometry in
            ZStack{
                if colorScheme == .light {
                    Color.clear
                        .background(
                            Image("ProfileBackground")
                                .resizable() // 关键：使图片可缩放
                                .scaledToFill() // 填充整个区域
                                .edgesIgnoringSafeArea(.all)
                        )
                        .frame(maxWidth:.infinity,maxHeight: .infinity)
                }
                
                VStack{
                    //安全区域高度
                    Color.clear.frame(height:  safeAreaTopInset() )
                    
                    VStack{
                        ImageResultNavigationBarView(viewModel: viewModel, alertMessage: $alertMessage, showResultAlert: $showResultAlert)
                        
                        imageResult
                    }
                }
                .padding(.horizontal,15)
                .edgesIgnoringSafeArea(.top)
                .alert(alertMessage, isPresented: $showResultAlert) {
                    Button("好的", role: .cancel) { }
                }
                .onAppear {
                    // 当视图出现时，开始轮询
                    viewModel.startPolling()
                    //            workInfo.decode(key: "size") ?? controller.size
                    if let work = detail {
                        print("传入的值：",work)
                        viewModel.pollingResult = UserArtWorkRes(
                            id: work.id,
                            links: work.links,
                            status: work.status,
                            provider: work.provider,
                            model: work.model,
                            agentType: work.agentType,
                            statusDescribe: work.statusDescribe,
                            createDate: work.createDate,
                            creationDay: work.creationDay
                            
                        )
                    }
                }
                .onDisappear {
                    // 当视图消失时（例如用户返回上一页），停止轮询
                    viewModel.stopPolling()
                    viewModel.pollingResult = nil
                }
            }
            .navigationBarHidden(true)
        }
        .background(DesignSystem.Colors.backgroundPage)
    }
        
    @ViewBuilder
    private var imageResult: some View {
        
        VStack(spacing: 20) {
            Spacer()
            if let result = viewModel.pollingResult {
                switch result.status {
                case .running:
                    loading
                    
                case .success:
                    if result.agentType == .image,
                       let links = result.links, // 确保 links 数组存在
                       !links.isEmpty // 确保数组不为空
                    {
                        // MARK: - 优化后的逻辑
                        if links.count > 1 {
                            // --- 情况1: 链接数量大于1，使用我们新的多图浏览器 ---
                            MultiImageViewer(imageUrls: links)

                        } else if let urlString = links.first, let url = URL(string: urlString) {
                            // --- 情况2: 只有一个链接，使用原来的单图显示 ---
                            KFImage(url)
                                .placeholder { ProgressView() }
                                .fade(duration: 0.25)
                                .onFailure { error in
                                    print("KFImage failed to load image from url: \(url), error: \(error)")
                                }
                                .resizable()
                                .aspectRatio(contentMode: .fit)
                                .frame(maxWidth:.infinity)
                                .cornerRadius(12)
                                
                        } else {
                            // --- 情况3: 数组存在但第一个元素不是有效链接 (不太可能但做个保护) ---
                            fallbackView // 使用下面的占位视图
                        }
                        
                    }
                    // MARK: - 其他或无效情况处理分支
                    else {
                        fallbackView // 使用下面的占位视图
                    }
                    

                case .failed:
                    Image(systemName: "xmark.octagon.fill")
                        .font(.largeTitle)
                        .foregroundColor(.red)
                    Text("生成失败")
                        .font(.title)
                    //                    Text(result.failureReason ?? "未知错误")
                    //                        .foregroundColor(.secondary)
                default:
                    Text(result.status?.rawValue ?? "")
                }
                
                
            } else if viewModel.isLoading {
                // 初始加载状态
                ProgressView("正在获取任务状态...")
            }
            
            if let errorMessage = viewModel.errorMessage {
                Text("错误: \(errorMessage)")
                    .foregroundColor(.red)
            }
            Spacer()
            
            if viewModel.pollingResult?.status != .running {
                bottomSection
            }
            
        }
        
    }
    
    private var bottomSection: some View {
        VStack {
            //MARK: 点击跳转
            Button( action: {
                guard let agentType = detail?.agentType else { return }
                //跳转进入对应的创作页面
                switch agentType {
                case .image :
                    return navigator.push(Route.createImage(detail: detail))
                case .video:
                    return  navigator.push(Route.createVideo(detail: detail,createType: nil))
                case .effect:
                    return  navigator.push(Route.createEffectVideo( type: nil ,detail: detail))
                }
            }) {
                Text("再次生成")
                    .font(.headline)
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity) // 使文本容器宽度最大化
                    .padding()
            }
            .background(DesignSystem.Colors.primary) // 按钮背景色
            .cornerRadius(15) // 圆角
            .frame(width: UIScreen.main.bounds.width * 0.9) // 屏幕宽度的80%
            .padding(.bottom, 30) // 底部间距
        }
    }
    
    private var loading: some View {
        VStack {
            if let url = Bundle.main.url(forResource: "effect_fuzzy", withExtension: "webp") {
                AnimatedImage(url: url)
                    .indicator(SDWebImageActivityIndicator.medium)
                    .resizable()
                    .aspectRatio(1, contentMode: .fit)
            } else {
                ProgressView("正在生成...")
            }
            
            
            VStack(alignment:.center){
                HStack(spacing:0){
                    Text("退出页面后，AI将继续生成。您可随时点击右上角")
                    Image(systemName: "clock")
                        .foregroundStyle(DesignSystem.Colors.textPrimary)
                        .font(.system(size: DesignSystem.FontSize.md))
                }
                HStack{
                    Text("图标查看生成进度。欢迎前往主页继续探索")
                }
            }
            .font(.system(size: DesignSystem.FontSize.md))
            .foregroundColor(DesignSystem.Colors.textPrimary)
        }
    }
    
    private var fallbackView: some View {
        VStack(spacing: 12) {
            Image(systemName: "questionmark.diamond.fill")
                .font(.system(size: 60))
                .foregroundColor(.secondary)
            Text("未返回有效的媒体链接")
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity, idealHeight: 300) // 适应宽度
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
}

//MARK: - ImageResultNavigationBarView
struct ImageResultNavigationBarView: View {
    @EnvironmentObject var navigator: FlowPathNavigator
    
    @ObservedObject var viewModel: UserArtWorkViewModel
    @Binding var alertMessage: String
    @Binding var showResultAlert: Bool
    
    // --- 核心逻辑：保存图片到相册 ---
    private func saveImageToPhotoLibrary(from imageUrl: URL?) {
        guard let url = imageUrl else {
            self.alertMessage = "图片地址无效。"
            self.showResultAlert = true
            return
        }
        
        // 1. 从 Kingfisher 缓存中获取图片，而不是重新下载
        KingfisherManager.shared.retrieveImage(with: url) { result in
            switch result {
            case .success(let imageResult):
                // 2. 获取到了 UIImage 对象
                let image = imageResult.image
                
                // 3. 使用 Photos 框架保存图片
                PHPhotoLibrary.shared().performChanges({
                    // 创建一个 asset change request
                    PHAssetChangeRequest.creationRequestForAsset(from: image)
                }) { success, error in
                    // 回到主线程更新 UI
                    DispatchQueue.main.async {
                        if success {
                            self.alertMessage = "图片已成功保存到相册！"
                        } else if let error = error {
                            self.alertMessage = "保存失败: \(error.localizedDescription)"
                        } else {
                            self.alertMessage = "保存失败，请检查相册权限。"
                        }
                        self.showResultAlert = true
                    }
                }
                
            case .failure(let error):
                // 如果缓存中没有，Kingfisher 会尝试下载，如果也失败了，会进入这里
                DispatchQueue.main.async {
                    self.alertMessage = "无法获取图片: \(error.localizedDescription)"
                    self.showResultAlert = true
                }
            }
        }
    }
    
    var body: some View {
        VStack(spacing: 0) {
            // 自定义导航栏主体
            HStack {
                Image(systemName: "chevron.backward")
                    .foregroundStyle(DesignSystem.Colors.textPrimary)
                    .font(.title3)
                    .onTapGesture {
                        navigator.pop()
                    }
                
                Spacer()
                
                Image(systemName: "clock")
                    .foregroundStyle(DesignSystem.Colors.textPrimary)
                    .font(.title3)
                    .onTapGesture {
                        navigator.push(Route.artWorkHistory(defaultMode: .image))
                    }
                
                //MARK: -图片下载
                if viewModel.pollingResult?.status == .success,
                   let result = viewModel.pollingResult
                {
                    Button(action: {
                        // 点击按钮时，调用保存图片的函数
                        saveImageToPhotoLibrary(from:URL(string:result.links?.first ?? ""))
                    }) {
                        Image(systemName: "arrow.down.to.line.compact")
                            .font(.title3)
                            .foregroundStyle(DesignSystem.Colors.textPrimary)
                    }
                }
            }
            .frame(height: 30)
        }
        .frame(maxWidth: .infinity)
        .padding(.top,10)
        .padding(.bottom,5)
    }
}

//MARK: - MultiImageViewer
struct MultiImageViewer: View {
    let imageUrls: [String]
    
    // 使用 @State 来追踪当前显示的图片索引
    @State private var currentIndex = 0
    
    var body: some View {
        // ZStack 用于将 TabView 和控制按钮叠在一起
        ZStack {
            TabView(selection: $currentIndex) {
                // ForEach 遍历所有图片链接
                ForEach(imageUrls.indices, id: \.self) { index in
                    // 尝试将字符串转换为 URL
                    if let url = URL(string: imageUrls[index]) {
                        // 使用你原来的 KFImage 配置
                        KFImage(url)
                            .placeholder { ProgressView() }
                            .fade(duration: 0.25)
                            .onFailure { error in
                                print("KFImage failed to load image from url: \(url), error: \(error)")
                            }
                            .resizable()
                            .aspectRatio(contentMode: .fit)
                            .tag(index) // ★ 关键：将每个页面与它的索引绑定
                    }
                }
            }
            // 使用 .page 样式来实现全屏滑动效果
            .tabViewStyle(.page(indexDisplayMode: .never)) // 隐藏系统默认的小圆点
            
            HStack {
                // 左箭头按钮
                if currentIndex > 0 { // 只在不是第一张时显示
                    Button(action: {
                        // 使用动画平滑地切换
                        withAnimation {
                            currentIndex -= 1
                        }
                    }) {
                        Image(systemName: "chevron.left.circle.fill")
                            .font(.largeTitle)
                            .foregroundColor(.white.opacity(0.8))
                            .shadow(radius: 5) // 添加阴影使其在任何背景下都清晰
                    }
                }
                
                Spacer() // 将两个按钮推向两边
                
                // 右箭头按钮
                if currentIndex < imageUrls.count - 1 { // 只在不是最后一张时显示
                    Button(action: {
                        withAnimation {
                            currentIndex += 1
                        }
                    }) {
                        Image(systemName: "chevron.right.circle.fill")
                            .font(.largeTitle)
                            .foregroundColor(.white.opacity(0.8))
                            .shadow(radius: 5)
                    }
                }
            }
            .padding(.horizontal) // 给按钮一些边距
            
            VStack {
                Spacer() // 推到底部
                HStack(spacing: 8) {
                    ForEach(imageUrls.indices, id: \.self) { index in
                        Circle()
                            .fill(index == currentIndex ? Color.white : Color.gray.opacity(0.6))
                            .frame(width: 8, height: 8)
                            .animation(.spring(), value: currentIndex) // 给指示器切换加动画
                    }
                }
                .padding(.bottom, 12)
            }
        }
        .frame(maxWidth: .infinity)
        .background(Color.black.opacity(0.2)) // 给一个暗色背景，提升图片显示效果
        .cornerRadius(12)
    }
}
