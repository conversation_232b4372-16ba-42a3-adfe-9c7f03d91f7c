//
//  VideoHistory.swift
//  GrofyAI
//
//  Created by kissy on 2025/6/27.
//
import SwiftUI

struct VideoHistoryView: View {
    // 使用 @StateObject 来创建和管理 Controller 的生命周期
    @StateObject private var controller = UserArtWorksPageController()

    @Binding var isEditing: Bool
    @Binding var selectedItems: Set<ArtWorksCell.ID>
    var isMyWork: Bool
    var needsBottomSafeArea: Bool

    init(
        isMyWork: Bool = false,
        needsBottomSafeArea: Bool = false,
        isEditing: Binding<Bool>,         // 1. 参数类型必须是 Binding<Bool>
        selectedItems: Binding<Set<ArtWorksCell.ID>> // 2. 参数类型必须是 Binding<Set<...>>
    ) {
        self.isMyWork = isMyWork
        self.needsBottomSafeArea = needsBottomSafeArea
        // 3. 初始化 "下划线" 属性
        self._isEditing = isEditing
        self._selectedItems = selectedItems
    }
    
    private func calculateColumnCount(in containerWidth: CGFloat, itemWidth: CGFloat, spacing: CGFloat) -> Int {
            let totalAvailableWidth = containerWidth
            let itemWithSpacingWidth = itemWidth + spacing
            let count = (totalAvailableWidth + spacing) / itemWithSpacingWidth
            return max(1, Int(floor(count)))
        }
    
    
    
    var body: some View {
        ZStack {
            // 主要内容区域
            mainContentView
            
            // 全屏加载动画 (仅在初次加载时显示)
            if controller.isLoading {
                ProgressView("正在加载...")
                    .progressViewStyle(CircularProgressViewStyle())
                    .padding()
                    .cornerRadius(10)
                    .shadow(radius: 10)
            }
        }
        
        // 当视图出现时，加载数据
        .onAppear {
            controller.generateImage()
            controller.onViewPolling = true
            controller.agentType = .video
        }
        // 当视图消失时，停止轮询，避免后台资源浪费
        .onDisappear {
            controller.stopPolling()
            controller.onViewPolling = false
        }
    }
    
    // 将主内容抽取为一个独立的计算属性，使 body 更清晰
    @ViewBuilder
    private var mainContentView: some View {
        VStack{
            if controller.errorMessage != nil  {
                // 显示错误信息
                VStack {
                    Spacer()
                    Text("加载失败")
                        .font(.headline)
                    Button("重试") {
                        controller.generateImage()
                    }
                    .buttonStyle(.borderedProminent)
                    Spacer()
                }
            }else if controller.artWorkSection.isEmpty && !controller.isLoading {
                // 显示空状态
                VStack(spacing:0){
                    Image("IconIsEmpty")
                        .resizable()
                        .scaledToFit()
                        .frame(width: 100,height: 100)
                    Text("暂无作品哦，快去创作吧！")
                        .font(.system(size: DesignSystem.FontSize.sm))
                        .foregroundColor(.secondary)
                }
            } else {
                // 显示滚动列表
                ScrollView {
                    LazyVStack(spacing: 5, pinnedViews: [.sectionHeaders]) {
                        ForEach(controller.artWorkSection) { section in
                            
                            let myWorks = section.artworks.filter{ $0.status == .success }
                            
                            let columnCount = calculateColumnCount(
                                in: UIScreen.main.bounds.width,
                                itemWidth: 110,
                                spacing: 8
                            )
                            
                            let columns: [GridItem] = Array(
                                repeating: .init(.fixed(110), spacing: 8),
                                count: columnCount
                            )
                            
                            Section {
                                // 图片网格
                                LazyVGrid(columns: columns, spacing: 8) {
                                    ForEach(isMyWork ? myWorks : section.artworks) { artwork in
                                        ArtWorkGridCellVideoView(
                                            artwork: artwork,
                                            isEditing: isEditing,
                                            isSelected: selectedItems.contains(artwork.id)
                                        ) {
                                            // 处理选择/取消选择的逻辑
                                            if selectedItems.contains(artwork.id) {
                                                selectedItems.remove(artwork.id)
                                            } else {
                                                selectedItems.insert(artwork.id)
                                            }
                                        }
                                        
                                    }
                                }
                            } header: {
                                // 分区头部 (日期)
                                if isMyWork {
                                    if myWorks.count > 0 {
                                        DateHeaderView(section: section)
                                    }
                                } else {
                                    DateHeaderView(section: section)
                                }
                            }
                        }
                    }
                }
                .padding(.horizontal)
                .scrollIndicators(.hidden)
                // 添加下拉刷新功能
                .refreshable {
                    // refreshable 需要一个 async 方法
                    await controller.refreshData()
                }
            }
            
            if isEditing && !selectedItems.isEmpty {
                VStack {
                    // 在这里可以添加分割线或其他元素
                    //                Divider()
                    Button( action:deleteSelectedItems) {
                        Text("删除 (\(selectedItems.count))")
                            .font(.headline)
                            .foregroundColor(.white)
                            .frame(maxWidth: .infinity) // 使文本容器宽度最大化
                            .padding()
                    }
                    .background(DesignSystem.Colors.error) // 按钮背景色
                    .cornerRadius(15) // 圆角
                    .frame(width: UIScreen.main.bounds.width * 0.9) // 屏幕宽度的80%
                    .padding(.bottom, 30) // 底部间距
                }
            }

            // 根据需要添加底部安全区域空间
            if needsBottomSafeArea && !isEditing {
                Spacer().frame(height: 50)
            }
        }

    }
    
    private func deleteSelectedItems() {
        let idsToDelete = selectedItems
        
        isEditing = false
        selectedItems.removeAll()
        
        Task {
            // 这里使用的是 idsToDelete，它是一个包含了原始数据的副本，
            // 不会受到后面 selectedItems.removeAll() 的影响。
            guard !idsToDelete.isEmpty else { return } // 加个保护
            await controller.deleteItems(ids: idsToDelete)
        }
    }
}
