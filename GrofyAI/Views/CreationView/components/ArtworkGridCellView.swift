//
//  ArtworksCellView.swift
//  GrofyAI
//
//  Created by kissy on 2025/6/21.
//

import SwiftUI
import FlowStacks
import Kingfisher
import AVKit // 引入 AVKit 框架来使用 VideoPlayer


// MARK: - Image GridCell
struct ArtWorkGridCellView: View {
    @EnvironmentObject var navigator: FlowPathNavigator
    @Environment(\.colorScheme) private var colorScheme
    let artwork: ArtWorksCell
    
    @State private var hasLoadFailed = false
    
    let isEditing: Bool
    let isSelected: Bool
    let onSelect: () -> Void  // 当在编辑模式下被点击时调用的闭包
    
    
    
    var body: some View {
        // 使用 ZStack 来叠加加载指示器
        ZStack {
            // 主要内容，根据编辑模式和状态调整透明度
            mainCellContent
                .opacity(isEditing && !isSelectable ? 0.5 : 1.0) // 正在运行的项在编辑时变暗
            
            // --- 新增：编辑模式下的蒙层和选择指示器 ---
            if isEditing {
                if isSelectable {
                    // 可选择项的UI
                    Color.black.opacity(isSelected ? 0.3 : 0.0) // 选中时加深蒙层
                    
                    VStack {
                        HStack {
                            Spacer()
                            Image(systemName: isSelected ? "checkmark.circle.fill" : "circle")
                                .font(.title2)
                                .foregroundColor(.white)
                                .background(
                                    // 给白色图标加一点阴影，使其在亮色背景上更清晰
                                    Circle().fill(Color.black.opacity(0.3)).scaleEffect(1.2)
                                )
                                .padding(6)
                        }
                        Spacer()
                    }
                } else {
                    // 不可选（running状态）的UI，只加一层蒙层，不显示选择按钮
                    Color.black.opacity(0.4)
                }
            }
        }
//        .aspectRatio(1, contentMode: .fit)
        .frame(width: 110,height: 110)
        .background( colorScheme == .light ? Color.black.opacity(0.1) : DesignSystem.Colors.backgroundCard )
        .clipShape(RoundedRectangle(cornerRadius: 8)) // 添加圆角
        .onTapGesture {
            if isEditing {
                // 在编辑模式下，只有可选项才能触发选择操作
                if isSelectable {
                    onSelect()
                }
            } else {
                // 不在编辑模式下，执行原有的导航操作
                navigator.push(Route.imageArtWorkCreationResult(transactionID: nil, detail: artwork))
            }
        }
    }
    
    @ViewBuilder
    private var mainCellContent: some View {
        ZStack {
            
            if artwork.status != .failed {
                if hasLoadFailed {
                    Image(systemName: "photo.fill")
                        .font(.largeTitle)
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                        .frame(maxWidth: .infinity, maxHeight: .infinity)
                        .background(Color.gray.opacity(0.1))
                } else {
                    
                    KFImage(getImageUrlFromArtwork(artwork))
                        .placeholder {
                            ProgressView()
                                .frame(maxWidth: .infinity, maxHeight: .infinity)
                                .background(Color.gray.opacity(0.1))
                        }
                        .onFailure { error in
                            self.hasLoadFailed = true
                            print("Kingfisher failed to load image: \(error.localizedDescription)")
                        }
                        .fade(duration: 0.25)
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                    
                }
            } else { // artwork.status == .failed
                Image(systemName: "exclamationmark.triangle")
                    .font(.largeTitle)
                    .foregroundColor(DesignSystem.Colors.error)
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                    .background(DesignSystem.Colors.backgroundCard)
            }
            
            if artwork.status == .running {
                Color.black.opacity(0.4)
                ProgressView()
                    .progressViewStyle(CircularProgressViewStyle(tint: .white))
            }
        }
        
    }
    
    
    // 计算属性，判断当前项在编辑模式下是否可选
    private var isSelectable: Bool {
        artwork.status != .running
    }
    
    // 这是一个【假设】的函数，你需要根据你的 API 来实现它！
    private func getImageUrlFromArtwork(_ artwork: ArtWorksCell) -> URL? {
        // 方案A: 如果 ArtWorksCell 有一个 imageUrl 属性
        // return URL(string: artwork.imageUrl)
        
        return URL(string: artwork.links?.first ?? "")
    }
}


//MARK: - Vide GridCell

struct ArtWorkGridCellVideoView: View {
    @EnvironmentObject var navigator: FlowPathNavigator
    let artwork: ArtWorksCell
    
    // 使用 @State 来持有和管理 AVPlayer 的生命周期
    // 这样可以避免在 View 重绘时重复创建播放器
    @State private var player: AVPlayer?
    // 用于处理 URL 无效或播放器初始化失败的情况
    @State private var hasLoadFailed = false
    
    let isEditing: Bool
    let isSelected: Bool
    let onSelect: () -> Void  // 当在编辑模式下被点击时调用的闭包

    var body: some View {
        ZStack {
            // 主要内容，根据编辑模式和状态调整透明度
            mainCellContent
                .opacity(isEditing && !isSelectable ? 0.5 : 1.0) // 正在运行的项在编辑时变暗
            
            // --- 新增：编辑模式下的蒙层和选择指示器 ---
            if isEditing {
                if isSelectable {
                    // 可选择项的UI
                    Color.black.opacity(isSelected ? 0.3 : 0.0) // 选中时加深蒙层
                    
                    VStack {
                        HStack {
                            Spacer()
                            Image(systemName: isSelected ? "checkmark.circle.fill" : "circle")
                                .font(.title2)
                                .foregroundColor(.white)
                                .background(
                                    // 给白色图标加一点阴影，使其在亮色背景上更清晰
                                    Circle().fill(Color.black.opacity(0.3)).scaleEffect(1.2)
                                )
                                .padding(6)
                        }
                        Spacer()
                    }
                } else {
                    // 不可选（running状态）的UI，只加一层蒙层，不显示选择按钮
                    Color.black.opacity(0.4)
                }
            }
        }
        .aspectRatio(1, contentMode: .fill) // 对于视频，.fill 通常比 .fit 效果更好
        .frame(width: 110, height: 110)
        .background(DesignSystem.Colors.backgroundCard)
        .clipShape(RoundedRectangle(cornerRadius: 8))
        .onTapGesture {
            if isEditing {
                // 在编辑模式下，只有可选项才能触发选择操作
                if isSelectable {
                    onSelect()
                }
            } else {
                // 不在编辑模式下，执行原有的导航操作
                navigator.push(Route.videoArtWorkCreationResult(transactionID: nil, detail: artwork))
            }
        }
        .onAppear(perform: initializePlayer) // 当视图出现时，初始化播放器
        .onDisappear {
            // 当视图消失时，暂停播放并清空播放器，以释放资源
            player?.pause()
            player = nil
        }
    }
    
    
    @ViewBuilder
    private var mainCellContent: some View {
        ZStack {
            // MARK: - 视频播放器核心区域
            if artwork.status != .failed {
                // 如果播放器成功初始化，则显示 VideoPlayer
                if let player = player {
                    VideoPlayer(player: player)
                        // 在视频上覆盖一层透明视图，以禁用默认的播放/暂停控件
                        .overlay(Color.clear)
                        .disabled(true) // 进一步确保交互被禁用
                        .transition(.opacity.animation(.easeInOut))
                } else {
                    // 如果 player 尚未创建或加载失败，显示占位符
                    Group {
                        if hasLoadFailed {
                            // URL 无效或初始化失败
                            Image(systemName: "video.slash.fill")
                        } else {
                            // 正在初始化播放器
                            ProgressView()
                        }
                    }
                    .font(.largeTitle)
                    .foregroundColor(DesignSystem.Colors.textSecondary)
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                }
            } else {
                // MARK: - 任务本身失败的状态
                Image(systemName: "exclamationmark.triangle")
                    .font(.largeTitle)
                    .foregroundColor(DesignSystem.Colors.error)
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
            }
            
            // MARK: - 任务运行中的遮罩
            if artwork.status == .running {
                Color.black.opacity(0.4)
                ProgressView()
                    .progressViewStyle(CircularProgressViewStyle(tint: .white))
            }
        }
    }
    // MARK: - 辅助函数
    
    // 计算属性，判断当前项在编辑模式下是否可选
    private var isSelectable: Bool {
        artwork.status != .running
    }
    
    private func initializePlayer() {
        // 1. 确保 URL 有效
        guard let videoURL = getVideoUrlFromArtwork(artwork) else {
            self.hasLoadFailed = true
            print("Error: Invalid video URL.")
            return
        }
        
        // 2. 创建 AVPlayer 实例并赋值给 @State 变量
        let playerItem = AVPlayerItem(url: videoURL)
        let avPlayer = AVPlayer(playerItem: playerItem)
        
        // 3. 设置为静音和自动播放
        avPlayer.isMuted = true
        avPlayer.play()
        
        self.player = avPlayer
        
        // 4. (关键) 监听视频播放结束事件，以实现循环播放
        // 使用 NotificationCenter 观察 AVPlayerItem.didPlayToEndTimeNotification
        NotificationCenter.default.addObserver(
            forName: .AVPlayerItemDidPlayToEndTime,
            object: playerItem, // 只观察这个特定的 playerItem
            queue: .main
        ) { _ in
            // 当视频播放结束时，将播放头重置到开头并再次播放
            avPlayer.seek(to: .zero)
            avPlayer.play()
        }
    }
    
    // 根据你的数据模型获取视频 URL
    private func getVideoUrlFromArtwork(_ artwork: ArtWorksCell) -> URL? {
        // 假设视频 URL 在 links 数组中，或者 artwork 有一个 videoUrl 属性
        // 请根据你的实际数据结构修改这里
        guard let urlString = artwork.links?.first, !urlString.isEmpty else {
            return nil
        }
        return URL(string: urlString)
    }
}
