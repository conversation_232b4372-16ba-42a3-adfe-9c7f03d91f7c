//
//  VideoArtworkCreationResult.swift
//  GrofyAI
//
//  Created by kissy on 2025/7/16.
//


import SwiftUI
import AVKit
import FlowStacks
import Combine
import Kingfisher
import SDWeb<PERSON>mageSwiftUI
import SDWebImageWebPCoder

struct VideoArtworkCreationResult: View {
    
    @EnvironmentObject var navigator: FlowPathNavigator
    @Environment(\.dismiss) private var dismiss // 使用 SwiftUI 内置的 dismiss
    @Environment(\.colorScheme) private var colorScheme
    
    
    // 视图的展示和消失由导航栈控制
    var artwork: ArtWorksCell?
    @StateObject private var viewModel: UserArtWorkViewModel
    
    //用于控制弹窗的状态变量
    @State private var showResultAlert = false
    @State private var alertMessage = ""
    
    //视频播放相关变量
    @State private var player: AVPlayer?
    @State private var isPlaying: Bool = true
    @State private var showPlayPauseIcon: Bool = false
    //用于监听播放器状态的订阅者
    @State private var playerStatusObserver: AnyCancellable?
    //标记视频是否已准备好播放，用于控制封面和视频的切换
    @State private var isVideoReady = false
    
    
    init(transactionID: Int?,detail:ArtWorksCell?) {
        // _viewModel 的下划线语法用于在 init 中初始化 @StateObject
        _viewModel = StateObject(wrappedValue: UserArtWorkViewModel(transactionID: transactionID ?? nil))
        self.artwork = detail
    }
    
    
    
    private func safeAreaTopInset() -> CGFloat {
        let keyWindow = UIApplication.shared.connectedScenes
            .filter({ $0.activationState == .foregroundActive })
            .map({ $0 as? UIWindowScene })
            .compactMap({ $0 })
            .first?.windows
            .filter({ $0.isKeyWindow }).first
        return keyWindow?.safeAreaInsets.top ?? 0
    }
    
    
    private func togglePlayPause() {
        guard player != nil else { return }
        
        // 切换播放状态
        isPlaying.toggle()
        
        if isPlaying {
            player?.play()
        } else {
            player?.pause()
        }
        
        // 显示反馈图标，并在片刻后让它消失
//        showPlayPauseIcon = true
//        DispatchQueue.main.asyncAfter(deadline: .now() + 0.8) {
//            withAnimation {
//                showPlayPauseIcon = false
//            }
//        }
        showPlayPauseIcon = !isPlaying
    }
    
    
    // 初始化播放器并播放
    private func setupVideoPlayer() {
        guard player == nil, let urlString = viewModel.pollingResult?.links?.first, let url = URL(string: urlString) else {
            return
        }
        
        let playerItem = AVPlayerItem(url: url)
        player = AVPlayer(playerItem: playerItem)
        player?.isMuted = true
        
        // 4. 监听 playerItem 的状态
        playerStatusObserver = playerItem.publisher(for: \.status)
            .receive(on: DispatchQueue.main)
            .sink { [weak playerItem] status in
                switch status {
                case .readyToPlay:
                    // 当状态变为 readyToPlay 时，更新我们的状态变量
                    withAnimation { isVideoReady = true }
                                        player?.play() // 准备好后自动开始播放
                case .failed:
                    // 可以在这里处理加载失败的情况
                    print("视频加载失败: \(playerItem?.error?.localizedDescription ?? "未知错误")")
                    isVideoReady = false // 确保视频不显示
                default:
                    // .unknown 状态，什么都不做
                    break
                }
            }
        
        
        NotificationCenter.default.addObserver(
                    forName: .AVPlayerItemDidPlayToEndTime, // 监听视频播放结束的通知
                    object: player?.currentItem,           // 只关心当前这个播放项
                    queue: .main                           // 在主线程执行响应
                ) { _ in
                    // 当通知触发时，让播放器回到时间的起点
                    player?.seek(to: .zero)
                    // 然后再次播放
                    player?.play()
                }
    }
    
    // 暂停视频
    private func pauseVideo() {
        player?.pause()
        isPlaying = false // 确保状态同步
    }
    
    var body: some View {
        GeometryReader { geometry in
            ZStack{
                if colorScheme == .light {
                    Color.clear
                        .background(
                            Image("ProfileBackground")
                                .resizable() // 关键：使图片可缩放
                                .scaledToFill() // 填充整个区域
                                .edgesIgnoringSafeArea(.all)
                        )
                        .frame(maxWidth:.infinity,maxHeight: .infinity)
                }
                
                VStack{
                    //安全区域高度
                    Color.clear.frame(height:  safeAreaTopInset() )
                    
                    VStack{
                        VideoResultNavigationBarView(
                            viewModel: viewModel,
                            alertMessage: $alertMessage,
                            showResultAlert: $showResultAlert
                        )
                        
                        videoResult
                    }
                }
                .padding(.horizontal,15)
                .edgesIgnoringSafeArea(.top)
                .alert(alertMessage, isPresented: $showResultAlert) {
                    Button("好的", role: .cancel) { }
                }
                .onChange(of: viewModel.pollingResult?.status){ status in
                    if status == .success {
                        setupVideoPlayer()
                    }
                }
                .onAppear {
                    // 当视图出现时，开始轮询
                    viewModel.startPolling()
                    //            workInfo.decode(key: "size") ?? controller.size
                    if let work = artwork {
                        print("传入的值：",work)
                        viewModel.pollingResult = UserArtWorkRes(
                            id: work.id,
                            links: work.links,
                            status: work.status,
                            provider: work.provider,
                            model: work.model,
                            agentType: work.agentType,
                            statusDescribe: work.statusDescribe,
                            createDate: work.createDate,
                            creationDay: work.creationDay
                            
                        )
                    }
                }
                .onDisappear {
                    // 当视图消失时（例如用户返回上一页），停止轮询
                    viewModel.stopPolling()
                    viewModel.pollingResult = nil
                    
                    // 离开页面时，取消状态监听并暂停视频
                    playerStatusObserver?.cancel()
                    player?.pause()
                    // 移除循环播放的通知监听
                    NotificationCenter.default.removeObserver(self)
                }
            }
            .navigationBarHidden(true)
        }
        .background(DesignSystem.Colors.backgroundPage)
    }
        
        
    @ViewBuilder
    private var videoResult: some View {
        VStack(spacing: 20) {
            Spacer()
            if let result = viewModel.pollingResult {
                switch result.status {
                case .running:
                    loading
                        
                case .success:
                    ZStack{
                        if (result.agentType == AgentType.video || result.agentType == AgentType.effect),
                           let videoUrlString = result.links?.first,
                           URL(string: videoUrlString) != nil
                        {
                            loading
                                .opacity(isVideoReady ? 0 : 1)
                            
                            // 视频播放器
                            if let player = player {
                                VideoPlayer(player: player)
                                    .disabled(true)
                                    .opacity(isVideoReady ? 1 : 0) // 只有视频准备好后，才淡入显示
                            }
                            
                            Color.clear
                                .contentShape(Rectangle()) // 让整个透明区域都可以响应点击
                                .onTapGesture {
                                    togglePlayPause()
                                }
                        
                            
                            if showPlayPauseIcon {
                                Image(systemName: isPlaying ? "pause.fill" : "play.fill")
                                    .font(.system(size: 60, weight: .bold))
                                    .foregroundColor(.white.opacity(0.8))
                                    .transition(.opacity.animation(.easeInOut))
                                // 允许点击穿透图标，防止它挡住下面的UI层
                                    .allowsHitTesting(false)
                            }
                            
                        }else {
                            // MARK: - 其他或无效情况处理分支
                            fallbackView // 使用下面的占位视图
                        }
                    }
                    
                case .failed:
                    ZStack{
                        Color.clear
                            .contentShape(Rectangle()) // 让整个透明区域都可以响应点击
                        
                        VStack{
                            Image(systemName: "xmark.octagon.fill")
                                .font(.largeTitle)
                                .foregroundColor(.red)
                            Text("生成失败")
                                .font(.title)
                        }
                    }
                    //                    Text(result.failureReason ?? "未知错误")
                    //                        .foregroundColor(.secondary)
                default:
                    Text(result.status?.rawValue ?? "")
                }
                
            } else if viewModel.isLoading {
                loading
            }
            
            if let errorMessage = viewModel.errorMessage {
                Text("错误: \(errorMessage)")
                    .foregroundColor(.red)
            }
            Spacer()
            
            if viewModel.pollingResult?.status != .running {
                bottomSection
            }
        }
        
    }
    
    private var fallbackView: some View {
        VStack(spacing: 12) {
            Image(systemName: "questionmark.diamond.fill")
                .font(.system(size: 60))
                .foregroundColor(.secondary)
            Text("未返回有效的媒体链接")
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity, idealHeight: 300) // 适应宽度
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }

    
    // MARK: - UI 元素
    private var loading: some View {
        VStack {
            if let url = Bundle.main.url(forResource: "effect_fuzzy", withExtension: "webp") {
                AnimatedImage(url: url)
                    .indicator(SDWebImageActivityIndicator.medium)
                    .resizable()
                    .aspectRatio(1, contentMode: .fit)
            } else {
                ProgressView("正在生成...")
            }
            
            
            VStack(alignment:.center){
                HStack(spacing:0){
                    Text("退出页面后，AI将继续生成。您可随时点击右上角")
                    Image(systemName: "clock")
                        .foregroundStyle(DesignSystem.Colors.textPrimary)
                        .font(.system(size: DesignSystem.FontSize.md))
                }
                HStack{
                    Text("图标查看生成进度。欢迎前往主页继续探索")
                }
            }
            .font(.system(size: DesignSystem.FontSize.md))
            .foregroundColor(DesignSystem.Colors.textPrimary)
        }
        .opacity(isVideoReady ? 0 : 1)
    }
        
    /// 底部区域 (包含左侧信息和右侧操作按钮)
    private var bottomSection: some View {
        VStack {
            //MARK: 点击跳转
            Button( action: {
                guard let agentType = artwork?.agentType else { return }
                //跳转进入对应的创作页面
                switch agentType {
                case .image :
                    return navigator.push(Route.createImage(detail: artwork))
                case .video:
                    return  navigator.push(Route.createVideo(detail: artwork,createType: nil))
                case .effect:
                    return  navigator.push(Route.createEffectVideo( type: nil ,detail: artwork))
                }
                
            }) {
                Text("再次生成")
                    .font(.headline)
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity) // 使文本容器宽度最大化
                    .padding()
            }
            .background(DesignSystem.Colors.primary) // 按钮背景色
            .cornerRadius(15) // 圆角
            .frame(width: UIScreen.main.bounds.width * 0.9) // 屏幕宽度的80%
            .padding(.bottom, 30) // 底部间距
        }
    }
}


struct VideoResultNavigationBarView: View {
    @EnvironmentObject var navigator: FlowPathNavigator
    
    @ObservedObject var viewModel: UserArtWorkViewModel
    @Binding var alertMessage: String
    @Binding var showResultAlert: Bool
    
    private var createAgentType: HistoryMode? {
        guard let agentType  = viewModel.pollingResult?.agentType else {
            return nil
        }
        
        switch agentType {
        case .effect: return .effect
        case .image: return .image
        case .video: return .video
        }
    }
            
    var body: some View {
        VStack(spacing: 0) {
            // 自定义导航栏主体
            HStack {
                Image(systemName: "chevron.backward")
                    .foregroundStyle(DesignSystem.Colors.textPrimary)
                    .font(.title3)
                    .onTapGesture {
                        navigator.pop()
                    }
                
                Spacer()
                
                Image(systemName: "clock")
                    .foregroundStyle(DesignSystem.Colors.textPrimary)
                    .font(.title3)
                    .onTapGesture {
                        navigator.push(Route.artWorkHistory(defaultMode: createAgentType))
                    }
                
                //MARK: - 视频下载
                if viewModel.pollingResult?.status == .success, viewModel.pollingResult != nil {
                    Button(action: {
                        Task {
                            // 点击按钮时，调用保存图片的函数
                            await viewModel.download()
                        }
                    }) {
                        Image(systemName: "arrow.down.to.line.compact")
                            .font(.title3)
                            .foregroundStyle(DesignSystem.Colors.textPrimary)
                    }
                }
            }
            .frame(height: 30)
        }
        .frame(maxWidth: .infinity)
        .padding(.top,10)
        .padding(.bottom,5)
    }
}
