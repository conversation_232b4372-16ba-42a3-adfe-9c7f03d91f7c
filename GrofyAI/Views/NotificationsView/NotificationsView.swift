import SwiftUI

// 通知设置视图
struct NotificationsView: View {
    @State private var pushEnabled = true
    @State private var emailEnabled = false
    @State private var smsEnabled = true

    var body: some View {
        Form {
            Section(header: Text("通知方式")) {
                Toggle("推送通知", isOn: $pushEnabled)
                Toggle("邮件通知", isOn: $emailEnabled)
                Toggle("短信通知", isOn: $smsEnabled)
            }

            Section(header: Text("通知类型")) {
                Toggle("新消息", isOn: .constant(true))
                Toggle("系统公告", isOn: .constant(true))
                Toggle("活动提醒", isOn: .constant(false))
            }
        }
        .navigationTitle("通知设置")
    }
}
