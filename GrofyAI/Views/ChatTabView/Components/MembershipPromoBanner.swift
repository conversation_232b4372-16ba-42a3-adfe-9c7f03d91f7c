import SwiftUI

// MARK: - 会员购买提示横幅组件

struct MembershipPromoBanner: View {
    @EnvironmentObject private var authStore: AuthStore
    @EnvironmentObject private var globalAuthManager: GlobalAuthManager

    let onUpgradeAction: () -> Void

    @State private var isPressed = false

    init(onUpgradeAction: @escaping () -> Void = {}) {
        self.onUpgradeAction = onUpgradeAction
    }

    var body: some View {
        Group {
            if authStore.isPaidMember() {
                EmptyView()
            } else {
                Button(action: handleBannerTap) {
                    HStack(spacing: DesignSystem.Spacing.md) {
                        Spacer()

                        VStack(alignment: .leading, spacing: 2) {
                            Text("会员专享")
                                .subheadlineStyle()
                                .foregroundColor(.white)

                            Text("解锁更多AI功能")
                                .fontXS(weight: DesignSystem.FontWeight.regular)
                                .foregroundColor(.white.opacity(0.9))
                        }

                        Spacer()

                        Text("立即解锁")
                            .buttonTextStyle()
                            .foregroundColor(.white)
                            .padding(.horizontal, DesignSystem.Spacing.md)
                            .padding(.vertical, DesignSystem.Spacing.sm)
                            .background(
                                RoundedRectangle(cornerRadius: DesignSystem.Rounded.md)
                                    .fill(Color.white.opacity(0.2))
                            )
                            .overlay(
                                RoundedRectangle(cornerRadius: DesignSystem.Rounded.md)
                                    .stroke(Color.white.opacity(0.3), lineWidth: DesignSystem.BorderWidth.regular)
                            )
                    }
                    .padding(.leading, DesignSystem.Spacing.xl)
                    .padding(.trailing, DesignSystem.Spacing.lg)
                    .padding(.vertical, DesignSystem.Spacing.md)
                    .background(
                        Image("ImageMembershipBanner")
                            .resizable()
                            .aspectRatio(contentMode: backgroundContentMode())
                            .animation(.easeInOut(duration: 0.3), value: backgroundContentMode())
                    )
                }
                .buttonStyle(.plain)
                .frame(height: 60)
                .frame(maxWidth: .infinity)
                .cornerRadius(DesignSystem.Rounded.md)
                .scaleEffect(isPressed ? 0.98 : 1.0)
                .opacity(isPressed ? 0.9 : 1.0)
                .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
                    withAnimation(.easeInOut(duration: 0.1)) {
                        isPressed = pressing
                    }
                }, perform: {})
            }
        }
    }

    /// 横幅点击事件
    private func handleBannerTap() {
        if authStore.getAccessToken() != nil {
            onUpgradeAction()
        } else {
            globalAuthManager.requestAuthentication()
        }
    }

    /// 判断是否为横屏模式 - 基于设备实际屏幕方向
    private func isLandscapeMode() -> Bool {
        let screenSize = UIScreen.main.bounds.size
        return screenSize.width > screenSize.height
    }

    /// 响应式背景图片显示模式 - 根据设备类型和屏幕方向优化显示效果
    private func backgroundContentMode() -> ContentMode {
        let screenSize = UIScreen.main.bounds.size
        let isLandscape = screenSize.width > screenSize.height

        // 判断是否为iPad（基于屏幕最小边长）
        let isIPad = min(screenSize.width, screenSize.height) >= 768

        let contentMode: ContentMode = if isIPad {
            // iPad：横竖屏都使用.fill，确保背景图片填充整个容器宽度
            // iPad屏幕较大，即使竖屏模式下使用.fill也能保持VIP图标可见性
            .fill
        } else {
            // iPhone：横屏用.fill，竖屏用.fit（保护VIP图标等重要元素）
            isLandscape ? .fill : .fit
        }

        return contentMode
    }
}
