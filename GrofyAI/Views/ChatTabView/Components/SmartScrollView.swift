import SwiftUI

// MARK: - 智能滚动视图组件

struct SmartScrollView<Content: View>: View {
    
    let content: () -> Content
    let threshold: CGFloat 
    let animationDuration: Double
    
    @State private var contentHeight: CGFloat = 0
    @State private var availableHeight: CGFloat = 0
    @State private var isInitialized: Bool = false
    
    init(
        threshold: CGFloat = 20,
        animationDuration: Double = 0.25,
        @ViewBuilder content: @escaping () -> Content
    ) {
        self.threshold = threshold
        self.animationDuration = animationDuration
        self.content = content
    }
    
    // MARK: - 视图主体
    
    var body: some View {
        ViewThatFits(in: .vertical) {
            // 当内容适合视图时，不使用ScrollView
            GeometryReader { geometry in
                content()
                    .frame(minHeight: geometry.size.height)
                    .onAppear {
                        initializeLayout(geometry: geometry)
                    }
                    .onChange(of: geometry.size) { newSize in
                        handleGeometryChange(newSize: newSize)
                    }
            }
            
            // 当内容超出视图时，使用ScrollView
            GeometryReader { geometry in
                ScrollView(.vertical, showsIndicators: false) {
                    content()
                        .frame(minHeight: geometry.size.height) 
                }
                .scrollDismissesKeyboard(.interactively)
                .onAppear {
                    initializeLayout(geometry: geometry)
                }
                .onChange(of: geometry.size) { newSize in
                    handleGeometryChange(newSize: newSize)
                }
            }
        }
    }
    
    // MARK: - 私有方法

    /// 初始化布局
    private func initializeLayout(geometry: GeometryProxy) {
        availableHeight = geometry.size.height
        isInitialized = true
    }

    /// 处理几何变化
    private func handleGeometryChange(newSize: CGSize) {
        availableHeight = newSize.height
    }
}
