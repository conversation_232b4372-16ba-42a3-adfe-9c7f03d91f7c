import SwiftUI

// MARK: - 功能卡片布局组件

struct FeatureCardsLayout: View {
    let features: [FeatureCardItem]
    let onFeatureTap: (FeatureCardItem) -> Void

    @State private var rightColumnHeight: CGFloat = 0

    var body: some View {
        HStack(alignment: .top, spacing: DesignSystem.Spacing.md) {
            // 相册卡片 - 左列，高度匹配右侧列总高度
            HeightMatchingFeatureCard(
                feature: features[0].feature,
                targetHeight: rightColumnHeight > 0 ? rightColumnHeight : nil
            ) {
                onFeatureTap(features[0])
            }
            .frame(maxWidth: .infinity)

            // 右侧第一列
            VStack(alignment: .leading, spacing: DesignSystem.Spacing.md) {
                // 视频卡片 - 高度由内容自动决定
                FeatureCard(feature: features[1].feature) {
                    onFeatureTap(features[1])
                }
                .frame(maxWidth: .infinity)

                // 文档卡片 - 高度由内容自动决定
                FeatureCard(feature: features[2].feature) {
                    onFeatureTap(features[2])
                }
                .frame(maxWidth: .infinity)
            }
            .background(
                GeometryReader { geometry in
                    Color.clear
                        .onAppear {
                            rightColumnHeight = geometry.size.height
                        }
                        .onChange(of: geometry.size.height) { newHeight in
                            rightColumnHeight = newHeight
                        }
                }
            )

            // 右侧第二列
            VStack(alignment: .leading, spacing: DesignSystem.Spacing.md) {
                // 音频卡片 - 高度由内容自动决定
                FeatureCard(feature: features[3].feature) {
                    onFeatureTap(features[3])
                }
                .frame(maxWidth: .infinity)

                // 图书卡片 - 高度由内容自动决定
                FeatureCard(feature: features[4].feature) {
                    onFeatureTap(features[4])
                }
                .frame(maxWidth: .infinity)
            }
        }
    }
}
