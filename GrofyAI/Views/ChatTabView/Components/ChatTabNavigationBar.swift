import SwiftUI

// MARK: - 聊天页面顶部导航栏组件

struct ChatTabNavigationBar: View {
    @ObservedObject private var modelManager = ModelManager.shared
    let onModelSelectionTap: () -> Void
    let onMenuTap: () -> Void
    @Environment(\.colorScheme) private var colorScheme

    var body: some View {
        VStack(spacing: 0) {
            HStack(alignment: .center, spacing: DesignSystem.Spacing.lg) {
                But<PERSON>(action: onMenuTap) {
                    Image(systemName: "line.3.horizontal")
                        .fontXXL()
                        .foregroundColor(DesignSystem.Colors.textPrimary)
                }
                .buttonStyle(.plain)

                Spacer()

                ModelSelectionButton.compact(onTap: onModelSelectionTap)

                Spacer()

                Image(systemName: "line.3.horizontal")
                    .fontXXL()
                    .foregroundColor(.clear)
            }
            .frame(height: 44)
            .padding(.horizontal, DesignSystem.Spacing.lg)

            Spacer()
                .frame(height: DesignSystem.Spacing.sm)
        }
    }
}
