import SwiftUI

/// 输入框按钮组件
struct InputButton: View {
    let iconName: String
    let action: () -> Void
    let isEnabled: Bool
    let isSystemIcon: Bool

    @State private var cachedForegroundColor: Color

    init(iconName: String, isEnabled: Bool = true, isSystemIcon: Bool = false, action: @escaping () -> Void) {
        self.iconName = iconName
        self.isEnabled = isEnabled
        self.isSystemIcon = isSystemIcon
        self.action = action
        _cachedForegroundColor = State(initialValue: isEnabled ? DesignSystem.Colors.textSecondary : DesignSystem.Colors
            .textSecondary.opacity(0.4)
        )
    }

    init(systemIcon: String, isEnabled: Bool = true, action: @escaping () -> Void) {
        iconName = systemIcon
        self.isEnabled = isEnabled
        isSystemIcon = true
        self.action = action
        _cachedForegroundColor = State(initialValue: isEnabled ? DesignSystem.Colors.textSecondary : DesignSystem.Colors
            .textSecondary.opacity(0.4)
        )
    }

    var body: some View {
        Button(action: action) {
            Group {
                if isSystemIcon {
                    Image(systemName: iconName)
                } else {
                    Image(iconName)
                }
            }
            .foregroundColor(cachedForegroundColor)
            .font(.system(size: DesignSystem.FontSize.lg, weight: DesignSystem.FontWeight.medium))
        }
        .buttonStyle(.plain)
        .disabled(!isEnabled)
        .onChange(of: isEnabled) { enabled in
            cachedForegroundColor = enabled ? DesignSystem.Colors.textSecondary : DesignSystem.Colors.textSecondary
                .opacity(0.4)
        }
    }
}

// MARK: - 切换按钮组件

struct ToggleInputButton: View {
    let iconName: String
    let title: String
    @Binding var isSelected: Bool
    let isEnabled: Bool
    let action: () -> Void

    @State private var cachedBackgroundColor: Color
    @State private var cachedForegroundColor: Color
    @State private var cachedBorderColor: Color
    @State private var cachedOpacity: Double

    init(
        iconName: String,
        title: String,
        isSelected: Binding<Bool>,
        isEnabled: Bool = true,
        action: @escaping () -> Void
    ) {
        self.iconName = iconName
        self.title = title
        _isSelected = isSelected
        self.isEnabled = isEnabled
        self.action = action

        let selected = isSelected.wrappedValue
        if isEnabled {
            _cachedBackgroundColor = State(initialValue: selected ? DesignSystem.Colors.primary : Color.clear)
            _cachedForegroundColor = State(initialValue: selected ? .white : DesignSystem.Colors.textSecondary)
        } else {
            _cachedBackgroundColor = State(initialValue: Color.clear)
            _cachedForegroundColor = State(initialValue: DesignSystem.Colors.textTertiary)
        }
        _cachedBorderColor = State(initialValue: DesignSystem.Colors.border)
        _cachedOpacity = State(initialValue: isEnabled ? 1.0 : 0.7)
    }

    var body: some View {
        Button(action: action) {
            HStack(spacing: DesignSystem.Spacing.xs) {
                Image(iconName)
                    .foregroundColor(cachedForegroundColor)

                Text(title)
                    .fontSM(weight: DesignSystem.FontWeight.medium)
                    .foregroundColor(cachedForegroundColor)
            }
            .padding(.horizontal, DesignSystem.Spacing.sm)
            .padding(.vertical, DesignSystem.Spacing.xs)
            .background(
                RoundedRectangle(cornerRadius: DesignSystem.Rounded.sm)
                    .fill(cachedBackgroundColor)
            )
            .overlay(
                RoundedRectangle(cornerRadius: DesignSystem.Rounded.sm)
                    .stroke(cachedBorderColor, lineWidth: DesignSystem.BorderWidth.thin)
            )
        }
        .buttonStyle(.plain)
        .disabled(!isEnabled)
        .opacity(cachedOpacity)
        .onChange(of: isSelected) { selected in
            updateCachedColors(selected: selected)
        }
        .onChange(of: isEnabled) { enabled in
            updateCachedOpacity(enabled: enabled)
        }
    }

    private func updateCachedColors(selected: Bool) {
        if !isEnabled {
            cachedBackgroundColor = Color.clear
            cachedForegroundColor = DesignSystem.Colors.textTertiary
        } else {
            cachedBackgroundColor = selected ? DesignSystem.Colors.primary : Color.clear
            cachedForegroundColor = selected ? .white : DesignSystem.Colors.textSecondary
        }
    }

    private func updateCachedOpacity(enabled: Bool) {
        cachedOpacity = enabled ? 1.0 : 0.7
    }
}

// MARK: - 发送按钮组件

struct SendButton: View {
    let action: () -> Void
    @Binding var inputText: String
    let additionalDisableCondition: Bool

    init(inputText: Binding<String>, additionalDisableCondition: Bool = false, action: @escaping () -> Void) {
        _inputText = inputText
        self.additionalDisableCondition = additionalDisableCondition
        self.action = action
    }

    private var buttonState: (isEnabled: Bool, foregroundColor: Color, opacity: Double) {
        let textIsValid = !inputText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
        let isEnabled = textIsValid && !additionalDisableCondition

        return (
            isEnabled: isEnabled,
            foregroundColor: isEnabled ? DesignSystem.Colors.primary : DesignSystem.Colors.textSecondary,
            opacity: isEnabled ? 1.0 : 0.4
        )
    }

    var body: some View {
        let state = buttonState

        Button(action: action) {
            Image("IconInputSend")
                .foregroundColor(state.foregroundColor)
        }
        .buttonStyle(.plain)
        .disabled(!state.isEnabled)
        .opacity(state.opacity)
    }
}
