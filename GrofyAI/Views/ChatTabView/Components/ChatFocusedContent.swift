import SwiftUI

// MARK: - 聊天页面聚焦状态内容组件

struct ChatFocusedContent: View {
    var body: some View {
        VStack(spacing: 0) {
            Spacer()

            VStack(spacing: 20) {
                BrandLogoView.largeIcon()

                Text("开启新会话")
                    .popupTitleStyle()

                // Text("输入您的问题，AI助手将为您提供帮助")
                //     .font(.system(size: 14))
                //     .foregroundColor(DesignSystem.Colors.textTertiary)
                //     .multilineTextAlignment(.center)
            }
            .frame(maxWidth: .infinity)

            Spacer()
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .padding(.horizontal, DesignSystem.Spacing.lg)
        .transition(.opacity.combined(with: .scale(scale: 0.98)))
    }
}
