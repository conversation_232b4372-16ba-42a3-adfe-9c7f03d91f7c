import FlowStacks
import MijickPopups
import SwiftUI

// MARK: - 设置界面

struct SettingsView: View {
    @EnvironmentObject var navigator: FlowPathNavigator
    @EnvironmentObject private var authStore: AuthStore
    @State private var showLogoutAlert = false
    @State private var showDeleteAccountAlert = false
    @State private var isDeleting = false
    private let deleteAccountService = DeleteAccountService()

    var body: some View {
        VStack(spacing: 0) {
            settingsContent
        }
        .navigationBarBackButtonHidden()
        .navigationBarTitleDisplayMode(.inline)
        .toolbar {
            ToolbarItem(placement: .topBarLeading) {
                BackButton(onBack: handleBackTap)
            }
            ToolbarItem(placement: .principal) {
                Text("设置")
                    .font(.system(size: DesignSystem.FontSize.lg, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.textPrimary)
            }
        }
        .hideNavigationBarLine(backgroundColor: DesignSystem.Colors.backgroundPage)
        .background(DesignSystem.Colors.backgroundPage)
        .overlay(
            Group {
                if isDeleting {
                    Color.black.opacity(0.3)
                        .ignoresSafeArea()
                        .overlay(
                            VStack(spacing: 16) {
                                ProgressView()
                                    .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                    .scaleEffect(1.2)
                                Text("正在注销账号...")
                                    .foregroundColor(.white)
                                    .font(.system(size: DesignSystem.FontSize.lg, weight: .medium))
                            }
                            .padding(24)
                            .background(Color.black.opacity(0.8))
                            .cornerRadius(12)
                        )
                }
            }
        )
        .alert("退出登录", isPresented: $showLogoutAlert) {
            Button("取消", role: .cancel) {}
            Button("确定", role: .destructive) {
                logout()
            }
        } message: {
            Text("确定要退出登录吗？")
        }
    }

    // MARK: - 设置内容

    @ViewBuilder
    private var settingsContent: some View {
        VStack(spacing: 0) {
            List {
                Section(header: Text("通用")) {
                    Button(action: {
                        navigator.push(Route.themeSetting)
                    }) {
                        HStack {
                            Image("IconTheme")
                                .foregroundColor(DesignSystem.Colors.textSecondary)
                                .font(.system(size: 16, weight: .medium))
                                .frame(width: 20)
                            Text("主题设置")
                            Spacer()
                            Image(systemName: "chevron.right")
                                .foregroundColor(DesignSystem.Colors.textSecondary)
                                .font(.system(size: 14, weight: .medium))
                        }
                    }
                    .foregroundColor(DesignSystem.Colors.textPrimary)

                    Button(action: {
                        handleLanguageSettingTap()
                    }) {
                        HStack {
                            Image("IconLanguage")
                                .foregroundColor(DesignSystem.Colors.textSecondary)
                                .font(.system(size: 16, weight: .medium))
                                .frame(width: 20)
                            Text("语言设置")
                            Spacer()
                            Image(systemName: "chevron.right")
                                .foregroundColor(DesignSystem.Colors.textSecondary)
                                .font(.system(size: 14, weight: .medium))
                        }
                    }
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                }

                Section(header: Text("隐私与安全")) {
                    Button(action: {
                        handleTermsOfServiceTap()
                    }) {
                        HStack {
                            Image("IconTerms")
                                .foregroundColor(DesignSystem.Colors.textSecondary)
                                .font(.system(size: 16, weight: .medium))
                                .frame(width: 20)
                            Text("使用条款")
                            Spacer()
                            Image(systemName: "chevron.right")
                                .foregroundColor(DesignSystem.Colors.textSecondary)
                                .font(.system(size: 14, weight: .medium))
                        }
                    }
                    .foregroundColor(DesignSystem.Colors.textPrimary)

                    Button(action: {
                        handlePrivacyPolicyTap()
                    }) {
                        HStack {
                            Image("IconPrivacy")
                                .foregroundColor(DesignSystem.Colors.textSecondary)
                                .font(.system(size: 16, weight: .medium))
                                .frame(width: 20)
                            Text("隐私政策")
                            Spacer()
                            Image(systemName: "chevron.right")
                                .foregroundColor(DesignSystem.Colors.textSecondary)
                                .font(.system(size: 14, weight: .medium))
                        }
                    }
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                }

                Section(header: Text("支持")) {
                    Button(action: {
                        navigator.push(Route.problemFeedback)
                    }) {
                        HStack {
                            Image("IconFeedback")
                                .foregroundColor(DesignSystem.Colors.textSecondary)
                                .font(.system(size: 16, weight: .medium))
                                .frame(width: 20)
                            Text("问题反馈")
                            Spacer()
                            Image(systemName: "chevron.right")
                                .foregroundColor(DesignSystem.Colors.textSecondary)
                                .font(.system(size: 14, weight: .medium))
                        }
                    }
                    .foregroundColor(DesignSystem.Colors.textPrimary)

                    Button(action: {
                        AppStoreHelper.openAppStoreWriteReviewPage()
                    }) {
                        HStack {
                            Image("IconRating")
                                .foregroundColor(DesignSystem.Colors.textSecondary)
                                .font(.system(size: 16, weight: .medium))
                                .frame(width: 20)
                            Text("应用评分")
                            Spacer()
                            Image(systemName: "chevron.right")
                                .foregroundColor(DesignSystem.Colors.textSecondary)
                                .font(.system(size: 14, weight: .medium))
                        }
                    }
                    .foregroundColor(DesignSystem.Colors.textPrimary)

                    ShareLink(item: shareText) {
                        HStack {
                            Image("IconShare")
                                .foregroundColor(DesignSystem.Colors.textSecondary)
                                .font(.system(size: 16, weight: .medium))
                                .frame(width: 20)
                            Text("分享应用")
                            Spacer()
                            Image(systemName: "chevron.right")
                                .foregroundColor(DesignSystem.Colors.textSecondary)
                                .font(.system(size: 14, weight: .medium))
                        }
                    }
                    .foregroundColor(DesignSystem.Colors.textPrimary)

                    Button(action: {
                        navigator.push(Route.aboutUs)
                    }) {
                        HStack {
                            Image("IconAbout")
                                .foregroundColor(DesignSystem.Colors.textSecondary)
                                .font(.system(size: 16, weight: .medium))
                                .frame(width: 20)
                            Text("关于我们")
                            Spacer()
                            Image(systemName: "chevron.right")
                                .foregroundColor(DesignSystem.Colors.textSecondary)
                                .font(.system(size: 14, weight: .medium))
                        }
                    }
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                }
            }
            .listStyle(InsetGroupedListStyle())
            .scrollContentBackground(.hidden)
            .background(DesignSystem.Colors.backgroundPage)
            .scrollDisabled(true)

            if authStore.getAccessToken() != nil {
                accountActionButtons
            }
        }
    }

    @ViewBuilder
    private var accountActionButtons: some View {
        VStack(spacing: DesignSystem.Spacing.md) {
            Button(action: {
                showDeleteAccountPopup()
            }) {
                Text("注销账号")
                    .buttonTextStyle()
                    .foregroundColor(DesignSystem.Colors.error)
                    .frame(maxWidth: .infinity)
                    .frame(height: 48)
                    .background(DesignSystem.Colors.error.opacity(0.1))
                    .cornerRadius(DesignSystem.Rounded.md)
            }
            .buttonStyle(AccountActionButtonStyle())

            Button(action: {
                showLogoutAlert = true
            }) {
                Text("退出登录")
                    .buttonTextStyle()
                    .foregroundColor(DesignSystem.Colors.primary)
                    .frame(maxWidth: .infinity)
                    .frame(height: 48)
                    .background(DesignSystem.Colors.primary.opacity(0.1))
                    .cornerRadius(DesignSystem.Rounded.md)
            }
            .buttonStyle(AccountActionButtonStyle())
        }
        .padding(.horizontal, DesignSystem.Spacing.lg)
        .padding(.vertical, DesignSystem.Spacing.lg)
        .background(DesignSystem.Colors.backgroundPage)
    }

    private func handleBackTap() {
        navigator.pop()
    }

    private func logout() {
        authStore.clearUser()
        ToastManager.shared.showSuccess("已退出登录")
    }

    @MainActor
    private func deleteAccount() async {
        isDeleting = true

        do {
            try await deleteAccountService.deleteAccount()

            ToastManager.shared.showSuccess("账号已注销")
            authStore.clearUser()
            navigator.popToRoot()

        } catch let error as BusinessError {
            ToastManager.shared.showError(error.message.isEmpty ? "注销失败，请稍后重试" : error.message)
        } catch {
            ToastManager.shared.showError("注销失败，请稍后重试")
        }

        isDeleting = false
    }

    private func handleTermsOfServiceTap() {
        WebViewManager.shared.openTermsOfService()
    }

    private func handlePrivacyPolicyTap() {
        WebViewManager.shared.openPrivacyPolicy()
    }

    private func handleLanguageSettingTap() {
        openLanguageSettings()
    }

    private func openLanguageSettings() {
        guard let settingsURL = URL(string: UIApplication.openSettingsURLString) else {
            return
        }

        if UIApplication.shared.canOpenURL(settingsURL) {
            UIApplication.shared.open(settingsURL, options: [:], completionHandler: nil)
        }
    }

    private func showDeleteAccountPopup() {
        Task {
            await DeleteAccountConfirmPopup(
                onDelete: {
                    Task {
                        await deleteAccount()
                    }
                }
            )
            .present()
        }
    }

    private var shareText: String {
        return "快来试试这款超棒的AI应用吧！\n\n\(AppConfig.App.displayName) - 您的智能AI助手，支持对话、图像识别、知识库问答等多种功能。\n\n下载链接：\(AppConfig.Store.AppUrl)"
    }
}

// MARK: - 账号操作按钮样式

struct AccountActionButtonStyle: ButtonStyle {
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .scaleEffect(configuration.isPressed ? 0.98 : 1.0)
            .opacity(configuration.isPressed ? 0.8 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: configuration.isPressed)
    }
}
