import MijickPopups
import SwiftUI

// MARK: - 注销账号确认弹窗

struct DeleteAccountConfirmPopup: BottomPopup {
    @State private var countdown = 10
    @State private var countdownTask: Task<Void, Never>?

    let onDelete: (() -> Void)?

    init(onDelete: (() -> Void)? = nil) {
        self.onDelete = onDelete
    }

    var body: some View {
        createContent()
            .onAppear {
                startCountdown()
            }
            .onDisappear {
                countdownTask?.cancel()
            }
    }

    func createContent() -> some View {
        let popupHeight: CGFloat = 380

        return VStack(spacing: 0) {
            RoundedRectangle(cornerRadius: 2.5)
                .fill(DesignSystem.Colors.textTertiary.opacity(0.6))
                .frame(width: 40, height: 5)
                .padding(.top, DesignSystem.Spacing.sm)
                .padding(.bottom, DesignSystem.Spacing.md)

            VStack(spacing: 20) {
                Image(systemName: "exclamationmark.triangle.fill")
                    .font(.system(size: 52))
                    .foregroundColor(.orange)
                    .padding(.top, DesignSystem.Spacing.md)

                Text("注销账号")
                    .font(.system(size: 22, weight: .bold))
                    .foregroundColor(DesignSystem.Colors.textPrimary)

                Text("注销账号后，您的所有数据及权益将被永久删除且无法恢复。")
                    .font(.system(size: 16))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
                    .multilineTextAlignment(.center)
                    .lineSpacing(6)
                    .padding(.horizontal, DesignSystem.Spacing.xl)

                Text("确定要注销账号吗？")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                    .padding(.bottom, DesignSystem.Spacing.md)

                VStack(spacing: 12) {
                    Button(action: {
                        if countdown == 0 {
                            onDelete?()
                            Task {
                                await dismissLastPopup()
                            }
                        }
                    }) {
                        HStack(spacing: 6) {
                            Text("注销账号")
                                .font(.system(size: 17, weight: .semibold))

                            if countdown > 0 {
                                Text("(\(countdown)s)")
                                    .font(.system(size: 15, weight: .medium))
                            }
                        }
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .frame(height: 52)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(countdown > 0 ? Color.gray : Color.red)
                        )
                        .animation(.easeInOut(duration: 0.2), value: countdown)
                    }
                    .disabled(countdown > 0)

                    Button(action: {
                        Task {
                            await dismissLastPopup()
                        }
                    }) {
                        Text("取消")
                            .font(.system(size: 17, weight: .medium))
                            .foregroundColor(DesignSystem.Colors.textPrimary)
                            .frame(maxWidth: .infinity)
                            .frame(height: 52)
                            .background(
                                RoundedRectangle(cornerRadius: 12)
                                    .fill(DesignSystem.Colors.backgroundInput)
                            )
                    }
                }
                .padding(.horizontal, DesignSystem.Spacing.xl)
                .padding(.bottom, DesignSystem.Spacing.xl)
            }
        }
        .frame(maxWidth: .infinity)
        .frame(height: popupHeight)
        .background(DesignSystem.Colors.backgroundCard)
        .clipShape(RoundedRectangle(cornerRadius: DesignSystem.Rounded.lg))
    }

    /// 开始倒计时
    @MainActor
    private func startCountdown() {
        countdownTask = Task {
            for i in (0..<10).reversed() {
                if Task.isCancelled { break }

                countdown = i

                try? await Task.sleep(nanoseconds: 1_000_000_000)
            }
        }
    }
}
