//
//  ProblemFeedbackView.swift
//  GrofyAI
//
//  Created by kissy on 2025/7/21.
//
import FlowStacks
import Kingfisher
import SwiftUI

struct ProblemFeedbackView: View {
    @EnvironmentObject var navigator: FlowPathNavigator
    @StateObject private var controller = ProblemFeedbackController()

    var body: some View {
        ZStack {
            VStack(spacing: 0) {
                ScrollView {
                    problemFeedbackContent
                }
            }
            .navigationBarBackButtonHidden()
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .topBarLeading) {
                    BackButton(onBack: { navigator.pop() })
                }
                ToolbarItem(placement: .principal) {
                    Text("问题反馈")
                        .font(.system(size: DesignSystem.FontSize.lg, weight: .medium))
                        .foregroundColor(DesignSystem.Colors.textPrimary)
                }
            }
            .hideNavigationBarLine(backgroundColor: DesignSystem.Colors.backgroundPage)
            .background(DesignSystem.Colors.backgroundPage)

            // 提交时的加载覆盖层
            if controller.isSubmitting {
                Color.black.opacity(0.4).ignoresSafeArea()
                ProgressView()
                    .progressViewStyle(CircularProgressViewStyle(tint: .white))
                    .padding()
                    .background(Color.black.opacity(0.6))
                    .cornerRadius(10)
            }
        }
        // 绑定错误弹窗
        .alert("错误", isPresented: $controller.showError, presenting: controller.errorMessage) { _ in
            Button("好的") {
                // 点击按钮后清空错误信息
                controller.errorMessage = nil
            }
        } message: { message in
            Text(message)
        }
    }

    @ViewBuilder
    private var problemFeedbackContent: some View {
        VStack(alignment: .leading, spacing: 20) {
            // --- 联系邮箱输入框 ---
            Text("联系邮箱 *")
                .foregroundColor(DesignSystem.Colors.textPrimary)
                .font(.system(size: 16, weight: .medium))

            TextField(
                "",
                text: $controller.contactEmail,
                prompt: Text("请输入您的联系邮箱,方便我们与您联系").foregroundColor(DesignSystem.Colors.textHint)
            )
            .padding()
            .background(DesignSystem.Colors.backgroundCard)
            .cornerRadius(DesignSystem.Rounded.md)
            .foregroundColor(DesignSystem.Colors.textPrimary) // 用户输入文字的颜色
            .keyboardType(.emailAddress)
            .autocapitalization(.none)

            // --- 问题反馈输入框 ---
            Text("问题反馈 *")
                .foregroundColor(DesignSystem.Colors.textPrimary)
                .font(.system(size: 16, weight: .medium))

            // 使用 ZStack 为 TextEditor 添加 Placeholder
            ZStack(alignment: .topLeading) {
                TextEditor(text: $controller.feedbackText)
                    .scrollContentBackground(.hidden) // iOS 16+ 使背景色生效
                    .padding(8) // 内边距
                    .frame(height: 200)
                    .background(DesignSystem.Colors.backgroundCard)
                    .cornerRadius(DesignSystem.Rounded.md)
                    .foregroundColor(DesignSystem.Colors.textPrimary) // 用户输入文字的颜色

                // 当 TextEditor 内容为空时，显示占位符
                if controller.feedbackText.isEmpty {
                    Text("请在此处描述您遇到的问题或建议...")
                        .foregroundColor(DesignSystem.Colors.textHint)
                        .padding(16)
                        .allowsHitTesting(false) // 允许点击穿透到 TextEditor
                }
            }

            // --- 问题反馈图片 ---
            Text("上传图片")
                .foregroundColor(DesignSystem.Colors.textPrimary)
                .font(.system(size: 16, weight: .medium))
            ChooseImageView(controller: controller)

            Spacer() // 将按钮推到底部

            // --- 提交按钮 ---
            Button(action: {
                hideKeyboard()
                controller.onSubmit {
                    // 提交成功后，返回上一页
                    navigator.pop()
                }
            }) {
                Text("提 交")
                    .font(.headline)
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity) // 撑满宽度
                    .frame(height: 50)
//                    .background(DesignSystem.Colors.primary)
//                    .cornerRadius(DesignSystem.Rounded.md)
            }
            .background(DesignSystem.Colors.primary)
            .cornerRadius(DesignSystem.Rounded.md) // 把圆角应用在主背景色上
            .overlay(
                // 只有在禁用时才显示这个视图
                Group {
                    if controller.isDisabled {
                        // 创建一个半透明的灰色蒙层
                        Color.gray.opacity(0.3) // 你可以调整不透明度来达到最佳效果
                            // 确保蒙层也有和按钮一样的圆角
                            .cornerRadius(DesignSystem.Rounded.md)
                    }
                }
            )
            // 当反馈内容为空时，禁用按钮
            .disabled(controller.isDisabled)
            .animation(.easeInOut(duration: 0.2), value: controller.isDisabled)
            
        }
        .onTapGesture {
            hideKeyboard()
        }
        .padding()
    }
}

// MARK: - ChooseImageView

struct ChooseImageView: View {
    @Environment(\.presentationMode) private var presentationMode
    @ObservedObject var controller: ProblemFeedbackController

    @State private var showLibraryMultiPicker = false

    // 定义网格布局
    private let gridItems = [
        GridItem(.flexible()),
        GridItem(.flexible()),
        GridItem(.flexible()),
        GridItem(.flexible()),
        GridItem(.flexible()),
    ]

    var body: some View {
        LazyVGrid(columns: gridItems, spacing: 10) {
            // 遍历所有待显示的图片
            ForEach(controller.displayImages) { image in
                imageCell(for: image)
            }

            // 如果还能添加，则显示添加按钮
            if controller.canAddMoreImages {
                addCell
            }
        }
        .sheet(isPresented: $showLibraryMultiPicker) {
            // sheet 的内容：将结果绑定到我们的临时状态变量
            let limit = controller.maxImageCount - controller.displayImages.count
            MultiImagePicker(selectionLimit: limit) { selectedImages in
                // 选择完成后的回调
                controller.handleSelected(images: selectedImages)
            }
        }
    }

    /// "添加图片"按钮的视图
    private var addCell: some View {
        ZStack {
            RoundedRectangle(cornerRadius: 8)
                .stroke(style: StrokeStyle(lineWidth: 1, dash: [5]))
                .foregroundColor(DesignSystem.Colors.textHint)
                .frame(width: 64, height: 64)

            Image(systemName: "plus")
                .font(.title)
                .foregroundColor(DesignSystem.Colors.textHint)
        }
        .contentShape(Rectangle())
        .onTapGesture {
            showLibraryMultiPicker = true
        }
    }

    /// 单个图片单元格的视图
    @ViewBuilder
    private func imageCell(for image: DisplayImage) -> some View {
        ZStack(alignment: .center) {
            // 基础图片视图
            baseImageView(for: image.state)

            // 根据状态添加覆盖层
            switch image.state {
            case .uploading:
                // 上传中：半透明黑色覆盖 + 加载菊花
                Color.black.opacity(0.3).cornerRadius(8)
                ProgressView().tint(.white)
            case .failed:
                // 失败：半透明黑色覆盖 + 错误图标
                Color.black.opacity(0.5).cornerRadius(8)
                Image(systemName: "exclamationmark.triangle.fill")
                    .foregroundColor(.yellow)
                    .font(.title2)
            default:
                // .local 或 .uploaded 状态不需要覆盖层
                EmptyView()
            }

            // 删除按钮（始终在右上角）
            VStack {
                HStack {
                    Spacer()
                    Button(action: {
                        controller.removeImage(id: image.id)
                    }) {
                        Image(systemName: "xmark.circle.fill")
                            .foregroundColor(.gray)
                            .background(Circle().fill(.white))
                            .font(.title3)
                    }
                }
                Spacer()
            }
            .padding(-6) // 将按钮稍微拉出一点，视觉效果更好
        }
        .frame(width: 64, height: 64)
    }

    /// 基础图片视图，处理Kingfisher的无感加载
    @ViewBuilder
    private func baseImageView(for state: ImageState) -> some View {
        // 这是实现“无感”过渡的核心
        switch state {
        case .uploaded(let url, let previewImage):
            // 对已上传的图片使用 Kingfisher
            KFImage(URL(string: url))
                // **【关键】**: 使用本地的 UIImage 作为占位符。
                // Kingfisher会立即显示此占位符，然后在后台下载远程URL，
                // 下载完成后平滑地替换它。
                .placeholder {
                    Image(uiImage: previewImage)
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                }
                .fade(duration: 0.25) // 淡入淡出过渡效果
                .resizable()
                .aspectRatio(contentMode: .fill)
                .frame(width: 64, height: 64)
                .cornerRadius(8)
                .clipped()
        default:
            // 对于 .local, .uploading, .failed, 直接显示本地 UIImage
            if let uiImage = state.image {
                Image(uiImage: uiImage)
                    .resizable()
                    .aspectRatio(contentMode: .fill)
                    .frame(width: 64, height: 64)
                    .cornerRadius(8)
                    .clipped()
            }
        }
    }
}
