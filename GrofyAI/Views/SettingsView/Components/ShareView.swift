//
//  ShareView.swift
//  GrofyAI
//
//  Created by kissy on 2025/7/21.
//

import SwiftUI

// 使用 UIViewControllerRepresentable 来包装 UIKit 的 UIActivityViewController
struct ShareView: UIViewControllerRepresentable {
    
    var activityItems: [Any]
    var applicationActivities: [UIActivity]? = nil
    
    // 创建底层的 UIViewController
    func makeUIViewController(context: Context) -> UIActivityViewController {
        let controller = UIActivityViewController(activityItems: activityItems, applicationActivities: applicationActivities)
        return controller
    }
    
    // 更新 UIViewController (在这里我们不需要做什么)
    func updateUIViewController(_ uiViewController: UIActivityViewController, context: Context) {}
}
