import FlowStacks
import SwiftUI

struct AboutUsView: View {
    @EnvironmentObject var navigator: FlowPathNavigator

    var body: some View {
        VStack(spacing: 0) {
            VStack(spacing: DesignSystem.Spacing.xl) {
                Spacer()
                VStack(spacing: DesignSystem.Spacing.lg) {
                    BrandLogoView.largeIcon(withShadow: true)

                    VStack(spacing: DesignSystem.Spacing.sm) {
                        Text(AppConfig.App.displayName)
                            .font(DesignSystem.Typography.titleLarge)
                            .foregroundColor(DesignSystem.Colors.textPrimary)
                            .fontWeight(.bold)

                        Text("版本 V1.0.0-beta.1")
                            .font(DesignSystem.Typography.body)
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                    }
                }
                Spacer()

                // 版权信息
                VStack(spacing: DesignSystem.Spacing.sm) {
                    Text("© 2025 MoonvyAI Team")
                        .font(DesignSystem.Typography.caption)
                        .foregroundColor(DesignSystem.Colors.textTertiary)

                    Text("保留所有权利")
                        .font(DesignSystem.Typography.caption)
                        .foregroundColor(DesignSystem.Colors.textTertiary)
                }
                .padding(.bottom, DesignSystem.Spacing.xl)
            }
            .padding(.horizontal, DesignSystem.Spacing.lg)
        }
        .frame(maxWidth: .infinity)
        .navigationBarBackButtonHidden()
        .navigationBarTitleDisplayMode(.inline)
        .toolbar {
            ToolbarItem(placement: .topBarLeading) {
                BackButton(onBack: handleBackTap)
            }
            ToolbarItem(placement: .principal) {
                Text("关于我们")
                    .font(.system(size: DesignSystem.FontSize.lg, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.textPrimary)
            }
        }
        .hideNavigationBarLine(backgroundColor: DesignSystem.Colors.backgroundPage)
        .background(DesignSystem.Colors.backgroundPage)
    }

    private func sectionView(title: String, content: String) -> some View {
        VStack(alignment: .leading, spacing: DesignSystem.Spacing.sm) {
            Text(title)
                .font(DesignSystem.Typography.headline)
                .foregroundColor(DesignSystem.Colors.textPrimary)
                .fontWeight(.semibold)

            Text(content)
                .font(DesignSystem.Typography.body)
                .foregroundColor(DesignSystem.Colors.textSecondary)
                .lineSpacing(4)
                .frame(maxWidth: .infinity, alignment: .leading)
        }
        .padding(DesignSystem.Spacing.lg)
        .background(DesignSystem.Colors.backgroundCard)
        .cornerRadius(DesignSystem.Rounded.md)
    }

    private func handleBackTap() {
        navigator.pop()
    }
}
