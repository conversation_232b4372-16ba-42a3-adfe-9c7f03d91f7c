import SwiftUI

// MARK: - Toast 提示组件

/// 轻量级Toast提示组件
/// 支持成功、警告、错误等不同类型的提示
struct ToastView: View {
    let message: String
    let type: ToastType
    let duration: TimeInterval
    @Binding var isShowing: Bool

    enum ToastType {
        case success
        case warning
        case error
        case info

        var icon: String {
            switch self {
            case .success:
                return "checkmark.circle.fill"
            case .warning:
                return "exclamationmark.triangle.fill"
            case .error:
                return "xmark.circle.fill"
            case .info:
                return "info.circle.fill"
            }
        }

        var color: Color {
            switch self {
            case .success:
                return DesignSystem.Colors.success
            case .warning:
                return DesignSystem.Colors.warning
            case .error:
                return DesignSystem.Colors.error
            case .info:
                return DesignSystem.Colors.info
            }
        }
    }

    var body: some View {
        if isShowing {
            HStack(spacing: DesignSystem.Spacing.sm) {
                Image(systemName: type.icon)
                    .foregroundColor(type.color)
                    .font(.system(size: 16, weight: .medium))

                Text(message)
                    .font(DesignSystem.Typography.content)
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                    .multilineTextAlignment(.leading)
            }
            .padding(.horizontal, DesignSystem.Spacing.lg)
            .padding(.vertical, DesignSystem.Spacing.md)
            .background(
                RoundedRectangle(cornerRadius: DesignSystem.Rounded.md)
                    .fill(DesignSystem.Colors.backgroundCard)
                    .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 2)
            )
            .transition(.asymmetric(
                insertion: .move(edge: .top).combined(with: .opacity),
                removal: .move(edge: .top).combined(with: .opacity)
            ))
        }
    }
}

// MARK: - Toast 管理器

/// Toast 提示管理器
/// 用于在全局范围内显示 Toast 提示
@MainActor
class ToastManager: ObservableObject {
    static let shared = ToastManager()

    @Published var isShowing = false
    @Published var message = ""
    @Published var type: ToastView.ToastType = .info
    @Published var duration: TimeInterval = 2.0

    // 延迟隐藏任务管理
    private var hideWorkItem: DispatchWorkItem?

    // 应用生命周期观察者
    private var backgroundObserver: NSObjectProtocol?

    private init() {
        setupLifecycleObservers()
    }

    /// 显示成功提示
    func showSuccess(_ message: String, duration: TimeInterval = 2.0) {
        show(message: message, type: .success, duration: duration)
    }

    /// 显示警告提示
    func showWarning(_ message: String, duration: TimeInterval = 3.0) {
        show(message: message, type: .warning, duration: duration)
    }

    /// 显示错误提示
    func showError(_ message: String, duration: TimeInterval = 3.0) {
        show(message: message, type: .error, duration: duration)
    }

    /// 显示信息提示
    func showInfo(_ message: String, duration: TimeInterval = 2.0) {
        show(message: message, type: .info, duration: duration)
    }

    /// 显示 Toast 提示
    private func show(message: String, type: ToastView.ToastType, duration: TimeInterval) {
        // 取消之前的延迟隐藏任务
        hideWorkItem?.cancel()

        // 如果当前有Toast在显示，直接更新内容
        if isShowing {
            // 直接更新内容，不需要隐藏再显示
            self.message = message
            self.type = type
            self.duration = duration

            // 重新设置延迟隐藏
            scheduleHide(duration: duration)
        } else {
            showToast(message: message, type: type, duration: duration)
        }
    }

    private func showToast(message: String, type: ToastView.ToastType, duration: TimeInterval) {
        self.message = message
        self.type = type
        self.duration = duration

        withAnimation(.easeInOut(duration: 0.3)) {
            isShowing = true
        }

        // 设置延迟隐藏
        scheduleHide(duration: duration)
    }

    /// 统一的延迟隐藏管理方法
    private func scheduleHide(duration: TimeInterval) {
        // 创建新的延迟隐藏任务
        hideWorkItem = DispatchWorkItem { [weak self] in
            withAnimation(.easeInOut(duration: 0.3)) {
                self?.isShowing = false
            }
        }

        // 执行延迟任务
        DispatchQueue.main.asyncAfter(deadline: .now() + duration, execute: hideWorkItem!)
    }

    /// 手动隐藏 Toast（用于应用生命周期管理）
    private func hide() {
        hideWorkItem?.cancel()
        withAnimation(.easeInOut(duration: 0.3)) {
            isShowing = false
        }
    }

    /// 清理Toast状态（用于视图生命周期管理）
    func clearToast() {
        hideWorkItem?.cancel()
        withAnimation(.easeInOut(duration: 0.3)) {
            isShowing = false
        }
    }

    // MARK: - 应用生命周期管理

    /// 设置应用生命周期观察者
    private func setupLifecycleObservers() {
        backgroundObserver = NotificationCenter.default.addObserver(
            forName: UIApplication.didEnterBackgroundNotification,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            MainActor.assumeIsolated {
                self?.handleAppDidEnterBackground()
            }
        }
    }

    /// 应用进入后台时的处理
    private func handleAppDidEnterBackground() {
        // 应用进入后台时隐藏Toast
        hide()
    }

    /// 清理资源
    deinit {
        // 取消延迟任务
        hideWorkItem?.cancel()

        // 移除生命周期观察者
        if let observer = backgroundObserver {
            NotificationCenter.default.removeObserver(observer)
        }
    }
}

// MARK: - Toast 容器视图

/// Toast 容器视图修饰符
/// 用于在视图上层显示 Toast 提示
struct ToastModifier: ViewModifier {
    @StateObject private var toastManager = ToastManager.shared

    func body(content: Content) -> some View {
        content
            .overlay(
                VStack {
                    ToastView(
                        message: toastManager.message,
                        type: toastManager.type,
                        duration: toastManager.duration,
                        isShowing: $toastManager.isShowing
                    )
                    .padding(.horizontal, DesignSystem.Spacing.lg)
                    .padding(.top, DesignSystem.Spacing.md)

                    Spacer()
                }
                .allowsHitTesting(false)
            )
    }
}

// MARK: - View 扩展

extension View {
    /// 添加 Toast 提示功能
    func withToast() -> some View {
        modifier(ToastModifier())
    }
}
