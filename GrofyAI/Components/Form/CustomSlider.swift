import SwiftUI
import UIKit

/// 定义一个可用于滑块值的协议。
/// 它必须是浮点数类型，并且能用于C风格函数（这是为了能方便地转换为Float）。
public protocol SliderValue: BinaryFloatingPoint, CVarArg {}

// 让常用的浮点类型遵守这个协议
extension Double: SliderValue {}
extension Float: SliderValue {}
extension CGFloat: SliderValue {}

class CustomTrackSlider: UISlider {
    
    // 我们需要知道轨道的高度，以便在 trackRect 中进行垂直居中
    var trackHeight: CGFloat = 4.0
    
    /// 重写此方法来提供一个自定义的轨道矩形区域。
    override func trackRect(forBounds bounds: CGRect) -> CGRect {
        // 调用父类的方法获取原始的轨道矩形，作为参考
        let originalRect = super.trackRect(forBounds: bounds)
        
        // 创建一个新的矩形：
        // y: 计算 y 坐标，使其在控件中垂直居中。
        // height: 使用我们自定义的轨道高度。
        // x 和 width: 让它从控件的最左边开始，一直延伸到最右边。
        return CGRect(
            x: originalRect.origin.x,
            y: (bounds.height - trackHeight) / 2.0, // 垂直居中
            width: originalRect.size.width,
            height: trackHeight // 使用自定义高度
        )
    }
}

/// 一个可高度自定义的滑块组件，它封装了 UIKit 的 UISlider。
struct CustomSlider1<V: SliderValue>: UIViewRepresentable {
    
    // MARK: - 属性 (Properties)
    
    /// 滑块的绑定值。它的类型是泛型 V，必须遵守 SliderValue 协议。
    @Binding var value: V
    
    /// 滑块的取值范围。
    var range: ClosedRange<V> = 0...1
    
    /// 滑块的步长。如果为 nil，则滑块平滑移动。
    var step: V? = nil
    
    // --- 外观自定义属性 ---
    
    /// 滑块（Thumb）的自定义图片。
    var thumbImage: UIImage?
    
    /// 滑块的颜色。如果设置，它会覆盖 tint 效果。
    var thumbColor: Color?
    
    /// 滑块左侧（已选区域）轨道的颜色。
    var minTrackColor: UIColor?
    
    /// 滑块右侧（未选区域）轨道的颜色。
    var maxTrackColor: UIColor?
    
    //轨道的厚度，提供一个合理的默认值
    var trackHeight: CGFloat = 4.0
    
    // MARK: - UIViewRepresentable
    
    /// 创建 Coordinator，它是 UIKit 和 SwiftUI 之间的桥梁。
    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }
    
    /// 创建并配置底层的 UISlider。这个方法只会在视图首次创建时调用一次。
    func makeUIView(context: Context) -> UISlider {
        let slider = CustomTrackSlider()
        // 2. 将轨道高度传递给我们的子类，以便 trackRect 方法能使用它
        slider.trackHeight = self.trackHeight
        // --- 进行一次性设置 ---
        
        // 1. 配置范围
        slider.minimumValue = Float(range.lowerBound)
        slider.maximumValue = Float(range.upperBound)
        
        // 2. 配置初始值
        slider.value = Float(value)
        
        // 3. 配置外观
        slider.setThumbImage(thumbImage, for: .normal)
        if let thumbColor = thumbColor { slider.thumbTintColor = UIColor(thumbColor) }
        
        if let minColor = minTrackColor {
            let minTrackImage = createStretchableTrackImage(color: minColor, height: self.trackHeight)
            slider.setMinimumTrackImage(minTrackImage, for: .normal)
        }
        
        if let maxColor = maxTrackColor {
            let maxTrackImage = createStretchableTrackImage(color: maxColor, height: self.trackHeight)
            slider.setMaximumTrackImage(maxTrackImage, for: .normal)
        }
        
        
        if let thumbColor = thumbColor {
            slider.thumbTintColor = UIColor(thumbColor)
        }
        
        // 4. 添加事件监听，当滑块值改变时通知 Coordinator
        slider.addTarget(
            context.coordinator,
            action: #selector(Coordinator.valueChanged(_:)),
            for: .valueChanged
        )
        
        return slider
    }
    
    /// 当 SwiftUI 的状态发生变化时，更新底层的 UISlider。
    /// 这个方法会被频繁调用，因此内部逻辑需要尽可能高效。
    func updateUIView(_ uiView: UISlider, context: Context) {
        // --- 只更新必要的部分以提升性能 ---
        
        // 1. 更新滑块的值（这个会频繁变化）
        // 使用 setValue 并设置 animated: false，以防止 SwiftUI 状态快速更新时滑块出现抖动。
        uiView.setValue(Float(value), animated: false)
        
        // 2. 只有当颜色真的发生变化时才更新，避免不必要的重绘。
        if uiView.minimumTrackTintColor != minTrackColor {
            uiView.minimumTrackTintColor = minTrackColor
        }
        
        if uiView.maximumTrackTintColor != maxTrackColor {
            uiView.maximumTrackTintColor = maxTrackColor
        }
        
        // 将 SwiftUI 的 Color 转换为 UIColor 进行比较
        let newThumbUIColor = thumbColor != nil ? UIColor(thumbColor!) : nil
        if uiView.thumbTintColor != newThumbUIColor {
            uiView.thumbTintColor = newThumbUIColor
        }
        
        // 注意: 如果 thumbImage 也需要动态改变，也可以在这里添加类似的性能优化判断。
        // 对于静态图片，在 makeUIView 中设置一次就足够了。
    }
    
    // MARK: - Coordinator
    
    /// Coordinator 负责处理来自 UISlider 的事件，并更新 SwiftUI 的状态。
    class Coordinator: NSObject {
        var parent: CustomSlider1
        
        init(_ parent: CustomSlider1) {
            self.parent = parent
        }
        
        /// 当 UISlider 的值发生改变时，这个方法会被调用。
        @objc func valueChanged(_ sender: UISlider) {
            // --- 健壮的步进逻辑 ---
            if let step = parent.step {
                let stepAsFloat = Float(step)
                
                // 1. 计算当前值相对于最小值的偏移量，代表了多少“步”。
                let steps = (sender.value - sender.minimumValue) / stepAsFloat
                
                // 2. 将“步数”四舍五入到最近的整数。
                let roundedSteps = round(steps)
                
                // 3. 基于整数步数，重新计算一个精确的目标值。
                // 这种方式可以有效地消除累积的浮点误差。
                let newValue = sender.minimumValue + (roundedSteps * stepAsFloat)
                
                // 4.【至关重要的一步】将计算出的值严格限制在滑块的 [min, max] 范围内。
                // 这可以防止任何微小的计算误差导致值超出边界。
                let clampedValue = max(sender.minimumValue, min(sender.maximumValue, newValue))
                
                // 5. 更新 UI 和绑定值
                sender.setValue(clampedValue, animated: false)
                parent.value = V(clampedValue)
                
            } else {
                // 如果没有步长，则正常更新
                parent.value = V(sender.value)
            }
        }
    }
}

//使用图标 MARK: createThumbImage
func createThumbImage(
    systemName: String,
    pointSize: CGFloat = 25, // 控制图标的大小
    weight: UIImage.SymbolWeight = .regular, // 控制图标的粗细
    scale: UIImage.SymbolScale = .default
) -> UIImage? {
    // 1. 创建一个符号配置对象
    let config = UIImage.SymbolConfiguration(
        pointSize: pointSize,
        weight: weight,
        scale: scale
    )
    
    // 2. 使用系统名称和配置创建 UIImage
    guard let symbolImage = UIImage(systemName: systemName, withConfiguration: config) else {
        return nil
    }
    
    // 3. 设置渲染模式为 .alwaysTemplate
    // 这至关重要！它允许我们通过 tintColor 来改变图标的颜色。
    // 如果不设置，图标将保持其默认颜色（通常是黑色或白色）。
    return symbolImage.withRenderingMode(.alwaysTemplate)
}

func createStretchableTrackImage(color: UIColor, height: CGFloat = 4.0) -> UIImage {
    let rect = CGRect(x: 0, y: 0, width: height, height: height)
    let cornerRadius = height / 2.0
    
    // 使用 UIGraphicsImageRenderer 创建图片，这是现代的绘图方式
    let renderer = UIGraphicsImageRenderer(size: rect.size)
    let image = renderer.image { context in
        let path = UIBezierPath(roundedRect: rect, cornerRadius: cornerRadius)
        color.setFill()
        path.fill()
    }
    
    // 关键步骤：设置端盖，让图片可以从中心拉伸，而两端保持圆角不变。
    // 我们在图片的中心点进行拉伸。
    let capInsets = UIEdgeInsets(top: 0, left: cornerRadius, bottom: 0, right: cornerRadius)
    return image.resizableImage(withCapInsets: capInsets)
}

// MARK: - 便捷的初始化方法
extension CustomSlider1 {
    /// 创建一个自定义滑块。
    /// - Parameters:
    ///   - value: 滑块值的绑定。
    ///   - in: 滑块的取值范围。
    ///   - step: 步长增量。默认为 nil，表示平滑移动。
    ///   - thumbImage: 滑块的自定义图片。
    ///   - thumbColor: 滑块的颜色，会覆盖 `.tint()` 的效果。
    ///   - minTrackColor: 滑块左侧轨道的颜色。
    ///   - maxTrackColor: 滑块右侧轨道的颜色。
    init(value: Binding<V>,
         in range: ClosedRange<V>,
         step: V? = nil,
         thumbImage: UIImage? = nil,
         thumbColor: Color? = nil,
         minTrackColor: UIColor? = .systemBlue, // 提供一个默认颜色
         maxTrackColor: UIColor? = .systemGray4) {
        self._value = value
        self.range = range
        self.step = step
        self.thumbImage = thumbImage
        self.thumbColor = thumbColor
        self.minTrackColor = minTrackColor
        self.maxTrackColor = maxTrackColor
    }
}
