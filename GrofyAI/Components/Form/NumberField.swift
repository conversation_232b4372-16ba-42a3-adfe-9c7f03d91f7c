//
//  NumberField.swift
//  GrofyAI
//
//  Created by kissy on 2025/6/17.
//
import SwiftUI
import Combine
import Foundation

struct NumberField<V: NumericValue>: View {
    
    // MARK: - Properties
    
    let placeholder: String
    @Binding var value: V?
    let range: ClosedRange<V>?
    private let formatter: NumberFormatter
    
    // 当从非可选模式转换而来时，需要一个默认值来处理空的输入
    private let nonOptionalDefaultValue: V?
    private let showRandomButton: Bool
    
    // MARK: - State
    
    @State private var textValue: String
    @FocusState private var isFocused: Bool
    
    // MARK: - Initializer
    
    init(
        placeholder: String = "",
        value: Binding<V?>,
        range: ClosedRange<V>? = nil,
        formatter: NumberFormatter = NumberField.defaultFormatter(forType: V.self),
        nonOptionalDefaultValue: V? = nil,
        showRandomButton: Bool = false
    ) {
        self.placeholder = placeholder
        self._value = value
        self.range = range
        self.formatter = formatter
        self.nonOptionalDefaultValue = nonOptionalDefaultValue
        self.showRandomButton = showRandomButton
        
        // 初始化时，根据外部值设置内部文本状态
        if let initialValue = value.wrappedValue {
            self._textValue = State(initialValue: formatter.string(for: initialValue.doubleValue) ?? "")
        } else {
            self._textValue = State(initialValue: "")
        }
    }
    
    // MARK: - Body
    
    var body: some View {
        HStack{
            TextField(placeholder, text: $textValue)
                .font(.system(size: DesignSystem.FontSize.md))
                .keyboardType(.decimalPad)
                .padding(.horizontal, 10)
                .padding(.vertical, 8)
                .background(DesignSystem.Colors.backgroundInput)
            
                .focused($isFocused)
            // 当外部 `value` 变化时，如果文本框未聚焦，则更新文本
                .onChange(of: value) { newValue in
                    if !isFocused {
                        updateTextFromValue(newValue)
                    }
                }
            // 当焦点状态改变时，提交文本
                .onChange(of: isFocused) { focused in
                    if !focused {
                        commitText()
                    }
                }
            // 按下回车键时，提交文本
                .onSubmit(commitText)
            
            //生成随机数按钮，值在范围内，
            if showRandomButton && range != nil {
                Button(action: generateRandomValue) {
                    Image(systemName: "shuffle.circle.fill") // 使用一个直观的图标
                        .font(.system(size: 24))
                        .foregroundColor(DesignSystem.Colors.textPrimary)
                        .frame(maxHeight:.infinity)
                }
                .buttonStyle(.plain) // 使用 plain 样式以避免默认的蓝色背景
            }
            Spacer()
            
        }
        .background(DesignSystem.Colors.backgroundInput)
        .cornerRadius(8)
    }
    
    // MARK: - Private Methods
    
    /// 生成一个在指定范围内的随机数并更新 `value`。
    private func generateRandomValue() {
        // 安全地解包范围
        guard let range = self.range else { return }
        
        let lowerBound = range.lowerBound.doubleValue
        let upperBound = range.upperBound.doubleValue
        
        let randomValue: V
        
        // 判断类型，为整数或浮点数生成不同的随机值
        if V.self == Int.self {
            let randomInt = Int.random(in: Int(lowerBound)...Int(upperBound))
            randomValue = V(Double(randomInt))
        } else {
            let randomDouble = Double.random(in: lowerBound...upperBound)
            randomValue = V(randomDouble)
        }
        
        // 更新绑定的值，这将自动触发 UI 更新
        self.value = randomValue
    }
    
    /// 从绑定的 `value` 更新 `textValue`。
    private func updateTextFromValue(_ sourceValue: V?) {
        let newText = sourceValue.flatMap { formatter.string(for: $0.doubleValue) } ?? ""
        // 只有在文本确实不同时才更新，防止不必要的视图刷新
        if newText != self.textValue {
            self.textValue = newText
        }
    }
    
    /// 当用户提交输入时（失去焦点或按回车），解析文本并更新绑定的 `value`。
    private func commitText() {
        
        // 这比 Double(textValue) 更健壮，因为它尊重本地化设置（如逗号小数点）
        if textValue.isEmpty {
            // 如果文本为空:
            // - 如果是非可选模式，恢复到默认值
            // - 如果是可选模式，设为 nil
            self.value = nonOptionalDefaultValue
        } else if let number = formatter.number(from: textValue) {
            var finalValue = number.doubleValue
            if let range = range {
                finalValue = min(max(finalValue, range.lowerBound.doubleValue), range.upperBound.doubleValue)
            }
            self.value = V(finalValue)
        }
        
        // 无论解析成功与否，都根据最终的（可能是旧的）`value` 重新格式化文本。
        // - 如果输入有效（"12.3"），它会被格式化（可能还是 "12.3"）。
        // - 如果输入无效（"abc" 或 ""），它会恢复到 `self.value` 所代表的文本。
        // - 如果输入的值被范围限制（输入 1000，范围是 0...100），它会显示 "100"。
        updateTextFromValue(self.value)
    }
    
    /// 根据数值类型提供一个默认的格式化器。
    static func defaultFormatter(forType type: V.Type) -> NumberFormatter {
        let formatter = NumberFormatter()
        formatter.numberStyle = .decimal
        formatter.groupingSeparator = "" // 输入框中通常不显示千位分隔符
        formatter.maximumFractionDigits = (type == Int.self) ? 0 : 2 // 整数没有小数位
        formatter.minimumFractionDigits = 0
        return formatter
    }
}
