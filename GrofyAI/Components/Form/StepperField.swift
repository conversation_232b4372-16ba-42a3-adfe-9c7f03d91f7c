//
//  StepperField.swift
//  GrofyAI
//
//  Created by kissy on 2025/6/17.
//

import SwiftUI

// MARK: - 主组件: StepperField
struct StepperField<Label: View, V: NumericValue>: View {
    
    // MARK: - Properties
    
    /// 左侧显示的标签文本。
    private let label: String?

    private let customLabel: Label?
    
    /// 双向绑定的数值。
    @Binding var value: V
    
    /// 数值的有效范围，用于限制加减和手动输入。
    let range: ClosedRange<V>
    
    /// 每次点击按钮时增减的步长。
    let step: V
    
    /// 用于格式化数字显示的 Formatter。
    private let formatter: NumberFormatter
    
    // MARK: - Initializer
    
    private init(
        customLabel: Label?,
        label: String?,
        value: Binding<V>,
        in range: ClosedRange<V>,
        step: V = V(1.0), // 默认为 1
        formatter: NumberFormatter = NumberField.defaultFormatter(forType: V.self)
    ) {
        self.customLabel = customLabel
        self.label = label
        self._value = value
        self.range = range
        self.step = step
        self.formatter = formatter
    }
    
    // MARK: - Body
    
    var body: some View {
        HStack {
            // 1. 左侧标签
            if let labelView = customLabel {
                labelView
            } else if let labelText = label {
                Text(labelText)
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                    .font(.system(size: 14))
            }
            
            Spacer()
            
            // 2. 右侧步进器控件
            HStack(spacing: 0) {
                // 减号按钮
                Button(action: decrement) {
                    Image(systemName: "minus")
                        .contentShape(Rectangle()) // 增大点击区域
                }
                .disabled(value <= range.lowerBound) // 当值达到下限时禁用
                .stepperButtonStyle()
                
                // 中间的可编辑数字输入框
                NumberField(
                    value: $value.toOptional(),
                    range: range,
                    formatter: formatter
                )
                .multilineTextAlignment(.center)
                .frame(minWidth: 40, maxWidth: 60) // 给一个合适的宽度
                
                // 加号按钮
                Button(action: increment) {
                    Image(systemName: "plus")
                        .contentShape(Rectangle()) // 增大点击区域
                }
                .disabled(value >= range.upperBound) // 当值达到上限时禁用
                .stepperButtonStyle()
            }
            .background(Color(.systemGray6))
            .cornerRadius(8)
        }
    }
    
    // MARK: - Private Methods
    
    /// 执行减法操作
    private func decrement() {
        let newValue = value.doubleValue - step.doubleValue
        // 确保结果不会低于范围下限
        let clampedValue = max(newValue, range.lowerBound.doubleValue)
        self.value = V(clampedValue)
    }
    
    /// 执行加法操作
    private func increment() {
        let newValue = value.doubleValue + step.doubleValue
        // 确保结果不会高于范围上限
        let clampedValue = min(newValue, range.upperBound.doubleValue)
        self.value = V(clampedValue)
    }
}

// MARK: - 辅助的视图修饰器，用于统一按钮样式
private struct StepperButtonStyle: ViewModifier {
    func body(content: Content) -> some View {
        content
            .font(.system(size: 14, weight: .bold))
            .foregroundColor(.primary)
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
    }
}

private extension View {
    func stepperButtonStyle() -> some View {
        self.modifier(StepperButtonStyle())
    }
}


extension StepperField {
    
    /// **构造器 1 (高优先级): 用于自定义 View 标签。**
    /// 如果你提供了自定义视图，它将被优先显示。
    init(
        value: Binding<V>,
        in range: ClosedRange<V>,
        step: V = V(1.0),
        formatter: NumberFormatter = NumberField.defaultFormatter(forType: V.self),
        @ViewBuilder customLabel: () -> Label
    ) {
        self.init(
            customLabel: customLabel(), // 存储自定义 View
            label: nil,     // 忽略 String
            value: value,
            in: range,
            step: step,
            formatter: formatter
        )
    }
    
    /// **构造器 2 (低优先级): 仅用于 String 标签。**
    /// 这个构造器只在没有提供自定义 View 时使用。
    /// `where Label == EmptyView` 是一个技巧，确保这个 init 不会与上面的冲突。
    init(
        label: String,
        value: Binding<V>,
        in range: ClosedRange<V>,
        step: V = V(1.0),
        formatter: NumberFormatter = NumberField.defaultFormatter(forType: V.self)
    ) where Label == EmptyView { // <-- 关键技巧
        self.init(
            customLabel: nil,     // 没有自定义 View
            label: label,  // 存储 String
            value: value,
            in: range,
            step: step,
            formatter: formatter
        )
    }
    
    init(
        label: String,
        value: Binding<V>,
        in range: ClosedRange<V>,
        step: V = V(1.0),
        formatter: NumberFormatter = NumberField.defaultFormatter(forType: V.self),
        @ViewBuilder customLabel: () -> Label
    ) { // <-- 关键技巧
        self.init(
            customLabel: customLabel(), // 存储自定义 View
            label: nil,     // 忽略 String
            value: value,
            in: range,
            step: step,
            formatter: formatter
        )
    }
}
