//
//  RichTextEditor.swift
//  GrofyAI
//
//  Created by kissy on 2025/6/13.
//

import SwiftUI

struct RichTextEditor: View {
    
    // 占位符文字
    let placeholder: String
    // 通过 @Binding 与父视图双向绑定文本
    @Binding var text: String
    // 最大字符数
    let maxCount: Int
    
    // 用于解决空 TextEditor 光标过大问题的内部状态
    @State private var internalText: String
    
    // 定义一个零宽度空格常量，方便使用
    private let zeroWidthSpace = "\u{200B}"
    
    // 自定义初始化方法，让调用更简洁
    init(placeholder: String = "请输入内容...", text: Binding<String>, maxCount: Int = 1000) {
        self.placeholder = placeholder
        self._text = text // 注意 @Binding 属性的赋值方式
        self.maxCount = maxCount
        
        // 初始化内部 aText，如果外部 text 为空，则使用零宽度空格
        // _internalText 是 @State 属性包装器的写法
        _internalText = State(initialValue: text.wrappedValue.isEmpty ? zeroWidthSpace : text.wrappedValue)
        
        
        // 明确设置默认输入属性，确保初始光标大小正确
               // 这会告诉空的 TextView，光标应该按 14号字体的高度显示
    }
    
    var body: some View {
        ZStack(alignment: .topLeading) {
            // 主要的 TextEditor
            TextEditor(text: $internalText)
//            移除 TextEditor 默认的背景色，以便我们的背景色生效
                .scrollContentBackground(.hidden)
                .background(.clear)
                .foregroundColor(DesignSystem.Colors.textPrimary)
                .font(.system(size: 14))
                .lineSpacing(5)
            // 内部留出一些空间，避免文字紧贴边框
                .padding(.all, 8)
            // 为底部的计数器和顶部的清除按钮留出额外空间
                .padding(.bottom, 30)
                .onChange(of: internalText) { newValue in
                    // 当内部文本变化时（用户输入），更新外部的真实文本
                    // 将零宽度空格过滤掉，确保外部获取的是纯净的文本
                    text = newValue.replacingOccurrences(of: zeroWidthSpace, with: "")
                }
                .onChange(of: text) { newValue in
                    // 当外部文本变化时（例如被程序性地清空），更新内部文本
                    if newValue.isEmpty && internalText != zeroWidthSpace {
                        // 如果外部变空，且内部不是ZWS，则设置为ZWS来修复光标
                        internalText = zeroWidthSpace
                    } else if !newValue.isEmpty && internalText != newValue {
                        // 否则正常同步
                        internalText = newValue
                    }
                }
                
            
            // 当文本为空时，显示占位符
            if text.isEmpty {
                Text(placeholder)
                    .font(.system(size: 14))
                    .foregroundColor(DesignSystem.Colors.textHint)
                // 确保占位符与 TextEditor 文字位置对齐
                    .padding(.all, 15)
                // 让点击事件可以穿透到下面的 TextEditor
                    .allowsHitTesting(false)
            }
            
            
            // 右下角的字符计数器和删除按钮
            VStack {
                Spacer() // 将 HStack 推到底部
                HStack(alignment: .center, spacing: 8) { // alignment: .center 确保内部元素垂直居中, spacing 增加元素间距
                    Spacer() // 将所有内容推到右侧
                    
                    // 1. 字符计数器 (移除了 .padding(.bottom))
                    Text("\(text.count)/\(maxCount)")
                        .font(.system(size: 12))
                        .foregroundColor(.gray)
                    
                    // 2. 垂直分割线
                    Rectangle()
                        .frame(width: 1, height: 14) // 使用固定宽度和高度
                        .foregroundColor(Color.gray.opacity(0.5)) // 颜色调淡一些
                    
                    // 3. 删除按钮 (移除了 .padding(.top))
                    // 仅在有文本时显示
                    //                    if !text.isEmpty {
                    Button(action: {
                        self.text = ""
                    }) {
                        Image(systemName: "trash")
                            .font(.system(size: 14)) // 调整大小以匹配
                            .foregroundColor(.gray)
                    }
                    //                    }
                }
                // 将 Padding 应用于整个 HStack，使其与边框保持距离
                .padding(.horizontal, 12)
                .padding(.bottom, 10)
            }
            .background(.clear)
        }
        .frame(height: 150) // 可以根据需要调整高度
        .background(DesignSystem.Colors.backgroundCard)
        .cornerRadius(12)
    }
}
