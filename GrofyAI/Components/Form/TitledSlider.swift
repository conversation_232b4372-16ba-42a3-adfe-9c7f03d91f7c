//
//  TitledSlider.swift
//  GrofyAI
//
//  Created by kissy on 2025/6/16.
//
import SwiftUI
import Foundation


struct TitledSliderWithInfo<V: NumericValue>: View {

    let title: String
    @Binding var value: V
    let range: ClosedRange<V>
    let step: V

    let infoTitle: String?
    let infoMessage: String?
    
    // MARK: - Internal State
    @State private var isShowingInfoAlert = false

    // 更新初始化方法，提供 nil作为默认值 ---
    init(
        title: String,
        value: Binding<V>,
        in range: ClosedRange<V>,
        step: V = V(1.0),
        infoTitle: String? = nil, // 默认值为 nil
        infoMessage: String? = nil // 默认值为 nil
    ) {
        self.title = title
        self._value = value
        self.range = range
        self.step = step
        self.infoTitle = infoTitle
        self.infoMessage = infoMessage
    }

    var body: some View {
        //  根据 infoMessage 是否存在来决定是否传递 onInfoTapped 闭包 ---
        TitledSlider(
            title: title,
            value: $value,
            in: range,
            step: step,
            // 如果 infoMessage 不为 nil，则提供一个闭包来触发弹窗
            // 如果为 nil，则传递 nil，TitledSlider 将不会显示信息按钮
            onInfoTapped: infoMessage != nil ? {
                self.isShowingInfoAlert = true
            } : nil
        )
        //  ---  使用 ?? 提供默认值，确保 alert 不会因 nil 而崩溃 ---
        .alert(infoTitle ?? "信息", isPresented: $isShowingInfoAlert) {
            Button("知道了", role: .cancel) {}
        } message: {
            Text(infoMessage ?? "")
        }
    }
}


/// 一个带有标题、可输入文本框和可选信息按钮的自定义滑块组件。
/// 它可以处理任何遵守 SlidableValue 协议的数字类型 (如 Int, Double)。
struct TitledSlider<V: NumericValue>: View {
    
    // MARK: - Properties
    
    let title: String
    @Binding var value: V // <-- 泛型值
    let range: ClosedRange<V> // <-- 泛型范围
    let step: V // <-- 泛型步长
    let onInfoTapped: (() -> Void)?
    
    // MARK: - State
    
    @State private var textValue: String = ""
    @FocusState private var isTextFieldFocused: Bool
    
    // MARK: - Initializer
    
    init(
        title: String,
        value: Binding<V>,
        in range: ClosedRange<V>,
        step: V = V(1.0), // 默认步长为 1
        onInfoTapped: (() -> Void)? = nil
    ) {
        self.title = title
        self._value = value
        self.range = range
        self.step = step
        self.onInfoTapped = onInfoTapped
    }
    
    // MARK: - Body
    
    var body: some View {
        HStack(spacing: 12) {
            // MARK: 标题和信息按钮
            HStack(spacing: 4) {
                Text(title)
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                    .font(.system(size: 14, weight: .medium))
                
                // 如果提供了回调，则显示信息按钮
                if let onInfoTapped = onInfoTapped {
                    Button(action: onInfoTapped) {
                        Image(systemName: "questionmark.circle")
                            .font(.system(size:DesignSystem.FontSize.md))
                            .foregroundColor(.gray.opacity(0.8))
                    }
                    .buttonStyle(.plain)
                }
            }
//            .frame(width: 80, alignment: .leading)
            
            // MARK: 滑块
            //            Slider(
            //                value: internalValueBinding,
            //                in: range.lowerBound.doubleValue...range.upperBound.doubleValue,
            //                step: step.doubleValue
            //            )
            //            .frame(height: 50) // 可以控制整个控件的高度
            CustomSlider(
                value: internalValueBinding, // 使用转换后的 Double 绑定
                range: range.lowerBound.doubleValue...range.upperBound.doubleValue,
                step: step.doubleValue
            )
            
            // MARK: 可输入的文本框
            TextField("", text: $textValue)
                .frame(width: 50, height: 35)
                .multilineTextAlignment(.center)
                .keyboardType(.decimalPad) // 使用数字键盘
                .background(Color(.systemGray6))
                .cornerRadius(8)
                .focused($isTextFieldFocused)
                .onSubmit(commitTextValue) // 用户按下回车时提交
        }
        //        .onAppear(updateTextValue)  视图出现时，初始化文本框内容
        .onAppear{updateTextValue()}
        .onChange(of: value) { _ in updateTextValue() } // 当外部值变化时，更新文本框
        .onChange(of: isTextFieldFocused) { isFocused in
            // 当 isFocused 从 true 变为 false 时，此条件成立。
            if !isFocused {
                // 此时 TextField 失去了焦点，我们提交值。
                commitTextValue()
            }
        }
    }
    
    
   
    
    // MARK: - Private Methods
    
    /// 创建一个临时的 Double 类型绑定，供 Slider 内部使用。
    private var internalValueBinding: Binding<Double> {
        Binding<Double>(
            get: { self.value.doubleValue }, // 读取时，将 V -> Double
            set: { self.value = V($0) }       // 写入时，将 Double -> V
        )
    }
    
    /// 根据 value 更新文本框的显示
    private func updateTextValue() {
        if V.self == Int.self {
            // 如果是整数类型，不显示小数点
            self.textValue = String(format: "%.0f", self.value.doubleValue)
        } else {
            // 如果是浮点数，可以显示小数点（这里显示两位）
            self.textValue = String(format: "%.1f", self.value.doubleValue)
        }
    }
    
    /// 当用户在文本框中提交时，更新 value
    private func commitTextValue() {
        if let newDoubleValue = Double(textValue) {
            // 确保新值在有效范围内
            let clampedValue = min(max(newDoubleValue, range.lowerBound.doubleValue), range.upperBound.doubleValue)
            self.value = V(clampedValue)
        }
        // 无论转换成功与否，都根据最终的 value 值重新格式化文本框，确保显示一致性
        updateTextValue()
    }
}


struct CustomSlider: View {
    // MARK: Properties
    @Binding var value: Double
    let range: ClosedRange<Double>
    let step: Double
    
    // MARK: View Configuration
    private let trackHeight: CGFloat = 6
    private let thumbSize: CGFloat = 24
    
    // MARK: Body
    var body: some View {
        GeometryReader { geometry in
            // 使用 ZStack 将轨道和滑块按钮叠在一起
            HStack{
                ZStack(alignment: .leading) {
                    
                    // 1. 背景轨道
                    Capsule()
                        .fill(DesignSystem.Colors.backgroundInput)
                        .frame(height: trackHeight)
                    
                    // 2. 蓝色进度条
                    Capsule()
                        .fill(DesignSystem.Colors.primary) // 图片中的蓝色
                        .frame(width: progressWidth(in: geometry.size.width), height: trackHeight)
                    
                    // 3. 滑块按钮 (Thumb)
                    Circle()
                        .fill(.white)
                        .frame(width: thumbSize, height: thumbSize)
                        .shadow(color: .black.opacity(0.1), radius: 5, y: 2) // 添加阴影
                        .offset(x: thumbOffset(in: geometry.size.width))
                }
            }
            .frame(maxHeight:.infinity) // 确保 ZStack 有足够的高度容纳按钮
            .gesture(dragGesture(in: geometry.size.width)) // 添加拖动手势
        }
    }
    
    // MARK: - Helper Methods
    
    /// 计算滑块按钮的水平偏移量
    private func thumbOffset(in totalWidth: CGFloat) -> CGFloat {
        // 计算当前值在范围内的比例 (0.0 to 1.0)
        let ratio = (value - range.lowerBound) / (range.upperBound - range.lowerBound)
        // 乘以可用的滑动宽度（总宽度 - 按钮宽度），确保按钮不会超出边界
        return CGFloat(ratio) * (totalWidth - thumbSize)
    }
    
    /// 计算蓝色进度条的宽度
    private func progressWidth(in totalWidth: CGFloat) -> CGFloat {
        // 进度条的宽度等于按钮的偏移量加上按钮半径，使其末端在按钮中心
        return thumbOffset(in: totalWidth) + (thumbSize / 2)
    }
    
    /// 创建拖动手势
    private func dragGesture(in totalWidth: CGFloat) -> some Gesture {
        DragGesture(minimumDistance: 0)
            .onChanged { gestureValue in
                // 可用滑动宽度
                let availableWidth = totalWidth - thumbSize
                // 计算拖动位置在可用宽度上的比例
                let ratio = max(0, min(1, gestureValue.location.x / availableWidth))
                
                // 将比例转换回实际值
                var newValue = range.lowerBound + Double(ratio) * (range.upperBound - range.lowerBound)
                
                // 应用步长 (Stepping)
                if step > 0 {
                    newValue = round(newValue / step) * step
                }
                
                // 确保值在范围内并更新
                self.value = min(max(range.lowerBound, newValue), range.upperBound)
            }
    }
}
