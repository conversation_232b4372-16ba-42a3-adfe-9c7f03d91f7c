//
//  DropDownCoordinator.swift
//  GrofyAI
//
//  Created by kissy on 2025/6/27.
//

import SwiftUI
import Combine // 需要导入 Combine

// 所有 DropDownPicker 的中央协调器
class DropDownCoordinator: ObservableObject {
    // 使用 @Published，当 activePickerID 变化时，所有观察者都会收到通知
    @Published var activePickerID: UUID? = nil
    
    // 当一个 Picker 想打开时，它会调用这个方法
    func open(picker id: UUID) {
        activePickerID = id
    }
    
    // 当一个 Picker 关闭时，它会调用这个方法
    func close() {
        activePickerID = nil
    }
}



// 定义一个键，用于在 SwiftUI 环境中存储和访问我们的协调器
private struct DropDownCoordinatorKey: EnvironmentKey {
    // 默认值是一个新的协调器实例
    static let defaultValue = DropDownCoordinator()
}

// 扩展 EnvironmentValues，使其能够方便地访问我们的协调器
extension EnvironmentValues {
    var dropDownCoordinator: DropDownCoordinator {
        get { self[DropDownCoordinatorKey.self] }
        set { self[DropDownCoordinatorKey.self] = newValue }
    }
}
