//
//  DropDownPickerView.swift
//  GrofyAI
//
//  Created by kissy on 2025/6/27.
//
import SwiftUI


struct StatefulDropDownPicker<Value: Identifiable & Hashable, Label: View>: View {
    
    // MARK: - 透传的属性
    
    let placeholder: String
    let options: [Value]
    @Binding var selection: Value?
    @ViewBuilder let content: (Value) -> Label
    
    // MARK: - 内部状态管理
    
    /// 从环境中读取共享的协调器
    @Environment(\.dropDownCoordinator) private var coordinator
    
    /// 每个实例都有一个唯一的、稳定的 ID
    @State private var pickerID = UUID()
    
    /// isExpanded 不再是 State，而是通过协调器计算得出
    private var isExpanded: Bool {
        coordinator.activePickerID == pickerID
    }
    
    var body: some View {
        // 使用 ZStack 来实现点击外部关闭的功能
        DropDownPicker(
            placeholder: placeholder,
            options: options,
            selection: $selection,
            // 关键：创建一个自定义的 Binding 来与协调器交互
            isExpanded: Binding<Bool>(
                get: { isExpanded },
                set: { shouldExpand in
                    if shouldExpand {
                        // 告诉协调器“我要展开”
                        coordinator.open(picker: pickerID)
                    } else {
                        // 告诉协调器“我关闭了”（或者说，没有活动的了）
                        coordinator.close()
                    }
                }
            ),
            content: content
        )
    }
}
