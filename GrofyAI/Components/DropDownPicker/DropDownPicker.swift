//
//  DropDownPicker.swift
//  GrofyAI
//
//  Created by kissy on 2025/6/16.
//
import SwiftUI
/// - Parameters:
///   - Value: 选项的数据类型，必须遵守 `Identifiable` 和 `Hashable` 协议。
///   - Label: 用于构建每个选项视图的视图类型。
struct DropDownPicker<Value: Identifiable & Hashable, Label: View>: View {
    
    // MARK: - Properties
    
    let placeholder: String
    let options: [Value]
    @Binding var selection: Value?
    
    /// 修改为 @Binding，允许父视图控制展开状态，以实现点击外部关闭的功能
    @Binding var isExpanded: Bool
    
    @ViewBuilder let content: (Value) -> Label
    
    // MARK: - State
    
    /// 用于存储按钮的尺寸，以便让下拉列表的宽度与之匹配
    @State private var buttonSize: CGSize = .zero
    
    // MARK: - Body
    
    var body: some View {
        Button(action: {
            withAnimation(.spring(response: 0.3, dampingFraction: 0.8, blendDuration: 0)) {
                isExpanded.toggle()
            }
        }) {
            
            VStack{
                HStack {
                    if let selection = selection {
                        content(selection)
                            
                    } else {
                        Text(placeholder)
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                            .font(.system(size: 14))
                        
                    }
                    Spacer()
                    Image(systemName: "chevron.down")
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.secondary)
                        .rotationEffect(.degrees(isExpanded ? 180 : 0))
                }
                .padding(.horizontal,10)
                .frame(height: 40)
            }
            .background(backgroundView)
            .cornerRadius(12)
            // 使用 background + GeometryReader 读取尺寸，这种方式不会影响布局
            .background(
                GeometryReader { proxy in
                    Color.clear.onAppear {
                        self.buttonSize = proxy.size
                    }
                }
            )
        }
        
        .buttonStyle(.plain)
        .overlay(alignment: .topLeading) {
            // MARK: 悬浮的下拉列表
            if isExpanded {
                // 定义行高和最大高度
                let rowHeight: CGFloat = 40
                let maxHeight: CGFloat = 140
                
                // 计算内容所需的总高度 (这是一个近似值)
                let contentHeight = CGFloat(options.count) * rowHeight
                
                // 决定最终的列表高度
                let finalHeight = min(contentHeight, maxHeight)
                
                ScrollView {
                    VStack(alignment: .leading, spacing: 0) {
                        ForEach(options) { option in
                            content(option)
                                .padding(.horizontal,10)
                                .frame(height: 40)
                                .contentShape(Rectangle())
                                .onTapGesture {
                                    select(option: option)
                                }
                            
                            if options.last != option {
                                Divider().padding(.horizontal, 8)
                                    .background(DesignSystem.Colors.textPrimary)
                            }
                        }
                    }
                    
                }
                .scrollIndicators(.visible, axes: .vertical)
                .frame(width: buttonSize.width,height: finalHeight) // 宽度与按钮保持一致
                .background(backgroundView)
                .cornerRadius(12)
                .shadow(color: Color.black.opacity(0.1), radius: 8, x: 0, y: 4)
                .transition(.asymmetric(
                    insertion: .opacity.combined(with: .offset(y: -5)),
                    removal: .opacity.combined(with: .offset(y: -5))
                ))
                // 将列表向下偏移，使其刚好在按钮下方，并留出一点间隙
                .offset(y: buttonSize.height + 8)
            }
        }
        // zIndex 确保 overlay 在 ZStack 中位于最上层
        .zIndex(99)
    }
    
    
    
    
    private var backgroundView: some View {
        Color(.systemBackground)
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(Color.gray.opacity(0.2), lineWidth: 2)
            )
    }
    
    private func select(option: Value) {
        withAnimation(.spring(response: 0.3, dampingFraction: 0.8, blendDuration: 0)) {
            selection = option
            isExpanded = false
        }
    }
}


struct ModelOptionCell: View {
    @Environment(\.colorScheme) private var colorScheme
    
    let icon: String
    let icon_darkTheme: String
    let describe: String
    let displayName: String
    
    
    var body: some View {
        HStack(spacing: 5) {
            VStack{
                if colorScheme == .dark, icon_darkTheme.hasSuffix("__customWhite") {
                    Image(icon) // <-- 确保这里的名字和你在 Assets 中设置的一致
                        .resizable()
                        .renderingMode(.template)
                        .foregroundColor(.white)
                        .aspectRatio(contentMode: .fill)
                        .frame(width: 20, height: 20)
                } else {
                    Image(icon) // <-- 确保这里的名字和你在 Assets 中设置的一致
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                        .frame(width: 20, height: 20)
                }
            }
            .padding(3) // 1. 向内添加边距，给背景留出空间
            .background(DesignSystem.Colors.iconBackground) // 2. 设置背景颜色
            .clipShape(Circle()) // 3. 将背景裁剪成圆形
            
            
            VStack(alignment: .leading){
                Text(displayName)
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                
                Text(describe)
                    .font(.system(size: 12))
                    .foregroundStyle(DesignSystem.Colors.textSecondary)
            }
            
            
            Spacer()
        }
        .padding(.vertical,5)
    }
}
