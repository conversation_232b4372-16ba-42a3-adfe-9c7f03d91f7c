import MijickPopups
import SwiftUI
import WebKit

// MARK: - WebView 弹窗

struct WebViewPopup: CenterPopup {
    let url: URL
    let title: String

    init(url: URL, title: String) {
        self.url = url
        self.title = title
    }

    var body: some View {
        createContent()
    }

    func createContent() -> some View {
        let screenWidth = UIScreen.main.bounds.width
        let screenHeight = UIScreen.main.bounds.height
        let popupWidth = screenWidth * 0.9
        let popupHeight = screenHeight * 0.8

        return VStack(spacing: 0) {
            navigationBar

            Divider()
                .background(DesignSystem.Colors.border)

            WebViewContainer(url: url)
                .frame(height: popupHeight - 60)
                .background(Color.white)
        }
        .frame(width: popupWidth, height: popupHeight)
        .background(DesignSystem.Colors.backgroundCard)
        .clipShape(RoundedRectangle(cornerRadius: DesignSystem.Rounded.lg))
        .shadow(color: Color.black.opacity(0.1), radius: 10)
    }

    private var navigationBar: some View {
        HStack {
            Button(action: {
                Task {
                    await dismissLastPopup()
                }
            }) {
                Image(systemName: "xmark")
                    .font(.system(size: DesignSystem.FontSize.lg, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                    .frame(width: 30, height: 30)
                    .background(
                        Circle()
                            .fill(DesignSystem.Colors.backgroundInput)
                    )
            }

            Spacer()

            Text(title)
                .font(.system(size: 17, weight: .semibold))
                .foregroundColor(DesignSystem.Colors.textPrimary)

            Spacer()

            Color.clear
                .frame(width: 30, height: 30)
        }
        .padding(.horizontal, DesignSystem.Spacing.md)
        .frame(height: 60)
        .background(DesignSystem.Colors.backgroundCard)
    }
}

// MARK: - WebView Container

struct WebViewContainer: UIViewRepresentable {
    let url: URL

    func makeUIView(context: Context) -> WKWebView {
        let configuration = WKWebViewConfiguration()
        let webView = WKWebView(frame: .zero, configuration: configuration)
        webView.navigationDelegate = context.coordinator

        let request = URLRequest(url: url)
        webView.load(request)

        return webView
    }

    func updateUIView(_ webView: WKWebView, context: Context) {
        // No updates needed
    }

    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }

    class Coordinator: NSObject, WKNavigationDelegate {
        var parent: WebViewContainer

        init(_ parent: WebViewContainer) {
            self.parent = parent
        }

        func webView(_ webView: WKWebView, didStartProvisionalNavigation navigation: WKNavigation!) {
            // 开始加载
        }

        func webView(_ webView: WKWebView, didFinish navigation: WKNavigation!) {
            // 加载完成
        }

        func webView(_ webView: WKWebView, didFail navigation: WKNavigation!, withError error: Error) {
            // 加载失败
            print("WebView 加载失败: \(error.localizedDescription)")
        }
    }
}
