import Kingfisher
import MijickPopups
import SwiftUI

// MARK: - LLM模型选择弹窗

struct LLMModelSelectionPopup: BottomPopup, View {
    @ObservedObject private var modelManager = ModelManager.shared

    var body: some View {
        createContent()
    }

    func createContent() -> some View {
        let screenHeight = UIScreen.main.bounds.height
        let popupHeight = screenHeight * 0.8
        let headerHeight: CGFloat = 60
        let contentHeight = popupHeight - headerHeight

        return VStack(spacing: 0) {
            popupHeader

            Group {
                if modelManager.isLoading {
                    loadingView
                } else if modelManager.availableModels.isEmpty {
                    emptyView
                } else {
                    modelListView
                }
            }
            .frame(height: contentHeight)
        }
        .frame(maxWidth: .infinity)
        .frame(height: popupHeight)
        .background(DesignSystem.Colors.backgroundCard)
        .clipShape(RoundedRectangle(cornerRadius: DesignSystem.Rounded.lg))
    }
}

// MARK: - 子视图组件

extension LLMModelSelectionPopup {
    private var popupHeader: some View {
        VStack(spacing: DesignSystem.Spacing.md) {
            HStack {
                Text("模型列表")
                    .font(.system(size: DesignSystem.FontSize.lg))

                Spacer()

                Button(action: {
                    Task {
                        await dismissLastPopup()
                    }
                }) {
                    Image(systemName: "xmark")
                        .font(.system(size: DesignSystem.FontSize.lg, weight: .medium))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                }
            }
            .padding(.horizontal, DesignSystem.Spacing.lg)

            Rectangle()
                .fill(DesignSystem.Colors.separator)
                .frame(height: DesignSystem.BorderWidth.thin)
                .padding(.horizontal, DesignSystem.Spacing.xl)
        }
        .background(DesignSystem.Colors.backgroundCard)
    }

    private var loadingView: some View {
        VStack(spacing: 16) {
            ProgressView()
                .scaleEffect(1.2)

            Text("正在加载模型列表...")
                .font(.system(size: 14))
                .foregroundColor(DesignSystem.Colors.textSecondary)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }

    private var emptyView: some View {
        EmptyStateView(
            title: "暂无可用模型",
            description: "请检查网络连接或稍后重试",
            iconColor: .orange,
            actionButton: ActionButtonConfig(title: "重新加载") {
                Task {
                    await modelManager.loadModels(force: true)
                }
            },
            style: .standard
        )
    }

    private var modelListView: some View {
        ScrollView(.vertical, showsIndicators: true) {
            LazyVStack(spacing: DesignSystem.Spacing.lg) {
                ForEach(groupedModels, id: \.level) { group in
                    ModelGroupView(
                        level: group.level,
                        models: group.models,
                        selectedModel: modelManager.selectedModel,
                        onModelSelected: { model in
                            modelManager.selectModel(model)
                            Task {
                                await dismissLastPopup()
                            }
                        }
                    )
                }
            }
            .padding(.vertical, DesignSystem.Spacing.md)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }

    /// 按级别分组的模型数据
    private var groupedModels: [(level: String, models: [LLMListRes])] {
        let grouped = Dictionary(grouping: modelManager.availableModels) { model in
            model.safeLevel.displayName
        }

        // 按指定顺序排序分组
        let order = ["专家", "高级", "基础", "更多"]
        return order.compactMap { levelName in
            if let models = grouped[levelName], !models.isEmpty {
                return (level: levelName, models: models)
            }
            return nil
        }
    }
}

// MARK: - 模型分组视图

private struct ModelGroupView: View {
    let level: String
    let models: [LLMListRes]
    let selectedModel: LLMListRes?
    let onModelSelected: (LLMListRes) -> Void

    var body: some View {
        VStack(alignment: .leading, spacing: DesignSystem.Spacing.md) {
            HStack {
                VStack(spacing: DesignSystem.Spacing.xs) {
                    Text(level)
                        .font(DesignSystem.Typography.content)

                    RoundedRectangle(cornerRadius: 2)
                        .frame(maxWidth: 20, maxHeight: 2)
                        .foregroundColor(DesignSystem.Colors.primary)
                }
                Spacer()
            }
            .padding(.horizontal, DesignSystem.Spacing.lg)

            VStack(spacing: DesignSystem.Spacing.md) {
                ForEach(models, id: \.id) { model in
                    ModelCardView(
                        model: model,
                        isSelected: selectedModel?.id == model.id,
                        onTap: {
                            onModelSelected(model)
                        }
                    )
                    .padding(.horizontal, DesignSystem.Spacing.lg)
                }
            }
        }
    }
}

// MARK: - 模型卡片视图

private struct ModelCardView: View {
    let model: LLMListRes
    let isSelected: Bool
    let onTap: () -> Void

    var body: some View {
        Button(action: onTap) {
            HStack(spacing: DesignSystem.Spacing.sm) {
                ModelIconView(iconURL: model.icon, provider: model.provider, size: 30)

                VStack(alignment: .leading, spacing: DesignSystem.Spacing.xs) {
                    HStack(alignment: .firstTextBaseline, spacing: DesignSystem.Spacing.xs) {
                        HStack(spacing: DesignSystem.Spacing.sm) {
                            Text(model.safeName)
                                .font(.system(size: DesignSystem.FontSize.lg, weight: .medium))
                                .foregroundColor(isSelected ? .white : DesignSystem.Colors.textPrimary)
                                .lineLimit(1)

                            HStack(spacing: 6) {
                                ForEach(getCapabilityIcons(for: model), id: \.self) { iconName in
                                    Image(iconName)
                                        .resizable()
                                        .frame(width: 14, height: 14)
                                        .foregroundStyle(isSelected ? Color.white.opacity(0.8) : DesignSystem.Colors
                                            .textSecondary
                                        )
                                }
                            }
                        }

                        Spacer()

                        if let cost = model.cost {
                            HStack(spacing: 6) {
                                Text("\(cost)")
                                    .font(.system(size: 14, weight: .medium))
                                    .foregroundColor(isSelected ? Color.white.opacity(0.8) : DesignSystem.Colors
                                        .textSecondary
                                    )

                                Image("IconStarPoints")
                                    .resizable()
                                    .frame(width: 14, height: 14)
                            }
                        }
                    }

                    HStack(alignment: .firstTextBaseline) {
                        Text(model.safeSummary)
                            .font(.system(size: 12))
                            .foregroundColor(DesignSystem.Colors.textTertiary)
                            .lineLimit(2)

                        Spacer()

                        if let releaseDate = model.releaseDate {
                            Text(releaseDate)
                                .font(.system(size: 12))
                                .foregroundColor(DesignSystem.Colors.textTertiary)
                                .frame(minWidth: 60, alignment: .trailing)
                        }
                    }
                }
            }
            .padding(.horizontal, DesignSystem.Spacing.lg)
            .padding(.vertical, DesignSystem.Spacing.md)
            .background(
                isSelected ? DesignSystem.Colors.primary : DesignSystem.Colors.backgroundInput
            )
            .cornerRadius(DesignSystem.Rounded.md)
            .contentShape(Rectangle())
        }
        .buttonStyle(.plain)
    }

    private func getCapabilityIcons(for model: LLMListRes) -> [String] {
        var icons: [String] = []

        if let inputFormat = model.inputFormat {
            if inputFormat.safeText {
                icons.append("IconText")
            }
            if inputFormat.safeImage {
                icons.append("IconImage")
            }
            if inputFormat.safeAudio {
                icons.append("IconAudio")
            }
        }

        return icons
    }
}
