import MijickPopups
import SwiftUI

// MARK: - 功能选择弹窗

struct FeatureSelectionPopup: BottomPopup, View {
    let onFeatureSelected: ((FeatureCardItem) -> Void)?

    init(onFeatureSelected: ((FeatureCardItem) -> Void)? = nil) {
        self.onFeatureSelected = onFeatureSelected
    }

    var body: some View {
        createContent()
    }

    func createContent() -> some View {
        let screenHeight = UIScreen.main.bounds.height
        let popupHeight = screenHeight * 0.6
        let headerHeight: CGFloat = 80
        let contentHeight = popupHeight - headerHeight

        return VStack(spacing: 0) {
            popupHeader

            featureGridView
                .frame(height: contentHeight)
        }
        .frame(maxWidth: .infinity)
        .frame(height: popupHeight)
        .background(DesignSystem.Colors.backgroundCard)
        .clipShape(RoundedRectangle(cornerRadius: DesignSystem.Rounded.lg))
    }
}

// MARK: - 子视图组件

extension FeatureSelectionPopup {
    private var popupHeader: some View {
        VStack(spacing: DesignSystem.Spacing.md) {
            RoundedRectangle(cornerRadius: 2.5)
                .fill(DesignSystem.Colors.textTertiary.opacity(0.6))
                .frame(width: 40, height: 5)
                .padding(.top, DesignSystem.Spacing.sm)

            HStack {
                Text("功能菜单")
                    .font(DesignSystem.Typography.titleSmall)
                    .foregroundColor(DesignSystem.Colors.textPrimary)

                Spacer()

                Button(action: {
                    Task {
                        await dismissLastPopup()
                    }
                }) {
                    Image(systemName: "xmark")
                        .font(.system(size: DesignSystem.FontSize.lg, weight: .medium))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                        .frame(width: 32, height: 32)
                        .background(DesignSystem.Colors.backgroundInput)
                        .clipShape(Circle())
                }
            }
            .padding(.horizontal, DesignSystem.Spacing.xl)

            Rectangle()
                .fill(DesignSystem.Colors.separator)
                .frame(height: DesignSystem.BorderWidth.thin)
                .padding(.horizontal, DesignSystem.Spacing.xl)
        }
        .background(DesignSystem.Colors.backgroundCard)
    }

    private var featureGridView: some View {
        ScrollView(.vertical, showsIndicators: true) {
            VStack(spacing: DesignSystem.Spacing.xl) {
                mainFeaturesSection

                moreFeaturesSection
            }
            .padding(.horizontal, DesignSystem.Spacing.lg)
            .padding(.vertical, DesignSystem.Spacing.md)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }

    private var mainFeaturesSection: some View {
        HStack(spacing: DesignSystem.Spacing.md) {
            ForEach(mainFeatures, id: \.id) { feature in
                MainFeatureCardView(
                    feature: feature,
                    onTap: {
                        onFeatureSelected?(feature)
                        Task {
                            await dismissLastPopup()
                        }
                    }
                )
            }
        }
    }

    private var moreFeaturesSection: some View {
        VStack(spacing: DesignSystem.Spacing.sm) {
            HStack {
                Text("更多")
                    .font(DesignSystem.Typography.subheadline)
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                Spacer()
            }

            LazyVGrid(columns: [
                GridItem(.flexible(), spacing: DesignSystem.Spacing.md),
                GridItem(.flexible(), spacing: DesignSystem.Spacing.md),
            ], spacing: DesignSystem.Spacing.md) {
                ForEach(moreFeatures, id: \.id) { feature in
                    MoreFeatureCardView(
                        feature: feature,
                        onTap: {
                            onFeatureSelected?(feature)
                            Task {
                                await dismissLastPopup()
                            }
                        }
                    )
                }
            }
        }
    }

    /// 主要功能列表
    private var mainFeatures: [FeatureCardItem] {
        [
            // 相册
            FeatureCardItem(
                id: "album",
                feature: FeatureItem(
                    id: "album",
                    title: "相册",
                    iconName: "photo.on.rectangle.angled",
                    iconColor: DesignSystem.Colors.primary
                ),
                route: .textChat()
            ),
            // 视频
            FeatureCardItem(
                id: "video",
                feature: FeatureItem(
                    id: "video",
                    title: "视频",
                    iconName: "video.fill",
                    iconColor: DesignSystem.Colors.gradient
                ),
                route: .videoGeneration
            ),
            // 文档
            FeatureCardItem(
                id: "document",
                feature: FeatureItem(
                    id: "document",
                    title: "文档",
                    iconName: "doc.text.fill",
                    iconColor: DesignSystem.Colors.primary
                ),
                route: .imageGeneration
            ),
        ]
    }

    /// 更多功能列表
    private var moreFeatures: [FeatureCardItem] {
        [
            // 音频
            FeatureCardItem(
                id: "audio",
                feature: FeatureItem(
                    id: "audio",
                    title: "音频",
                    iconName: "music.note",
                    iconColor: .orange
                ),
                route: .audioGeneration
            ),
            // 图书
            FeatureCardItem(
                id: "book",
                feature: FeatureItem(
                    id: "book",
                    title: "图书",
                    iconName: "book.fill",
                    iconColor: DesignSystem.Colors.gradient
                ),
                route: .imageBook
            ),
            // 图片生成
            FeatureCardItem(
                id: "imageGenerate",
                feature: FeatureItem(
                    id: "imageGenerate",
                    title: "图片生成",
                    iconName: "photo.badge.plus",
                    iconColor: .purple
                ),
                route: .imageGeneration
            ),
            // 音乐生成
            FeatureCardItem(
                id: "musicGenerate",
                feature: FeatureItem(
                    id: "musicGenerate",
                    title: "音乐生成",
                    iconName: "waveform",
                    iconColor: .green
                ),
                route: .audioMusic
            ),
        ]
    }
}

// MARK: - 主要功能卡片视图

private struct MainFeatureCardView: View {
    let feature: FeatureCardItem
    let onTap: () -> Void

    var body: some View {
        Button(action: onTap) {
            VStack(spacing: DesignSystem.Spacing.sm) {
                Image(systemName: feature.feature.iconName)
                    .font(.system(size: 20, weight: .medium))
                    .foregroundColor(feature.feature.iconColor)

                Text(feature.feature.title)
                    .font(.system(size: DesignSystem.FontSize.lg, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                    .lineLimit(1)
            }
            .frame(maxWidth: .infinity)
            .padding(.vertical, DesignSystem.Spacing.xl)
            .background(DesignSystem.Colors.backgroundInput)
            .cornerRadius(DesignSystem.Rounded.md)
            .contentShape(Rectangle())
        }
        .buttonStyle(.plain)
    }
}

// MARK: - 更多功能卡片视图

private struct MoreFeatureCardView: View {
    let feature: FeatureCardItem
    let onTap: () -> Void

    var body: some View {
        Button(action: onTap) {
            HStack(spacing: DesignSystem.Spacing.md) {
                Image(systemName: feature.feature.iconName)
                    .font(.system(size: 18, weight: .medium))
                    .foregroundColor(feature.feature.iconColor)

                Text(feature.feature.title)
                    .font(.system(size: DesignSystem.FontSize.lg, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                    .lineLimit(1)

                Spacer()
            }
            .frame(maxWidth: .infinity, alignment: .leading)
            .padding(.horizontal, DesignSystem.Spacing.lg)
            .padding(.vertical, DesignSystem.Spacing.md)
            .background(DesignSystem.Colors.backgroundInput)
            .cornerRadius(DesignSystem.Rounded.md)
            .contentShape(Rectangle())
        }
        .buttonStyle(.plain)
    }
}
