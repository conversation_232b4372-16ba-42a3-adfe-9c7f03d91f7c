import SwiftUI

/// 加载中按钮
struct LoadingButton: View {
    let isLoading: Bool
    let text: String
    let isEnabled: Bool
    let onTap: () -> Void

    init(
        isLoading: Bool,
        text: String,
        isEnabled: Bool = true,
        onTap: @escaping () -> Void
    ) {
        self.isLoading = isLoading
        self.text = text
        self.isEnabled = isEnabled
        self.onTap = onTap
    }

    var body: some View {
        Button(action: onTap) {
            if isLoading {
                ProgressView()
                    .progressViewStyle(CircularProgressViewStyle(tint: DesignSystem.Colors.primary))
                    .scaleEffect(0.8)
            } else {
                Text(text)
                    .font(DesignSystem.Typography.body)
                    .foregroundColor(
                        isEnabled ? DesignSystem.Colors.primary : DesignSystem.Colors.textTertiary
                    )
            }
        }
        .disabled(!isEnabled || isLoading)
        .buttonStyle(PlainButtonStyle())
    }
}
