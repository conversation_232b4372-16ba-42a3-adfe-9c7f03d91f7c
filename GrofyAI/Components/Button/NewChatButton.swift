import SwiftUI

/// 新建会话按钮
struct NewChatButton: View {
    let onTap: () -> Void

    var body: some View {
        Button(action: onTap) {
            Image("IconChatNew")
                .resizable()
                .scaledToFit()
                .frame(width: 20, height: 20)
                .foregroundColor(DesignSystem.Colors.textPrimary)
        }
        .buttonStyle(.plain)
        .contentShape(Rectangle())
    }
}
