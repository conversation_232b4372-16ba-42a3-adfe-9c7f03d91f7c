import SwiftUI

/// 文本按钮
struct TextButton: View {
    let text: String
    let isEnabled: Bool
    let color: Color
    let onTap: () -> Void

    init(
        text: String,
        isEnabled: Bool = true,
        color: Color = DesignSystem.Colors.primary,
        onTap: @escaping () -> Void
    ) {
        self.text = text
        self.isEnabled = isEnabled
        self.color = color
        self.onTap = onTap
    }

    var body: some View {
        Button(action: onTap) {
            Text(text)
                .font(DesignSystem.Typography.body)
                .foregroundColor(isEnabled ? color : DesignSystem.Colors.textTertiary)
        }
        .disabled(!isEnabled)
        .buttonStyle(PlainButtonStyle())
    }
}
