import UIKit

// MARK: - Web 访问管理器

class WebViewManager {
    static let shared = WebViewManager()

    private init() {}

    /// 使用条款
    private var termsOfServiceURL: String {
        AppConfig.Web.termsOfServiceURL
    }

    /// 隐私政策
    private var privacyPolicyURL: String {
        AppConfig.Web.privacyPolicyURL
    }

    func openTermsOfService() {
        openURL(termsOfServiceURL, title: "服务条款")
    }

    func openPrivacyPolicy() {
        openURL(privacyPolicyURL, title: "隐私政策")
    }

    /// 打开指定 URL
    func openURL(_ urlString: String, title: String? = nil) {
        guard let url = URL(string: urlString) else {
            print("Invalid URL: \(urlString)")
            showErrorAlert(message: "无效的网址")
            return
        }

        openURL(url, title: title)
    }

    /// 打开指定 URL
    func openURL(_ url: URL, title: String? = nil) {
        DispatchQueue.main.async {
            self.presentWebViewController(for: url, title: title)
        }
    }

    // MARK: - 私有方法

    private func presentWebViewController(for url: URL, title: String?) {
        guard let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
              let window = windowScene.windows.first,
              let rootViewController = window.rootViewController
        else {
            openInSystemBrowser(url)
            return
        }

        let webViewController = SimpleWebViewController(url: url, title: title ?? "")
        webViewController.modalPresentationStyle = .fullScreen

        let topViewController = findTopViewController(from: rootViewController)
        topViewController.present(webViewController, animated: true)
    }

    private func openInSystemBrowser(_ url: URL) {
        if UIApplication.shared.canOpenURL(url) {
            UIApplication.shared.open(url, options: [:]) { success in
                if !success {
                    DispatchQueue.main.async {
                        self.showErrorAlert(message: "无法打开网页")
                    }
                }
            }
        } else {
            showErrorAlert(message: "无法打开网页")
        }
    }

    private func showErrorAlert(message: String) {
        guard let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
              let window = windowScene.windows.first,
              let rootViewController = window.rootViewController
        else {
            return
        }

        let alert = UIAlertController(title: "提示", message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default))

        let topViewController = findTopViewController(from: rootViewController)
        topViewController.present(alert, animated: true)
    }

    private func findTopViewController(from viewController: UIViewController) -> UIViewController {
        if let presentedViewController = viewController.presentedViewController {
            return findTopViewController(from: presentedViewController)
        }

        if let navigationController = viewController as? UINavigationController,
           let topViewController = navigationController.topViewController
        {
            return findTopViewController(from: topViewController)
        }

        if let tabBarController = viewController as? UITabBarController,
           let selectedViewController = tabBarController.selectedViewController
        {
            return findTopViewController(from: selectedViewController)
        }

        return viewController
    }
}
