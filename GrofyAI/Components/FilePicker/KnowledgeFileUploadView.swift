import SwiftUI

// MARK: - 知识库文件上传视图

struct KnowledgeFileUploadView: View {
    let categoryId: Int
    let onUploadCompleted: () -> Void

    @StateObject private var uploadManager = FileUploadManager()
    @State private var showFilePicker = false
    @State private var selectedFiles: [SelectedFile] = []
    @State private var showUploadSheet = false

    @Environment(\.presentationMode) private var presentationMode

    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // 导航栏
                navigationBar

                // 内容区域
                if uploadManager.uploadItems.isEmpty {
                    emptyStateView
                } else {
                    fileListView
                }

                // 底部操作栏
                if !uploadManager.uploadItems.isEmpty {
                    bottomActionBar
                }
            }
            .background(DesignSystem.Colors.backgroundPage)
        }
        .sheet(isPresented: $showFilePicker) {
            DocumentFilePicker(
                selectedFiles: $selectedFiles,
                allowMultipleSelection: true
            )
        }
        .onChange(of: selectedFiles) { files in
            if !files.isEmpty {
                uploadManager.addFiles(files)
                selectedFiles.removeAll()
            }
        }
    }

    // MARK: - 导航栏

    private var navigationBar: some View {
        HStack {
            Button("取消") {
                presentationMode.wrappedValue.dismiss()
            }
            .foregroundColor(DesignSystem.Colors.textSecondary)

            Spacer()

            Text("上传文件")
                .font(DesignSystem.Typography.navigationTitle)
                .foregroundColor(DesignSystem.Colors.textPrimary)

            Spacer()

            Button("选择文件") {
                showFilePicker = true
            }
            .foregroundColor(DesignSystem.Colors.primary)
        }
        .padding(.horizontal, DesignSystem.Spacing.lg)
        .padding(.vertical, DesignSystem.Spacing.md)
        .background(DesignSystem.Colors.backgroundPage)
    }

    // MARK: - 空状态视图

    private var emptyStateView: some View {
        EmptyStateView(
            title: "选择要上传的文件",
            description: "支持PDF、Word、Excel、PowerPoint、图片等格式",
            actionButton: ActionButtonConfig(title: "选择文件") {
                showFilePicker = true
            },
            style: .fullScreen
        )
    }

    // MARK: - 文件列表视图

    private var fileListView: some View {
        ScrollView {
            LazyVStack(spacing: DesignSystem.Spacing.md) {
                ForEach(uploadManager.uploadItems) { item in
                    FileUploadRowView(
                        item: item,
                        onRemove: {
                            uploadManager.removeFile(item)
                        },
                        onRetry: nil
                    )
                }
            }
            .padding(DesignSystem.Spacing.lg)
        }
    }

    // MARK: - 底部操作栏

    private var bottomActionBar: some View {
        VStack(spacing: DesignSystem.Spacing.md) {
            if uploadManager.isUploading {
                uploadProgressView
            }

            HStack(spacing: DesignSystem.Spacing.md) {
                Button("清空") {
                    uploadManager.clearAllFiles()
                }
                .foregroundColor(DesignSystem.Colors.textSecondary)
                .padding(.vertical, DesignSystem.Spacing.md)
                .frame(maxWidth: .infinity)
                .background(DesignSystem.Colors.backgroundCard)
                .cornerRadius(DesignSystem.Rounded.lg)
                .disabled(uploadManager.isUploading)

                Button(uploadButtonTitle) {
                    if !uploadManager.isUploading {
                        uploadManager.startUpload(categoryId: categoryId)
                    }
                }
                .foregroundColor(.white)
                .padding(.vertical, DesignSystem.Spacing.md)
                .frame(maxWidth: .infinity)
                .background(uploadButtonColor)
                .cornerRadius(DesignSystem.Rounded.lg)
                .disabled(uploadManager.uploadItems.isEmpty)
            }
        }
        .padding(DesignSystem.Spacing.lg)
        .background(DesignSystem.Colors.backgroundPage)
        .onChange(of: uploadManager.isUploading) { isUploading in
            if !isUploading {
                let stats = uploadManager.uploadStats
                if stats.success > 0, stats.uploading == 0 {
                    DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                        onUploadCompleted()
                        presentationMode.wrappedValue.dismiss()
                    }
                }
            }
        }
    }

    // MARK: - 上传进度视图

    private var uploadProgressView: some View {
        VStack(spacing: DesignSystem.Spacing.sm) {
            HStack {
                Text("上传进度")
                    .font(DesignSystem.Typography.caption)
                    .foregroundColor(DesignSystem.Colors.textSecondary)

                Spacer()

                Text("\(Int(uploadManager.uploadProgress * 100))%")
                    .font(DesignSystem.Typography.caption)
                    .foregroundColor(DesignSystem.Colors.textSecondary)
            }

            ProgressView(value: uploadManager.uploadProgress)
                .progressViewStyle(LinearProgressViewStyle(tint: DesignSystem.Colors.primary))
        }
    }

    // MARK: - 计算属性

    private var uploadButtonTitle: String {
        if uploadManager.isUploading {
            return "上传中..."
        } else {
            let count = uploadManager.uploadItems.count
            return "开始上传（\(count) 个文件）"
        }
    }

    private var uploadButtonColor: Color {
        if uploadManager.isUploading {
            return DesignSystem.Colors.textSecondary
        } else {
            return DesignSystem.Colors.primary
        }
    }
}
