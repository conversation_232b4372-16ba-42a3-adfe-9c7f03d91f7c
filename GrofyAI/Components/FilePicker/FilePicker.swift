//
//  FilePicker.swift
//  GrofyAI
//
//  Created by kissy on 2025/6/17.
//

//MARK: FilePicker
import SwiftUI
import MobileCoreServices

struct FilePicker: UIViewControllerRepresentable {
    // 输出
    @Binding var imageData: Data?
    @Binding var imageName: String? // 新增：用于传递文件名

    @Environment(\.presentationMode) private var presentationMode

    func makeUIViewController(context: Context) -> UIDocumentPickerViewController {
        // iOS 14+ 推荐使用 UTType.image.identifier
        // let supportedTypes = [UTType.image.identifier]
        let supportedTypes: [String] = [kUTTypeImage as String]
        
        let picker = UIDocumentPickerViewController(documentTypes: supportedTypes, in: .import)
        picker.allowsMultipleSelection = false // 确保用户一次只选一个文件
        picker.delegate = context.coordinator
        return picker
    }

    func updateUIViewController(_ uiViewController: UIDocumentPickerViewController, context: Context) {}

    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }

    class Coordinator: NSObject, UIDocumentPickerDelegate {
        let parent: FilePicker

        init(_ parent: FilePicker) {
            self.parent = parent
        }

        func documentPicker(_ controller: UIDocumentPickerViewController, didPickDocumentsAt urls: [URL]) {
            guard let url = urls.first else {
                parent.presentationMode.wrappedValue.dismiss()
                return
            }
            
            // 确保我们有权限访问这个文件（这部分逻辑非常重要，保持不变）
            let shouldStopAccessing = url.startAccessingSecurityScopedResource()
            defer {
                if shouldStopAccessing {
                    url.stopAccessingSecurityScopedResource()
                }
            }
            
            do {
                // 1. 获取文件名
                let fileName = url.lastPathComponent
                
                // 2. 读取文件数据并创建 UIImage
                let originalData = try Data(contentsOf: url)
                
                parent.imageData = originalData // 直接传递 Data
                parent.imageName = fileName
            } catch {
                print("无法从 URL 读取文件: \(error.localizedDescription)")
                // （可选）可以在此处处理错误，例如显示一个警告
            }

            // 4. 关闭选择器
            parent.presentationMode.wrappedValue.dismiss()
        }
        
        func documentPickerWasCancelled(_ controller: UIDocumentPickerViewController) {
            parent.presentationMode.wrappedValue.dismiss()
        }
    }
}
