import SwiftUI

// MARK: - 文件上传行视图

struct FileUploadRowView: View {
    let item: UploadFileItem
    let onRemove: () -> Void
    let onRetry: (() -> Void)?

    var body: some View {
        HStack(spacing: DesignSystem.Spacing.md) {
            fileIcon

            VStack(alignment: .leading, spacing: DesignSystem.Spacing.xs) {
                Text(item.file.name)
                    .font(DesignSystem.Typography.body)
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                    .lineLimit(1)

                HStack {
                    Text(item.file.formattedSize)
                        .font(DesignSystem.Typography.caption)
                        .foregroundColor(DesignSystem.Colors.textSecondary)

                    Text("•")
                        .font(DesignSystem.Typography.caption)
                        .foregroundColor(DesignSystem.Colors.textTertiary)

                    Text(item.file.displayName)
                        .font(DesignSystem.Typography.caption)
                        .foregroundColor(DesignSystem.Colors.textSecondary)

                    Spacer()

                    statusView
                }
            }

            Spacer()

            actionButton
        }
        .padding(DesignSystem.Spacing.md)
        .background(backgroundColorForStatus)
        .overlay(
            RoundedRectangle(cornerRadius: DesignSystem.Rounded.lg)
                .strokeBorder(borderColorForStatus, lineWidth: borderWidthForStatus)
        )
        .cornerRadius(DesignSystem.Rounded.lg)
    }

    // MARK: - 文件图标

    private var fileIcon: some View {
        let (iconName, iconColor) = item.file.typeIcon

        return Image(systemName: iconName)
            .font(.system(size: 24))
            .foregroundColor(iconColor)
            .frame(width: 40, height: 40)
            .background(iconColor.opacity(0.1))
            .cornerRadius(DesignSystem.Rounded.md)
    }

    // MARK: - 状态视图

    @ViewBuilder
    private var statusView: some View {
        switch item.status {
        case .pending:
            Text("等待上传")
                .font(DesignSystem.Typography.caption)
                .foregroundColor(DesignSystem.Colors.textSecondary)

        case .uploading:
            HStack(spacing: DesignSystem.Spacing.xs) {
                ProgressView()
                    .scaleEffect(0.7)

                Text("上传中 \(Int(item.progress * 100))%")
                    .font(DesignSystem.Typography.caption)
                    .foregroundColor(DesignSystem.Colors.primary)
            }

        case .success:
            HStack(spacing: DesignSystem.Spacing.xs) {
                Image(systemName: "checkmark.circle.fill")
                    .foregroundColor(.green)

                Text("上传成功")
                    .font(DesignSystem.Typography.caption)
                    .foregroundColor(.green)
            }

        case .failed:
            HStack(spacing: DesignSystem.Spacing.xs) {
                Image(systemName: "exclamationmark.circle.fill")
                    .foregroundColor(.red)

                Text("上传失败")
                    .font(DesignSystem.Typography.caption)
                    .foregroundColor(.red)
            }
        }
    }

    // MARK: - 操作按钮

    @ViewBuilder
    private var actionButton: some View {
        switch item.status {
        case .failed:
            Button(action: onRemove) {
                Image(systemName: "xmark.circle.fill")
                    .font(.system(size: 20))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
            }
            .buttonStyle(.plain)

        case .pending:
            Button(action: onRemove) {
                Image(systemName: "xmark.circle.fill")
                    .font(.system(size: 20))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
            }
            .buttonStyle(.plain)

        case .uploading:
            Button(action: onRemove) {
                Image(systemName: "stop.circle.fill")
                    .font(.system(size: 20))
                    .foregroundColor(.red)
            }
            .buttonStyle(.plain)

        case .success:
            Image(systemName: "checkmark.circle.fill")
                .font(.system(size: 20))
                .foregroundColor(.green)
        }
    }

    // MARK: - 样式计算属性

    private var backgroundColorForStatus: Color {
        switch item.status {
        case .failed:
            return Color.red.opacity(0.05)
        case .success:
            return Color.green.opacity(0.05)
        default:
            return DesignSystem.Colors.backgroundCard
        }
    }

    private var borderColorForStatus: Color {
        switch item.status {
        case .failed:
            return Color.red.opacity(0.3)
        default:
            return Color.clear
        }
    }

    private var borderWidthForStatus: CGFloat {
        switch item.status {
        case .failed:
            return 1.5
        default:
            return 0
        }
    }
}
