import MobileCoreServices
import SwiftUI
import UniformTypeIdentifiers

// MARK: - 文档文件选择器

struct DocumentFilePicker: UIViewControllerRepresentable {
    @Binding var selectedFiles: [SelectedFile]
    let allowMultipleSelection: Bool
    let allowedFileTypes: [UTType]

    @Environment(\.presentationMode) private var presentationMode

    init(
        selectedFiles: Binding<[SelectedFile]>,
        allowMultipleSelection: Bool = true,
        allowedFileTypes: [UTType] = [
            .pdf, .text, .plainText, .rtf, .html,
            .spreadsheet, .presentation, .data,
            .image, .movie, .audio, .archive,
        ]
    ) {
        _selectedFiles = selectedFiles
        self.allowMultipleSelection = allowMultipleSelection
        self.allowedFileTypes = allowedFileTypes
    }

    func makeUIViewController(context: Context) -> UIDocumentPickerViewController {
        let picker = UIDocumentPickerViewController(forOpeningContentTypes: allowedFileTypes)
        picker.allowsMultipleSelection = allowMultipleSelection
        picker.delegate = context.coordinator
        return picker
    }

    func updateUIViewController(_ uiViewController: UIDocumentPickerViewController, context: Context) {}

    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }

    class Coordinator: NSObject, UIDocumentPickerDelegate {
        let parent: DocumentFilePicker

        init(_ parent: DocumentFilePicker) {
            self.parent = parent
        }

        func documentPicker(_ controller: UIDocumentPickerViewController, didPickDocumentsAt urls: [URL]) {
            var newFiles: [SelectedFile] = []

            for url in urls {
                let shouldStopAccessing = url.startAccessingSecurityScopedResource()
                defer {
                    if shouldStopAccessing {
                        url.stopAccessingSecurityScopedResource()
                    }
                }

                do {
                    let fileName = url.lastPathComponent
                    let fileData = try Data(contentsOf: url)
                    let fileExtension = url.pathExtension.lowercased()
                    let detectedMimeType = mimeType(for: fileExtension)

                    let selectedFile = SelectedFile(
                        id: UUID(),
                        name: fileName,
                        data: fileData,
                        mimeType: detectedMimeType,
                        size: fileData.count
                    )

                    newFiles.append(selectedFile)

                } catch {
                    print("❌ DocumentFilePicker: 无法读取文件 \(url.lastPathComponent): \(error.localizedDescription)")
                }
            }

            // 更新选中的文件列表
            if parent.allowMultipleSelection {
                parent.selectedFiles.append(contentsOf: newFiles)
            } else {
                parent.selectedFiles = newFiles
            }

            parent.presentationMode.wrappedValue.dismiss()
        }

        func documentPickerWasCancelled(_ controller: UIDocumentPickerViewController) {
            parent.presentationMode.wrappedValue.dismiss()
        }
    }
}

// MARK: - 选中文件模型

struct SelectedFile: Identifiable, Equatable {
    let id: UUID
    let name: String
    let data: Data
    let mimeType: String
    let size: Int

    /// 格式化文件大小（复用现有实现）
    var formattedSize: String {
        return size.formattedFileSize
    }

    /// 文件扩展名
    var fileExtension: String {
        return (name as NSString).pathExtension.lowercased()
    }

    /// 文件类别（复用MessageFile.FileCategory）
    var fileCategory: MessageFile.FileCategory {
        return MessageFile.FileCategory.from(fileExtension: fileExtension)
    }

    /// 文件类型图标和颜色（复用MessageFile扩展）
    var typeIcon: (iconName: String, iconColor: Color) {
        return (fileCategory.filledIconName, fileCategory.swiftUIColor)
    }

    /// 文件类型显示名称（复用MessageFile实现）
    var displayName: String {
        return fileCategory.displayName
    }

    /// 转换为UploadData
    func toUploadData() -> UploadData {
        return UploadData(
            data: data,
            fileName: name,
            mimeType: mimeType,
            fieldName: "files"
        )
    }

    static func == (lhs: SelectedFile, rhs: SelectedFile) -> Bool {
        return lhs.id == rhs.id
    }
}
