import Combine
import SwiftUI

// MARK: - 文件上传状态

enum FileUploadStatus: Equatable {
    case pending // 等待上传
    case uploading // 上传中
    case success(fileId: String, fileUrl: String) // 上传成功
    case failed(message: String) // 上传失败

    var isUploading: Bool {
        if case .uploading = self {
            return true
        }
        return false
    }

    var isSuccess: Bool {
        if case .success = self {
            return true
        }
        return false
    }

    var isFailed: Bool {
        if case .failed = self {
            return true
        }
        return false
    }
}

// MARK: - 上传文件项

struct UploadFileItem: Identifiable, Equatable {
    let id: UUID
    let file: SelectedFile
    var status: FileUploadStatus
    var progress: Double

    init(file: SelectedFile) {
        id = UUID()
        self.file = file
        status = .pending
        progress = 0.0
    }

    static func == (lhs: UploadFileItem, rhs: UploadFileItem) -> Bool {
        return lhs.id == rhs.id && lhs.status == rhs.status && lhs.progress == rhs.progress
    }
}

// MARK: - 文件上传管理器

@MainActor
class FileUploadManager: ObservableObject {
    @Published var uploadItems: [UploadFileItem] = []
    @Published var isUploading = false
    @Published var uploadProgress = 0.0

    private let fileService = FileService()
    private var uploadTasks: [String: Task<Void, Never>] = [:]

    /// 添加文件到上传队列
    func addFiles(_ files: [SelectedFile]) {
        clearAllFiles()

        let newItems = files.map { UploadFileItem(file: $0) }
        uploadItems.append(contentsOf: newItems)
    }

    /// 移除文件
    func removeFile(_ item: UploadFileItem) {
        uploadTasks[item.id.uuidString]?.cancel()
        uploadTasks.removeValue(forKey: item.id.uuidString)

        uploadItems.removeAll { $0.id == item.id }

        updateOverallProgress()
    }

    /// 清空所有文件
    func clearAllFiles() {
        // 取消所有上传任务
        for task in uploadTasks.values {
            task.cancel()
        }
        uploadTasks.removeAll()

        uploadItems.removeAll()
        isUploading = false
        uploadProgress = 0.0
    }

    /// 开始上传所有文件（批量上传）
    func startUpload(categoryId: Int) {
        guard !isUploading else { return }

        let pendingItems = uploadItems.filter { $0.status == .pending }
        guard !pendingItems.isEmpty else { return }

        isUploading = true

        // 批量上传所有文件
        let batchTaskKey = "batch_\(UUID().uuidString)"
        let batchUploadTask = Task { [weak self] in
            guard let self else { return }
            await uploadAllFiles(pendingItems, categoryId: categoryId)

            _ = await MainActor.run { [weak self] in
                self?.uploadTasks.removeValue(forKey: batchTaskKey)
            }
        }

        uploadTasks[batchTaskKey] = batchUploadTask
    }

    /// 批量上传所有文件
    private func uploadAllFiles(_ items: [UploadFileItem], categoryId: Int) async {
        for item in items {
            if let index = uploadItems.firstIndex(where: { $0.id == item.id }) {
                uploadItems[index].status = .uploading
                uploadItems[index].progress = 0.0
            }
        }

        do {
            await simulateRandomIncrementalProgress(items: items)

            let uploadDataArray = items.map { $0.file.toUploadData() }

            let results = try await fileService.uploadKnowledgeFiles(
                categoryId: categoryId,
                files: uploadDataArray
            )

            if Task.isCancelled { return }

            await completeProgressTo100(items: items)

            for (index, item) in items.enumerated() {
                guard let itemIndex = uploadItems.firstIndex(where: { $0.id == item.id }) else { continue }

                if index < results.count {
                    let result = results[index]
                    if result.isSuccess, let fileId = result.fileId, let fileUrl = result.fileUrl {
                        uploadItems[itemIndex].status = .success(fileId: fileId, fileUrl: fileUrl)
                        uploadItems[itemIndex].progress = 1.0
                    } else {
                        let errorMessage = result.message ?? "上传失败"
                        uploadItems[itemIndex].status = .failed(message: errorMessage)
                        uploadItems[itemIndex].progress = 0.0
                    }
                } else {
                    uploadItems[itemIndex].status = .failed(message: "服务器响应不完整")
                    uploadItems[itemIndex].progress = 0.0
                }
            }

        } catch {
            if !Task.isCancelled {
                let errorMessage = getErrorMessage(from: error)
                for item in items {
                    if let index = uploadItems.firstIndex(where: { $0.id == item.id }) {
                        uploadItems[index].status = .failed(message: errorMessage)
                        uploadItems[index].progress = 0.0
                    }
                }

                ToastManager.shared.showError("文件上传失败，请重新选择上传")
            }
        }

        uploadTasks.removeValue(forKey: "batch")
        uploadTasks.removeValue(forKey: "retry")

        updateOverallProgress()

        checkUploadCompletion()

        if !isUploading {
            let stats = uploadStats
            if stats.success > 0 {
                ToastManager.shared.showSuccess("成功上传 \(stats.success) 个文件")
            }
            if stats.failed > 0 {
                ToastManager.shared.showError("\(stats.failed) 个文件上传失败，请重新选择上传")
            }

            DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
                self.clearAllFiles()
            }
        }
    }

    /// 更新整体上传进度
    private func updateOverallProgress() {
        guard !uploadItems.isEmpty else {
            uploadProgress = 0.0
            return
        }

        let totalProgress = uploadItems.reduce(0.0) { $0 + $1.progress }
        uploadProgress = totalProgress / Double(uploadItems.count)
    }

    /// 检查上传是否完成
    private func checkUploadCompletion() {
        let hasUploadingItems = uploadItems.contains { $0.status.isUploading }
        if !hasUploadingItems {
            isUploading = false
        }
    }

    /// 获取上传统计信息
    var uploadStats: (total: Int, success: Int, failed: Int, uploading: Int) {
        let total = uploadItems.count
        let success = uploadItems.count(where: { $0.status.isSuccess })
        let failed = uploadItems.count(where: { $0.status.isFailed })
        let uploading = uploadItems.count(where: { $0.status.isUploading })

        return (total: total, success: success, failed: failed, uploading: uploading)
    }

    /// 清理已成功上传的文件
    func clearSuccessfulUploads() {
        uploadItems.removeAll { $0.status.isSuccess }
        updateOverallProgress()
    }

    /// 清理失败的上传文件
    func clearFailedUploads() {
        uploadItems.removeAll { $0.status.isFailed }
        updateOverallProgress()
    }

    /// 检查是否有成功完成的上传
    var hasSuccessfulUploads: Bool {
        return uploadItems.contains { $0.status.isSuccess }
    }

    /// 获取成功上传的文件数量
    var successfulUploadCount: Int {
        return uploadItems.count(where: { $0.status.isSuccess })
    }

    // MARK: - 随机增量进度模拟

    /// 随机增量进度模拟（3-8%增量，200-500ms间隔，上限88%）
    private func simulateRandomIncrementalProgress(items: [UploadFileItem]) async {
        var currentProgress = 0.0
        let maxProgress = 0.88 // 88%上限

        while currentProgress < maxProgress {
            if Task.isCancelled { return }

            // 随机增量：3-8%
            let increment = Double.random(in: 0.03...0.08)
            currentProgress = min(currentProgress + increment, maxProgress)

            // 更新所有文件的进度
            for item in items {
                if let index = uploadItems.firstIndex(where: { $0.id == item.id }) {
                    uploadItems[index].progress = currentProgress
                }
            }

            updateOverallProgress()

            // 随机间隔：200-500ms
            let interval = Double.random(in: 0.2...0.5)
            try? await Task.sleep(nanoseconds: UInt64(interval * 1_000_000_000))
        }
    }

    /// 快速完成进度到100%
    private func completeProgressTo100(items: [UploadFileItem]) async {
        // 快速从88%跳转到100%
        for item in items {
            if let index = uploadItems.firstIndex(where: { $0.id == item.id }) {
                uploadItems[index].progress = 1.0
            }
        }

        updateOverallProgress()
    }

    /// 获取友好的错误信息
    private func getErrorMessage(from error: Error) -> String {
        if let businessError = error as? BusinessError {
            return businessError.message
        } else if let urlError = error as? URLError {
            switch urlError.code {
            case .timedOut:
                return "上传超时，请检查网络连接"
            case .notConnectedToInternet:
                return "网络连接失败，请检查网络设置"
            case .cancelled:
                return "上传已取消"
            default:
                return "网络请求失败"
            }
        }
        return "上传失败: \(error.localizedDescription)"
    }
}
