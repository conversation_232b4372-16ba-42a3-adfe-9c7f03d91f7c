//
//  ImagePickerCameraPreview.swift
//  GrofyAI
//
//  Created by kissy on 2025/6/17.
//

import SwiftUI
import AVFoundation

// MARK: - CameraPreviewView.swift (附中文注释)

import SwiftUI
import AVFoundation

// 新增：一个自定义的 UIView 子类，用于更精确地管理预览图层的布局。
class VideoPreviewUIView: UIView {
    
    // 这个属性将持有来自相机的实时预览图层。
    var previewLayer: AVCaptureVideoPreviewLayer?

    // 这个方法会在视图的布局发生任何变化时（例如，初始布局、屏幕旋转）被系统自动调用。
    override func layoutSubviews() {
        super.layoutSubviews()
        
        // 确保预览图层的 frame (位置和尺寸) 始终与其父视图的 bounds (边界) 保持一致。
        // 这是修复所有位置偏差问题的关键。
        previewLayer?.frame = self.bounds
    }
}

// 更新后的 UIViewRepresentable 桥接视图。
// (注意：我将您提供的 ImagePickerCameraPreview 名称改回了 CameraPreviewView 以保持项目一致性)
struct ImagePickerCameraPreview: UIViewRepresentable {
    
    // 通过 @ObservedObject 订阅相机服务的变化。
    @ObservedObject var service: ImagePickerCameraService
    
    // 这个方法现在返回我们自定义的 UIView 子类。
    func makeUIView(context: Context) -> VideoPreviewUIView {
        // 创建一个 frame 为 .zero 的视图。SwiftUI 将会负责管理它的最终尺寸和位置。
        let view = VideoPreviewUIView(frame: .zero)
        view.backgroundColor = .clear // 设置背景色为透明。
        
        // 将相机服务中的 previewLayer 传递给我们自定义的 UIView。
        view.previewLayer = service.previewLayer
        
        // 如果预览图层尚未被添加到任何父图层上，就将它添加到我们自定义视图的图层层级中。
        if let layer = service.previewLayer, layer.superlayer == nil {
            view.layer.addSublayer(layer)
        }
        
        return view
    }
    
    // updateUIView 现在只负责处理配置相关的更新，比如设备方向的改变。
    // 布局相关的更新已经由 VideoPreviewUIView 的 layoutSubviews() 方法自动处理了。
    func updateUIView(_ uiView: VideoPreviewUIView, context: Context) {
        // 当设备方向改变时，同步更新预览图层的视频方向。
        if let connection = service.previewLayer?.connection,
           let orientation = AVCaptureVideoOrientation(deviceOrientation: UIDevice.current.orientation) {
            connection.videoOrientation = orientation
        }
    }
}

// 这个用于转换设备方向的辅助扩展保持不变。
extension AVCaptureVideoOrientation {
    init?(deviceOrientation: UIDeviceOrientation) {
        switch deviceOrientation {
        case .portrait: self = .portrait
        case .portraitUpsideDown: self = .portraitUpsideDown
        case .landscapeLeft: self = .landscapeRight // 注意：这里的映射是相反的
        case .landscapeRight: self = .landscapeLeft // 注意：这里的映射是相反的
        default: return nil // 对于其他方向（如 faceUp, faceDown），我们不进行转换
        }
    }
}
