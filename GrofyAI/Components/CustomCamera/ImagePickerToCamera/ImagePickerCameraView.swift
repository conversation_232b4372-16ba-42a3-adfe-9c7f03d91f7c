//
//  ImagePickerCameraView.swift
//  GrofyAI
//
//  Created by kissy on 2025/6/17.
//

// MARK: - CustomCameraView.swift

import SwiftUI

struct ImagePickerCameraView: View {
    @StateObject private var service = ImagePickerCameraService()
    
//    @Binding var capturedImage: UIImage?
    @Binding var capturedImageData: Data?
    @Binding var capturedImageName: String?
    
    @Environment(\.presentationMode) private var presentationMode
    
    private static let photoMode = "照片"
    @State private var selectedMode: String = "照片"
    
    
    private var flashButtonIconName: String {
        switch service.flashMode {
        case .on: return "bolt.fill"
        case .off: return "bolt.slash.fill"
        case .auto: return "bolt.badge.a.fill"
        @unknown default: return "bolt.slash.fill"
        }
    }
    
    var body: some View {
        ZStack {
            Color.black.ignoresSafeArea()
            
            if service.isPhotoTaken {
                photoPreviewView()
            } else if service.isSessionRunning {
                
                VStack {
                    topControlsView()
                    ZStack {
                        ImagePickerCameraPreview(service: service)
                        VStack{
                            Spacer()
                            zoomControlsView()
                                .padding(.bottom,20)
                        }
                    }
                    bottomControlsView()
                    
                }
            } else {
                ProgressView().progressViewStyle(CircularProgressViewStyle(tint: .white))
            }
        }
        .onAppear(perform: service.setup)
        .onDisappear(perform: service.stopSession)
    }
    
    
    private struct ZoomFactorButton: View {
        let factor: CGFloat
        let isSelected: Bool
        let action: () -> Void
        
        var body: some View {
            Button(action: action) {
                Text(String(format: "%.1f", factor))
                    .font(.system(size: 13, weight: .bold))
                    .foregroundColor(isSelected ? .yellow : .white)
                    .padding(.vertical, 6)
                    .padding(.horizontal, 10)
                    .background(ZStack { if isSelected { Circle().fill(Color.black.opacity(0.3)) } })
            }
        }
    }
    
    
    // MARK: - UI Component Views
    @ViewBuilder private func topControlsView() -> some View {
        HStack {
            Button(action: { service.toggleFlash() }) {
                Image(systemName: flashButtonIconName)
            }
            Spacer()
            
        }
        .font(.title2)
        .foregroundColor(.white)
        .padding(.horizontal)
        .padding(.top, 15)
    }
    
    
    @ViewBuilder private func zoomControlsView() -> some View {
        HStack(spacing: 12) {
            ForEach(service.availableZoomFactors, id: \.self) { factor in
                ZoomFactorButton(
                    factor: factor,
                    isSelected: service.currentZoomFactor == factor,
                    action: { service.setZoom(factor: factor) }
                )
            }
        }
        .padding(6)
        .background(Color.black.opacity(0.4))
        .clipShape(Capsule())
        .padding(.bottom, 10)
    }
    
    
    @ViewBuilder private func bottomControlsView() -> some View {
        HStack(alignment: .center, spacing: 0) {
            Button(action: { presentationMode.wrappedValue.dismiss() }) {
                Image(systemName: "xmark")
                    .font(.title)
                    .foregroundColor(.white)
                    .frame(width: 60, height: 60)
                    .background(Color.white.opacity(0.2))
                    .clipShape(RoundedRectangle(cornerRadius: 8))
            }.frame(maxWidth: .infinity)
            
            
            Button(action: { service.capturePhoto() }) {
                ZStack {
                    Circle().fill(Color.white)
                    Circle().stroke(Color.black, lineWidth: 4)
                }
                .frame(width: 75, height: 75)
            }.frame(maxWidth: .infinity)
            
            Button(action: { service.switchCamera() }) {
                Image(systemName: "arrow.triangle.2.circlepath")
                    .font(.title)
                    .foregroundColor(.white)
                    .frame(width: 60, height: 60)
                    .background(Color.white.opacity(0.2))
                    .clipShape(Circle())
            }.frame(maxWidth: .infinity)
        }
        .padding(.bottom, 30)
    }
    
    
    @ViewBuilder private func photoPreviewView() -> some View {
        VStack {
            Spacer()
            if let image = service.capturedImage { Image(uiImage: image).resizable().scaledToFit() }
            Spacer()
            HStack {
                Button("重拍") { service.isPhotoTaken = false; service.capturedImage = nil; service.startSession() }
                Spacer()
                Button("使用照片") {
//                    if let img = service.capturedImage {
                    if service.capturedImage != nil{
                        // ---- 这是核心功能增强 ----
                        //                        guard let image = service.capturedImage else { return }
                        
                        guard let data = service.capturedImageData else { return }
                        // 1. 设置图片
                        //                        capturedImage = image
                        
                        capturedImageData = data
                        
                        // 2. 生成并设置文件名
                        let timestamp = Int(Date().timeIntervalSince1970)
                        capturedImageName = "photo_\(timestamp).jpeg" // 使用 jpeg 扩展名
                        //                        capturedImage = img;
                        presentationMode.wrappedValue.dismiss()
                    }
                }
            }.font(.headline).foregroundColor(.white).padding()
            Spacer()
        }
    }
}

