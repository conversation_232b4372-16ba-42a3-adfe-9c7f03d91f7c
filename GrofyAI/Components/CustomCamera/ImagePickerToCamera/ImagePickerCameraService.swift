//
//  ImagePickerCameraService.swift
//  GrofyAI
//
//  Created by kissy on 2025/6/17.
//

import Foundation
import AVFoundation
import UIKit

// CameraService 是一个遵循 ObservableObject 协议的类，用于管理所有与相机相关的底层逻辑。
// 它封装了 AVFoundation 的复杂性，并为 SwiftUI 视图提供简单的、可观察的状态。
class ImagePickerCameraService: NSObject, ObservableObject, AVCapturePhotoCaptureDelegate {
    
    // MARK: - Published Properties (可供UI订阅的状态)
    
    /// 拍摄完成后的照片，类型为 UIImage。
    @Published var capturedImage: UIImage?
    
    /// 拍摄完成后用于最终导出的原始数据，类型为 Data。
    @Published var capturedImageData: Data?
    
    /// 一个布尔值，标记是否已经拍摄了一张照片。用于在UI中切换实时预览和照片预览。
    @Published var isPhotoTaken = false
    
    /// 当前的闪光灯模式 (开, 关, 自动)。
    @Published var flashMode: AVCaptureDevice.FlashMode = .off
    
    /// 当前的变焦系数 (例如 0.5, 1.0, 2.0)。
    @Published var currentZoomFactor: CGFloat = 1.0
    
    /// 设备上所有可用的变焦系数数组。UI会根据此数组动态生成变焦按钮。
    @Published var availableZoomFactors: [CGFloat] = []
    
    /// 一个布尔值，标记 AVCaptureSession 是否正在运行。这是UI判断相机是否准备就绪的关键。
    @Published var isSessionRunning = false

    // MARK: - Private Properties (内部管理)
    
    /// 核心的 AVFoundation 会话对象，管理输入和输出。
    private var captureSession: AVCaptureSession!
    
    /// 后置超广角摄像头设备。
    private var backUltraWideCamera: AVCaptureDevice?
    
    /// 后置广角摄像头设备 (通常是主摄像头)。
    private var backWideCamera: AVCaptureDevice?
    
    /// 后置长焦摄像头设备。
    private var backTelephotoCamera: AVCaptureDevice?
    
    /// 前置摄像头设备。
    private var frontCamera: AVCaptureDevice?
    
    /// 当前正在使用的摄像头设备。
    private var currentCamera: AVCaptureDevice!
    
    /// 用于捕捉静态照片的输出对象。
    private var photoOutput: AVCapturePhotoOutput!
    
    /// 用于在UI上显示实时相机画面的图层。
    var previewLayer: AVCaptureVideoPreviewLayer!
    
    override init() { super.init() }
    
    /// 公开的设置方法，由UI的 onAppear 调用，作为启动相机的入口点。
    func setup() {
        if captureSession == nil {
            // 将权限检查和会话配置放到后台线程，避免阻塞UI。
            DispatchQueue.global(qos: .userInitiated).async {
                self.checkPermissions()
            }
        }
    }
    
    /// 检查相机权限。
    private func checkPermissions() {
        switch AVCaptureDevice.authorizationStatus(for: .video) {
        case .authorized: // 已授权，直接配置会话。
            configureSession()
        case .notDetermined: // 尚未决定，发起权限请求。
            AVCaptureDevice.requestAccess(for: .video) { [weak self] granted in
                if granted {
                    // 用户同意后，在主线程上配置会话。
                    DispatchQueue.main.async { self?.configureSession() }
                }
            }
        default: // 已拒绝或受限。
            print("相机权限被拒绝或受限。")
        }
    }
    
    /// 配置 AVCaptureSession，包括输入、输出和预览层。
    private func configureSession() {
        captureSession = AVCaptureSession()
        captureSession.beginConfiguration() // 开始配置
        captureSession.sessionPreset = .photo // 设置会话质量为照片级别。
        
        discoverCameras() // 查找设备上所有可用的摄像头。
        
        // 默认使用后置广角摄像头。
        guard let defaultCamera = backWideCamera else {
            captureSession.commitConfiguration(); return
        }
        currentCamera = defaultCamera
        
        // 设置输入设备。
        do {
            let input = try AVCaptureDeviceInput(device: currentCamera)
            if captureSession.canAddInput(input) { captureSession.addInput(input) }
        } catch {
            print("设置相机输入时出错: \(error)"); captureSession.commitConfiguration(); return
        }
        
        // 设置输出设备。
        photoOutput = AVCapturePhotoOutput()
        if captureSession.canAddOutput(photoOutput) { captureSession.addOutput(photoOutput) }
        
        // 在主线程上创建预览图层，因为它与UI相关。
        DispatchQueue.main.async {
            self.previewLayer = AVCaptureVideoPreviewLayer(session: self.captureSession)
            self.previewLayer.videoGravity = .resizeAspect // 保持长宽比，允许黑边。
            self.startSession() // 预览层准备好后，启动会话。
        }
        
        captureSession.commitConfiguration() // 提交配置
    }
    
    /// 查找并分类设备上的所有摄像头。
    private func discoverCameras() {
        let devices = AVCaptureDevice.DiscoverySession(deviceTypes:
            [.builtInUltraWideCamera, .builtInWideAngleCamera, .builtInTelephotoCamera],
            mediaType: .video, position: .back).devices
        
        backUltraWideCamera = devices.first(where: { $0.deviceType == .builtInUltraWideCamera })
        backWideCamera = devices.first(where: { $0.deviceType == .builtInWideAngleCamera })
        backTelephotoCamera = devices.first(where: { $0.deviceType == .builtInTelephotoCamera })
        frontCamera = AVCaptureDevice.default(.builtInWideAngleCamera, for: .video, position: .front)
        
        // 根据找到的摄像头，生成可用的变焦系数数组。
        var factors: [CGFloat] = []
        if backUltraWideCamera != nil { factors.append(0.5) }
        if backWideCamera != nil { factors.append(1.0) }
        if backTelephotoCamera != nil { factors.append(2.0) }
        DispatchQueue.main.async { self.availableZoomFactors = factors }
    }

    /// 根据UI传递的变焦系数，切换到对应的物理摄像头。
    func setZoom(factor: CGFloat) {
        guard factor != currentZoomFactor else { return }
        var newCamera: AVCaptureDevice?
        switch factor {
        case 0.5: newCamera = backUltraWideCamera
        case 1.0: newCamera = backWideCamera
        case 2.0: newCamera = backTelephotoCamera
        default: newCamera = backWideCamera
        }
        if let camera = newCamera {
            changeCamera(to: camera)
            DispatchQueue.main.async { self.currentZoomFactor = factor }
        }
    }
    
    /// 更换当前会话中的摄像头输入设备。
    private func changeCamera(to newCameraDevice: AVCaptureDevice) {
        guard let session = captureSession, session.isRunning else { return }
        session.beginConfiguration()
        if let currentInput = session.inputs.first { session.removeInput(currentInput) }
        do {
            let newInput = try AVCaptureDeviceInput(device: newCameraDevice)
            if session.canAddInput(newInput) {
                session.addInput(newInput)
                currentCamera = newCameraDevice
            }
        } catch { print("切换摄像头时出错: \(error)") }
        session.commitConfiguration()
    }

    /// 在前置和后置摄像头之间切换。
    func switchCamera() {
        let newCamera = (currentCamera.position == .back) ? frontCamera : backWideCamera
        if let camera = newCamera {
            changeCamera(to: camera)
            // 切换到前置时，重置变焦系数为1.0
            DispatchQueue.main.async { self.currentZoomFactor = 1.0 }
        }
    }

    /// 拍摄一张照片。
    func capturePhoto() {
        let settings = AVCapturePhotoSettings()
        // 根据当前闪光灯模式配置拍摄设置。
        if currentCamera.hasFlash { settings.flashMode = self.flashMode }
        photoOutput.capturePhoto(with: settings, delegate: self)
    }

    /// AVCapturePhotoCaptureDelegate 的代理方法，当照片处理完成后被调用。
    func photoOutput(_ output: AVCapturePhotoOutput, didFinishProcessingPhoto photo: AVCapturePhoto, error: Error?) {
        stopSession() // 拍照后停止会话。
        guard let imageData = photo.fileDataRepresentation(), error == nil else { return }
        DispatchQueue.main.async {
            
            self.capturedImageData = imageData // 这是要传递出去的宝贵数据
            self.capturedImage = UIImage(data: imageData) // 这只是为了在确认界面显示
            self.isPhotoTaken = true
        }
    }
    
    /// 启动相机捕捉会话。
    func startSession() {
        if let s = captureSession, !s.isRunning {
            DispatchQueue.global().async {
                s.startRunning()
                DispatchQueue.main.async { self.isSessionRunning = true } // 通知UI会话已运行
            }
        }
    }
    
    /// 停止相机捕捉会话。
    func stopSession() {
        if let s = captureSession, s.isRunning {
            s.stopRunning()
            DispatchQueue.main.async { self.isSessionRunning = false } // 通知UI会话已停止
        }
    }
    
    /// 循环切换闪光灯模式 (开 -> 关 -> 自动 -> 开 ...)。
    func toggleFlash() {
        switch flashMode {
        case .on: flashMode = .off
        case .off: flashMode = .auto
        case .auto: flashMode = .on
        @unknown default: flashMode = .off
        }
    }
    
    //重拍时调用此方法。
    func retakePhoto() {
        // 将重置操作放在主线程，因为它们会更新 @Published 属性，从而影响UI。
        DispatchQueue.main.async {
            self.isPhotoTaken = false
            self.capturedImageData = nil
            self.capturedImage = nil
            
            // 关键一步：重新启动相机预览会话。
            // 因为拍照后 (didFinishProcessingPhoto) 我们停止了会话，
            // 所以现在需要重新启动它。
            self.startSession()
        }
    }
}
