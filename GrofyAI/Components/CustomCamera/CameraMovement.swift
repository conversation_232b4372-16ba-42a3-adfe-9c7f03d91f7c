//
//  CameraMovement.swift
//  GrofyAI
//
//  Created by kissy on 2025/6/26.
//
import SwiftUI

struct CameraMovementView: View {
    
    // MARK: - State Properties
    
    /// 控制矩形的水平和垂直位移
    @Binding private var offsetWidth: Int
    
    @Binding private var offsetHeight: Int
    
    /// 控制矩形的缩放比例
    @Binding private var scale: Int
    
    /// 控制绕 X 轴的旋转角度（垂直翻转） Tilt
    @Binding private var rotationX: Int
    
    /// 控制绕 Y 轴的旋转角度（水平翻转） Roll
    @Binding private var rotationY: Int
    
    /// 控制绕 Z 轴的平面旋转角度 Pan
    @Binding private var rotationZ: Int
    
    private var mode: CameraConfigOption
    
    
    init(
        mode: CameraConfigOption,
        offsetWidth: Binding<Int>,
        offsetHeight: Binding<Int>,
        scale: Binding<Int>,
        rotationX: Binding<Int>,
        rotationY: Binding<Int>,
        rotationZ: Binding<Int>
    ) {
        self.mode = mode
        self._offsetWidth = offsetWidth
        self._offsetHeight = offsetHeight
        self._scale = scale
        self._rotationX = rotationX
        self._rotationY = rotationY
        self._rotationZ = rotationZ
    }
    
    // MARK: - Body
    
    var body: some View {
        VStack(spacing: 10) {
            // MARK: - Controls
            
            VStack(alignment: .leading, spacing: 0) {
                switch mode {
                case .horizontal:
                    // 【修改】绑定到独立的 offsetWidth
                    TitledSlider(
                        title: mode.description,
                        value: $offsetWidth, // 绑定到 Double
                        in: -10...10,       // 范围是 Double
                        step: 1         // 步长是 Double
                    )
                    
                case .vertical:
                    // 【修改】绑定到独立的 offsetHeight
                    TitledSlider(
                        title: mode.description,
                        value: $offsetHeight, // 绑定到 Double
                        in: -10...10,       // 范围是 Double
                        step: 1         // 步长是 Double
                    )
                    
                case .pan:
                    TitledSlider(
                        title: mode.description,
                        value: $rotationZ, // 绑定到 Double
                        in: -10...10,       // 范围是 Double
                        step: 1         // 步长是 Double
                    )
                    
                case .tilt:
                    TitledSlider(
                        title: mode.description,
                        value: $rotationX, // 绑定到 Double
                        in: -10...10,       // 范围是 Double
                        step: 1         // 步长是 Double
                    )
                    
                case .roll:
                    TitledSlider(
                        title: mode.description,
                        value: $rotationY, // 绑定到 Double
                        in: -10...10,       // 范围是 Double
                        step: 1         // 步长是 Double
                    )
                    
                case .zoom:
                    TitledSlider(
                        title: mode.description,
                        value: $scale, // 绑定到 Double
                        in: -10...10,       // 范围是 Double
                        step: 1         // 步长是 Double
//                        onInfoTapped: {
//                            print("信息按钮被点击了！")
//                        }
                    )
                case .none:
                    Text("当前无运镜操作")
                        .font(.subheadline)
                        .foregroundColor(.gray)
                        .frame(height: 30)
                }
            }
            .animation(.spring(response: 0.4, dampingFraction: 0.6), value: mode)
            
            
            ZStack {
                RoundedRectangle(cornerRadius: 10)
                    .stroke(style: StrokeStyle(lineWidth: 1))
                    .foregroundColor(.gray.opacity(0.5))
                
                // 需要被变换的矩形
                RoundedRectangle(cornerRadius: 10)
                    .stroke(style: StrokeStyle(lineWidth: 1, dash: [5]))
                
                    .frame(width: 120, height: 70)
                    .overlay {
                        Image(systemName: "camera.metering.center.weighted.average")
                            .frame(height:50)
                            .font(.largeTitle) // 让图标更清晰
                            .foregroundColor(DesignSystem.Colors.textSecondary) // 修改图标颜色以增加对比度
                    }
                
                // MARK: - Final Transform Chain
                
                // 1. 应用缩放
                    .scaleEffect( (Double(scale)*0.05) + 1)
                
                // 2. 应用绕 X 轴的旋转（垂直翻转）
                    .rotation3DEffect(
                        .degrees(Double(rotationX * 8)),
                        axis: (x: 1, y: 0, z: 0),
                        perspective: 0.5
                    )
                
                // 3. 应用绕 Y 轴的旋转（水平翻转）
                    .rotation3DEffect(
                        .degrees(Double(rotationY * 8)),
                        axis: (x: 0, y: 1, z: 0)
                    )
                
                // 4. 应用绕 Z 轴的平面旋转
                    .rotationEffect(.degrees(Double(rotationZ) * 4.5))
                
                // 5. 最后应用 2D 位移
                    .offset(x: CGFloat(offsetWidth*5), y: CGFloat(offsetHeight*5))
            }
            .frame(height: 200)

        }
        .onChange(of: mode) { _ in
            withAnimation(.easeInOut) {
                resetTransformations()
            }
        }
    }
    
    // MARK: - Helper Methods
    
    private func resetTransformations() {
        // 【修改】分别重置
        offsetWidth = 0
        offsetHeight = 0
        scale = 0
        rotationX = 0
        rotationY = 0
        rotationZ = 0
    }
    
}
