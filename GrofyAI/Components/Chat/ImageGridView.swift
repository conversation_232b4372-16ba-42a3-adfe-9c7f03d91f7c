import Kingfisher
import SwiftUI

// MARK: - 九宫格图片展示组件

struct ImageGridView: View {
    let images: [ImageDisplayable]

    /// 最大显示宽度（默认为屏幕宽度的70%）
    let maxWidth: CGFloat

    let alignment: HorizontalAlignment

    let onImageTap: (Int) -> Void

    @State private var showFullScreenViewer = false
    @State private var selectedImageIndex = 0

    init(
        images: [ImageDisplayable],
        maxWidth: CGFloat = UIScreen.main.bounds.width * 0.7,
        alignment: HorizontalAlignment = .leading,
        onImageTap: @escaping (Int) -> Void = { _ in }
    ) {
        self.images = images
        self.maxWidth = maxWidth
        self.alignment = alignment
        self.onImageTap = onImageTap
    }

    /// 根据图片数量计算网格列数
    private var columns: [GridItem] {
        let count = images.count
        let columnCount = switch count {
        case 1:
            1
        case 2, 4:
            2
        case 3, 5...9:
            3
        default:
            3
        }

        return Array(repeating: .init(.flexible(), spacing: DesignSystem.Spacing.xs), count: columnCount)
    }

    private var imageSize: CGFloat {
        let count = images.count
        let spacing = DesignSystem.Spacing.xs

        switch count {
        case 1:
            return min(maxWidth, 200) // 单张图片最大200pt
        case 2, 4:
            return (maxWidth - spacing) / 2
        case 3, 5...9:
            return (maxWidth - spacing * 2) / 3
        default:
            return (maxWidth - spacing * 2) / 3
        }
    }

    var body: some View {
        if !images.isEmpty {
            LazyVGrid(columns: columns, alignment: alignment, spacing: DesignSystem.Spacing.xs) {
                ForEach(images.indices, id: \.self) { index in
                    imageCell(for: images[index], at: index)
                }
            }
            .frame(maxWidth: maxWidth)
            .fullScreenCover(isPresented: $showFullScreenViewer) {
                ImageGridFullScreenViewer(
                    images: images,
                    initialIndex: selectedImageIndex,
                    isPresented: $showFullScreenViewer
                )
            }
        }
    }

    @ViewBuilder
    private func imageCell(for image: ImageDisplayable, at index: Int) -> some View {
        ImageGridThumbnailView(
            image: image,
            size: imageSize,
            cornerRadius: DesignSystem.Rounded.md
        ) {
            selectedImageIndex = index
            onImageTap(index)
            showFullScreenViewer = true
        }
    }
}

// MARK: - 图片数据协议

protocol ImageDisplayable {
    var id: String { get }
    var url: String { get }
    var localImage: UIImage? { get }
    var isLoading: Bool { get }
}

extension MessageFile: ImageDisplayable {
    var isLoading: Bool {
        return url.isEmpty
    }
}

// MARK: - MessageImage 扩展

extension MessageImage: ImageDisplayable {
    var isLoading: Bool {
        if isGenerationRelated {
            return false
        }

        if fileUrl.hasPrefix("data:image/") {
            return false
        }

        return fileUrl.isEmpty
    }
}

// MARK: - 图片缩略图组件

struct ImageGridThumbnailView: View {
    let image: ImageDisplayable
    let size: CGFloat
    let cornerRadius: CGFloat
    let onTap: () -> Void

    @State private var loadingFailed = false

    var body: some View {
        Group {
            if loadingFailed {
                errorStateView
            } else {
                imageView
            }
        }
        .frame(width: size, height: size)
        .cornerRadius(cornerRadius)
        .onTapGesture(perform: onTap)
    }

    @ViewBuilder
    private var imageView: some View {
        if let messageImage = image as? MessageImage, messageImage.isGenerationRelated {
            switch messageImage.generationState {
            case .placeholder:
                loadingStateView
            case .partialImage:
                partialImageView(messageImage)
            case .finalizing:
                finalizingImageView(messageImage)
            case .completed, .none:
                defaultImageView
            }
        } else {
            defaultImageView
        }
    }

    @ViewBuilder
    private func partialImageView(_ messageImage: MessageImage) -> some View {
        if messageImage.url.hasPrefix("data:image/"),
           let data = Data(base64Encoded: String(messageImage.url.dropFirst(22))),
           let uiImage = UIImage(data: data)
        {
            ZStack {
                Image(uiImage: uiImage)
                    .resizable()
                    .aspectRatio(contentMode: .fill)
                    .clipped()

                if let progressInfo = messageImage.progressInfo {
                    VStack {
                        Spacer()
                        HStack {
                            progressIndicator(current: progressInfo.current, total: progressInfo.total)
                            Spacer()
                        }
                    }
                    .padding(DesignSystem.Spacing.xs)
                }
            }
        } else {
            errorStateView
        }
    }

    @ViewBuilder
    private func progressIndicator(current: Int, total: Int) -> some View {
        HStack(spacing: DesignSystem.Spacing.xs) {
            ProgressView()
                .scaleEffect(0.6)
                .tint(.white)

            Text("生成中 \(current)/\(total)")
                .font(DesignSystem.Typography.caption)
                .foregroundColor(.white)
        }
        .padding(.horizontal, DesignSystem.Spacing.xs)
        .padding(.vertical, 2)
        .background(
            RoundedRectangle(cornerRadius: DesignSystem.Rounded.sm)
                .fill(Color.black.opacity(0.7))
        )
    }

    @ViewBuilder
    private func finalizingImageView(_ messageImage: MessageImage) -> some View {
        ZStack {
            if messageImage.url.hasPrefix("data:image/"),
               let data = Data(base64Encoded: String(messageImage.url.dropFirst(22))),
               let uiImage = UIImage(data: data)
            {
                Image(uiImage: uiImage)
                    .resizable()
                    .aspectRatio(contentMode: .fill)
                    .clipped()
            }

            RoundedRectangle(cornerRadius: cornerRadius)
                .fill(Color.black.opacity(0.3))
                .overlay {
                    VStack(spacing: DesignSystem.Spacing.xs) {
                        ProgressView()
                            .tint(.white)
                            .scaleEffect(0.8)

                        Text("处理中...")
                            .font(DesignSystem.Typography.caption)
                            .foregroundColor(.white)
                    }
                }
        }
    }

    @ViewBuilder
    private var defaultImageView: some View {
        if image.isLoading {
            loadingStateView
        } else if let localImage = image.localImage {
            Image(uiImage: localImage)
                .resizable()
                .aspectRatio(contentMode: .fill)
                .clipped()
        } else if image.url.hasPrefix("data:image/") {
            if let data = Data(base64Encoded: String(image.url.dropFirst(22))),
               let uiImage = UIImage(data: data)
            {
                Image(uiImage: uiImage)
                    .resizable()
                    .aspectRatio(contentMode: .fill)
                    .clipped()
            } else {
                errorStateView
            }
        } else if let kfImage = KFImage.from(urlString: image.url) {
            kfImage
                .placeholder {
                    if let messageImage = image as? MessageImage, messageImage.isCompleted {
                        finalImageLoadingView
                    } else {
                        loadingStateView
                    }
                }
                .onFailure { _ in loadingFailed = true }
                .retry(maxCount: 3)
                .fade(duration: 0.25)
                .resizable()
                .aspectRatio(contentMode: .fill)
                .clipped()
        } else {
            errorStateView
        }
    }

    @ViewBuilder
    private var loadingStateView: some View {
        ImageLoadingGIFView(cornerRadius: cornerRadius)
    }

    @ViewBuilder
    private var finalImageLoadingView: some View {
        RoundedRectangle(cornerRadius: cornerRadius)
            .fill(DesignSystem.Colors.backgroundCard)
            .overlay {
                VStack(spacing: DesignSystem.Spacing.xs) {
                    ProgressView()
                        .tint(DesignSystem.Colors.primary)
                        .scaleEffect(0.8)

                    Text("处理中...")
                        .font(DesignSystem.Typography.caption)
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                }
            }
    }

    @ViewBuilder
    private var errorStateView: some View {
        RoundedRectangle(cornerRadius: cornerRadius)
            .fill(DesignSystem.Colors.backgroundCard)
            .overlay {
                VStack(spacing: DesignSystem.Spacing.xs) {
                    Image(systemName: "photo.fill")
                        .font(.title2)
                        .foregroundColor(DesignSystem.Colors.textTertiary)

                    Text("加载失败")
                        .font(DesignSystem.Typography.caption)
                        .foregroundColor(DesignSystem.Colors.textTertiary)
                }
            }
    }
}

// MARK: - 全屏图片查看器

struct ImageGridFullScreenViewer: View {
    let images: [ImageDisplayable]
    let initialIndex: Int
    @Binding var isPresented: Bool

    @State private var currentIndex = 0

    var body: some View {
        VStack {
            topControlBar

            if images.count > 1 {
                TabView(selection: $currentIndex) {
                    ForEach(images.indices, id: \.self) { index in
                        fullScreenImageView(for: images[index])
                            .tag(index)
                    }
                }
                .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
            } else {
                if currentIndex < images.count {
                    fullScreenImageView(for: images[currentIndex])
                }
            }

            bottomInfoView
        }
        .background(Color.black.ignoresSafeArea())
        .onAppear {
            currentIndex = initialIndex
        }
        .statusBarHidden()
    }

    @ViewBuilder
    private var topControlBar: some View {
        HStack {
            Button("关闭") {
                isPresented = false
            }
            .foregroundColor(.white)

            Spacer()

            if images.count > 1 {
                Text("\(currentIndex + 1) / \(images.count)")
                    .foregroundColor(.white)
                    .font(DesignSystem.Typography.body)
            }
        }
        .padding()
    }

    @ViewBuilder
    private var bottomInfoView: some View {
        if images.count > 1 {
            HStack {
                ForEach(images.indices, id: \.self) { index in
                    Circle()
                        .fill(currentIndex == index ? Color.white : Color.white.opacity(0.5))
                        .frame(width: 8, height: 8)
                }
            }
            .padding()
        }
    }

    @ViewBuilder
    private func fullScreenImageView(for image: ImageDisplayable) -> some View {
        if let localImage = image.localImage {
            Image(uiImage: localImage)
                .resizable()
                .aspectRatio(contentMode: .fit)
                .frame(maxWidth: .infinity, maxHeight: .infinity)
        } else if image.url.hasPrefix("data:image/") {
            if let data = Data(base64Encoded: String(image.url.dropFirst(22))),
               let uiImage = UIImage(data: data)
            {
                Image(uiImage: uiImage)
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
            }
        } else if let kfImage = KFImage.from(urlString: image.url) {
            kfImage
                .placeholder {
                    VStack {
                        ProgressView()
                            .tint(.white)
                        Text("加载中...")
                            .foregroundColor(.white)
                    }
                }
                .resizable()
                .aspectRatio(contentMode: .fit)
                .frame(maxWidth: .infinity, maxHeight: .infinity)
        }
    }
}
