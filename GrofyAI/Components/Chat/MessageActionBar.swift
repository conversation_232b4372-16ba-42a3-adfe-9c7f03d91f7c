import SwiftUI

// MARK: - 消息操作栏组件

/// 消息气泡底部的操作按钮栏
/// 包含重试、复制、分享、TTS等功能按钮
struct MessageActionBar: View {
    let message: ChatMessageModel
    let onRetry: (() -> Void)?
    let onCopy: () -> Void

    @EnvironmentObject private var ttsService: TextToSpeechService

    var body: some View {
        HStack(spacing: DesignSystem.Spacing.md) {
            Spacer()

            // 文字转语音按钮
            if !message.isUser, !message.content.isEmpty {
                TTSMessageButton(
                    messageId: message.id.uuidString,
                    text: message.content,
                    ttsService: ttsService
                )
            }

            // 重试按钮 (仅AI消息显示)
            if !message.isUser, let onRetry {
                MessageActionButton(icon: "IconMessageRetry", isSystemIcon: false, action: onRetry)
            }

            // 复制按钮
            MessageActionButton(icon: "IconMessageCopy", isSystemIcon: false, action: onCopy)

            // 分享按钮
            ShareMessageButton(message: message)
        }
        .opacity(0.8)
    }
}

// MARK: - 操作处理扩展

extension MessageActionBar {
    static func extractMainContent(from message: ChatMessageModel) -> String {
        if !message.isUser {
            return message.content
        }
        return message.content
    }

    static func formatMessageForSharing(_ message: ChatMessageModel) -> String {
        let role = message.isUser ? "用户" : "AI"
        let content = extractMainContent(from: message)

        var shareText = "[\(role)] \(content)"

        if !message.isUser, let modelId = message.modelId {
            if let modelInfo = ModelManager.shared.getModelById(modelId) {
                shareText = "[\(role) - \(modelInfo.safeName)] \(content)"
            }
        }

        shareText += "\n---\nFrom MoonvyAI"

        return shareText
    }
}

// MARK: - ShareLink分享按钮组件

struct ShareMessageButton: View {
    let message: ChatMessageModel
    @State private var isPressed = false

    var body: some View {
        ShareLink(item: shareText) {
            Group {
                Image("IconMessageShare")
                    .resizable()
                    .scaledToFit()
                    .frame(width: 20, height: 20)
            }
            .foregroundColor(DesignSystem.Colors.textSecondary)
            .frame(width: 24, height: 24)
            .background(
                Circle()
                    .fill(isPressed ? DesignSystem.Colors.backgroundCard.opacity(0.8) : Color.clear)
            )
            .scaleEffect(isPressed ? 0.9 : 1.0)
        }
        .buttonStyle(.plain)
        .animation(.easeInOut(duration: 0.1), value: isPressed)
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            isPressed = pressing
            if pressing {
                let impactFeedback = UIImpactFeedbackGenerator(style: .light)
                impactFeedback.impactOccurred()
            }
        }, perform: {})
    }

    private var shareText: String {
        return MessageActionBar.formatMessageForSharing(message)
    }
}
