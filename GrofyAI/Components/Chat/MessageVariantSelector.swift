import SwiftUI

struct MessageVariantSelector: View {
    let messageId: UUID
    let currentIndex: Int
    let totalCount: Int
    let onVariantChanged: (Int) -> Void

    var body: some View {
        HStack(spacing: 8) {
            // 左箭头按钮
            Button(action: {
                let newIndex = max(0, currentIndex - 1)
                onVariantChanged(newIndex)
            }) {
                Image(systemName: "chevron.left")
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(currentIndex > 0 ? DesignSystem.Colors.primary : DesignSystem.Colors.textSecondary)
            }
            .disabled(currentIndex <= 0)

            // 变体指示器
            Text("\(currentIndex + 1)/\(totalCount)")
                .font(.system(size: 12, weight: .medium))
                .foregroundColor(DesignSystem.Colors.textSecondary)
                .frame(minWidth: 30)

            // 右箭头按钮
            Button(action: {
                let newIndex = min(totalCount - 1, currentIndex + 1)
                onVariantChanged(newIndex)
            }) {
                Image(systemName: "chevron.right")
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(currentIndex < totalCount - 1 ? DesignSystem.Colors.primary : DesignSystem.Colors
                        .textSecondary
                    )
            }
            .disabled(currentIndex >= totalCount - 1)
        }
        .padding(.horizontal, 8)
        .padding(.vertical, 4)
        .background(
            RoundedRectangle(cornerRadius: 6)
                .fill(DesignSystem.Colors.separator)
        )
        .overlay(
            RoundedRectangle(cornerRadius: 6)
                .stroke(DesignSystem.Colors.border, lineWidth: 1)
        )
    }
}

struct MessageVariantSelector_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: 16) {
            MessageVariantSelector(
                messageId: UUID(),
                currentIndex: 0,
                totalCount: 3,
                onVariantChanged: { _ in }
            )

            MessageVariantSelector(
                messageId: UUID(),
                currentIndex: 1,
                totalCount: 3,
                onVariantChanged: { _ in }
            )

            MessageVariantSelector(
                messageId: UUID(),
                currentIndex: 2,
                totalCount: 3,
                onVariantChanged: { _ in }
            )
        }
        .padding()
        .previewLayout(.sizeThatFits)
    }
}
