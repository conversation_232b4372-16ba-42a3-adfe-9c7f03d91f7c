import SwiftUI

// MARK: - TTS消息按钮

/// TTS按钮，最小化状态订阅
struct TTSMessageButton: View {
    let messageId: String
    let text: String
    @ObservedObject var ttsService: TextToSpeechService
    
    @State private var localState: TTSDisplayState = .idle
    @State private var isPressed = false
    
    // 监听全局当前消息ID
    private var isCurrentMessage: Bool {
        ttsService.currentMessageId == messageId
    }
    
    var body: some View {
        Button(action: handleTap) {
            ZStack {
                // 背景层
                Circle()
                    .fill(isPressed ? Color.gray.opacity(0.2) : Color.clear)
                    .frame(width: 32, height: 32)
                
                // 图标层
                Group {
                    switch localState {
                    case .preparing, .preparingCached, .synthesizing:
                        ProgressView()
                            .scaleEffect(0.7)
                            .progressViewStyle(CircularProgressViewStyle(tint: iconColor))
                    
                    case .playing:
                        Image(systemName: "pause.fill")
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(iconColor)
                    
                    default:
                        Image(systemName: "speaker.wave.2.fill")
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(iconColor)
                    }
                }
            }
            .scaleEffect(isPressed ? 0.9 : 1.0)
        }
        .buttonStyle(.plain)
        .disabled(isProcessing)
        .animation(.easeInOut(duration: 0.1), value: isPressed)
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            isPressed = pressing
        }, perform: {})
        .onChange(of: ttsService.currentMessageId) { newMessageId in
            // 当全局消息ID改变时，如果不是当前消息，立即重置状态
            if newMessageId != messageId && localState != .idle {
                localState = .idle
            }
        }
    }
    
    private var iconColor: Color {
        switch localState {
        case .playing:
            return DesignSystem.Colors.primary
        case .error:
            return .red
        default:
            return DesignSystem.Colors.textSecondary
        }
    }
    
    private var isProcessing: Bool {
        switch localState {
        case .preparing, .preparingCached, .synthesizing:
            return true
        default:
            return false
        }
    }
    
    private func handleTap() {
        Task {
            do {
                if case .playing = localState {
                    await ttsService.stopPlayback(messageId: messageId)
                    localState = .idle
                } else {
                    try await ttsService.synthesizeAndPlay(
                        messageId: messageId,
                        text: text,
                        voice: .xiaoxiaoMultilingual
                    ) { state in
                        Task { @MainActor in
                            self.localState = state
                        }
                    }
                }
            } catch {
                localState = .error(error.localizedDescription)
            }
        }
    }
}

// MARK: - 消息操作栏

struct EnhancedMessageActionBar: View {
    let message: ChatMessageModel
    let onRetry: (() -> Void)?
    let onCopy: () -> Void
    let onShare: () -> Void
    
    // 使用环境对象而不是直接创建
    @EnvironmentObject private var ttsService: TextToSpeechService
    
    var body: some View {
        HStack(spacing: DesignSystem.Spacing.md) {
            Spacer()
            
            // TTS按钮 - 轻量级实现
            if !message.isUser, !message.content.isEmpty {
                TTSMessageButton(
                    messageId: message.id.uuidString,
                    text: message.content,
                    ttsService: ttsService
                )
            }
            
            // 其他按钮保持不变
            if !message.isUser, let onRetry {
                MessageActionButton(icon: "IconMessageRetry", isSystemIcon: false, action: onRetry)
            }
            
            MessageActionButton(icon: "IconMessageCopy", isSystemIcon: false, action: onCopy)
            MessageActionButton(icon: "IconMessageShare", isSystemIcon: false, action: onShare)
        }
        .opacity(0.8)
    }
}