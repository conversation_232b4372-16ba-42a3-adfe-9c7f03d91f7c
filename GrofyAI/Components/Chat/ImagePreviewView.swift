import Kingfisher
import SwiftUI

// MARK: - 图片预览组件

struct ImagePreviewView: View {
    let imageInfo: MessageImage

    var body: some View {
        if imageInfo.fileUrl.hasPrefix("data:image/") {
            // 处理base64数据URL格式的预览图片
            if let url = URL(string: imageInfo.fileUrl),
               let imageData = try? Data(contentsOf: url),
               let uiImage = UIImage(data: imageData)
            {
                Image(uiImage: uiImage)
                    .resizable()
                    .scaledToFit()
                    .cornerRadius(DesignSystem.Rounded.sm)
            } else {
                // 预览加载占位符
                RoundedRectangle(cornerRadius: DesignSystem.Rounded.sm)
                    .fill(DesignSystem.Colors.backgroundPage)
                    .overlay(
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle())
                    )
            }
        } else if imageInfo.fileUrl.isEmpty {
            // 生成中占位符
            RoundedRectangle(cornerRadius: DesignSystem.Rounded.sm)
                .fill(DesignSystem.Colors.backgroundPage)
                .overlay(
                    VStack(spacing: DesignSystem.Spacing.sm) {
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle())
                        Text("图片生成中...")
                            .font(DesignSystem.Typography.caption)
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                    }
                )
        } else {
            KFImage(URL(string: imageInfo.fileUrl))
                .placeholder {
                    RoundedRectangle(cornerRadius: DesignSystem.Rounded.sm)
                        .fill(DesignSystem.Colors.backgroundPage)
                        .overlay(
                            ProgressView()
                                .progressViewStyle(CircularProgressViewStyle())
                        )
                }
                .resizable()
                .scaledToFit()
                .cornerRadius(DesignSystem.Rounded.sm)
        }
    }
}
