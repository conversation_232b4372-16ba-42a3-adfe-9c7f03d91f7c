import SDWebImageSwiftUI
import SwiftUI

// MARK: - 图片加载GIF动画组件

struct ImageLoadingGIFView: View {
    let cornerRadius: CGFloat

    @State private var gifURL: URL?
    @State private var shouldUseGIF = true

    var body: some View {
        RoundedRectangle(cornerRadius: cornerRadius)
            .fill(DesignSystem.Colors.backgroundCard)
            .overlay {
                if shouldUseGIF, let url = gifURL {
                    AnimatedImage(url: url)
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .padding(DesignSystem.Spacing.sm)
                        .clipShape(RoundedRectangle(cornerRadius: cornerRadius))
                } else {
                    fallbackLoadingView
                }
            }
            .onAppear {
                loadGIFResource()
            }
            .onReceive(NotificationCenter.default
                .publisher(for: UIApplication.didReceiveMemoryWarningNotification)
            ) { _ in
                // 内存警告时切换到简单模式
                shouldUseGIF = false
            }
    }

    @ViewBuilder
    private var fallbackLoadingView: some View {
        VStack(spacing: DesignSystem.Spacing.xs) {
            ProgressView()
                .tint(DesignSystem.Colors.primary)
                .scaleEffect(0.8)

            Text("加载中...")
                .font(DesignSystem.Typography.caption)
                .foregroundColor(DesignSystem.Colors.textSecondary)
        }
    }

    private func loadGIFResource() {
        if let assetURL = getGIFFromAssets() {
            gifURL = assetURL
        } else if let bundleURL = Bundle.main.url(forResource: "gif_image_generation_loading", withExtension: "gif") {
            gifURL = bundleURL
        } else {
            shouldUseGIF = false
        }
    }

    private func getGIFFromAssets() -> URL? {
        guard let asset = NSDataAsset(name: "GifImageGenerationLoading") else {
            return nil
        }

        let tempURL = FileManager.default.temporaryDirectory
            .appendingPathComponent("gif_image_loading_\(UUID().uuidString).gif")

        do {
            try asset.data.write(to: tempURL)
            return tempURL
        } catch {
            return nil
        }
    }
}
