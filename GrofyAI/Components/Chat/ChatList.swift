import SwiftUI

// MARK: - 聊天消息列表组件

struct ChatList: View {
    let messages: [ChatMessageModel]
    let isLoading: Bool
    let loadingText: String
    let showSkeleton: Bool
    let onRetryMessage: (UUID) -> Void
    let onCopyMessage: (ChatMessageModel) -> Void
    let onShareMessage: (ChatMessageModel) -> Void
    let onVariantChanged: (UUID, Int) -> Void
    let getVariantInfo: (UUID) -> (current: Int, total: Int)?
    let imageRecognition: Bool

    @State private var isAtBottom = false
    @State private var isUserScrolling = false
    @State private var scrollProxy: ScrollViewProxy?

    init(
        messages: [ChatMessageModel],
        isLoading: Bool = false,
        loadingText: String = "AI正在思考...",
        showSkeleton: Bool = false,
        onRetryMessage: @escaping (UUID) -> Void = { _ in },
        onCopyMessage: @escaping (ChatMessageModel) -> Void = { _ in },
        onShareMessage: @escaping (ChatMessageModel) -> Void = { _ in },
        onVariantChanged: @escaping (UUID, Int) -> Void = { _, _ in },
        getVariantInfo: @escaping (UUID) -> (current: Int, total: Int)? = { _ in nil },
        imageRecognition: Bool = false
    ) {
        self.messages = messages
        self.isLoading = isLoading
        self.loadingText = loadingText
        self.showSkeleton = showSkeleton
        self.onRetryMessage = onRetryMessage
        self.onCopyMessage = onCopyMessage
        self.onShareMessage = onShareMessage
        self.onVariantChanged = onVariantChanged
        self.getVariantInfo = getVariantInfo
        self.imageRecognition = imageRecognition
    }

    var body: some View {
        ScrollViewReader { proxy in
            ScrollView {
                chatContent
            }
            .overlay(alignment: .bottom) {
                if !isAtBottom {
                    BackToBottomButton {
                        scrollToBottom(proxy: scrollProxy, animated: false)
                    }
                    .padding(.bottom, DesignSystem.Spacing.sm)
                }
            }
            .coordinateSpace(name: "scroll")
            .onAppear {
                scrollProxy = proxy
            }
            .onChange(of: messages) { _ in
                performAutoScroll(proxy: proxy)
            }
        }
    }

    @ViewBuilder
    private var chatContent: some View {
        LazyVStack(spacing: 14) {
            if showSkeleton, messages.isEmpty {
                ChatMessageSkeleton()
            } else {
                ForEach(messages) { message in
                    let isLastAIMessage = !message.isUser && isLastAIMessage(message, in: messages)
                    ChatItem(
                        message: message,
                        isLoading: isLoading && isLastAIMessage,
                        onRetry: isLastAIMessage ? { onRetryMessage(message.id) } : nil,
                        onCopy: { onCopyMessage(message) },
                        onVariantChanged: { variantIndex in onVariantChanged(message.id, variantIndex) },
                        variantInfo: getVariantInfo(message.id),
                        imageRecognition: imageRecognition
                    )
                    .id(message.id)
                }

                if isLoading, !showSkeleton {
                    loadingIndicator
                }

                // 底部检测器
                BottomDetector(isAtBottom: $isAtBottom)
            }
        }
        .padding(.vertical, 8)
        .scrollDetector(isAtBottom: $isAtBottom, isUserScrolling: $isUserScrolling)
        .id("bottom-anchor")
    }

    @ViewBuilder
    private var loadingIndicator: some View {
        if let lastMessage = messages.last,
           lastMessage.isUser,
           lastMessage.chatMode != .image
        {
            HStack {
                ProgressView()
                    .scaleEffect(0.8)
                Text(loadingText)
                    .font(.caption)
                    .foregroundColor(.secondary)
                Spacer()
            }
            .padding(.horizontal)
        }
    }

    private func performAutoScroll(proxy: ScrollViewProxy) {
        if let lastMessage = messages.last,
           lastMessage.isUser
        {
            scrollToBottom(proxy: proxy, animated: false)
        } else if isAtBottom, !isUserScrolling {
            if isLoading {
                scrollToBottom(proxy: proxy, animated: false)
            } else {
                scrollToBottom(proxy: proxy, animated: true)
            }
        } else {
            if let firstMessage = messages.first,
               firstMessage.type == .fileAnalysis
            {
                scrollToTop(proxy: proxy, animated: false)
            }
        }
    }

    private func scrollToBottom(proxy: ScrollViewProxy?, animated: Bool) {
        guard let proxy else { return }
        let anchorID = "bottom-anchor"

        if animated {
            withAnimation(.easeInOut(duration: 0.3)) {
                proxy.scrollTo(anchorID, anchor: .bottom)
            }
        } else {
            proxy.scrollTo(anchorID, anchor: .bottom)
        }
    }

    private func scrollToTop(proxy: ScrollViewProxy?, animated: Bool) {
        guard let proxy, let firstMessage = messages.first else { return }

        if animated {
            withAnimation(.easeInOut(duration: 0.3)) {
                proxy.scrollTo(firstMessage.id, anchor: .top)
            }
        } else {
            proxy.scrollTo(firstMessage.id, anchor: .top)
        }
    }

    /// 判断指定索引的消息是否为最后一条AI消息
    private func isLastAIMessage(_ message: ChatMessageModel, in messages: [ChatMessageModel]) -> Bool {
        guard !message.isUser else { return false }

        if isLoading,
           let lastMessage = messages.last,
           lastMessage.isUser
        {
            return false
        }

        if let currentIndex = messages.firstIndex(where: { $0.id == message.id }) {
            for i in (currentIndex + 1)..<messages.count {
                if !messages[i].isUser {
                    return false
                }
            }
        }

        return true
    }
}
