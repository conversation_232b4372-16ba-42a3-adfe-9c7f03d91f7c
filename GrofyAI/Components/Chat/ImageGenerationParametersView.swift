import SwiftUI

// MARK: - 图片生成参数配置视图

struct ImageGenerationParametersView: View {
    @Binding var parameters: ImageGenerationParameters
    @Binding var isVisible: Bool

    let onParameterChange: (ImageGenerationParameters) -> Void

    @State private var selectedTab: ImageParameterTab = .size

    var body: some View {
        if isVisible {
            VStack(spacing: DesignSystem.Spacing.sm) {
                TabSelector(selectedTab: $selectedTab)

                Group {
                    switch selectedTab {
                    case .size:
                        ImageSizeSelector(
                            options: ImageSize.allCases,
                            selection: $parameters.size
                        )
                        .padding(.vertical, DesignSystem.Spacing.md)

                    case .quality:
                        QualitySelector(
                            options: ImageQuality.allCases,
                            selection: $parameters.quality
                        )
                        .padding(.vertical, DesignSystem.Spacing.md)

                    case .format:
                        FormatSelector(
                            options: ImageGenerationFormat.allCases,
                            selection: $parameters.format
                        )
                        .padding(.vertical, DesignSystem.Spacing.md)

                    case .background:
                        BackgroundSelector(
                            options: ImageBackground.allCases,
                            selection: $parameters.background
                        )
                        .padding(.vertical, DesignSystem.Spacing.md)
                    }
                }
                .frame(maxWidth: .infinity)
                .padding(.horizontal, DesignSystem.Spacing.lg)
                .background(DesignSystem.Colors.backgroundCard)
                .cornerRadius(DesignSystem.Rounded.lg)
                .overlay(
                    RoundedRectangle(cornerRadius: DesignSystem.Rounded.lg)
                        .stroke(DesignSystem.Colors.border, lineWidth: DesignSystem.BorderWidth.thin)
                )
                .padding(.horizontal, DesignSystem.Spacing.lg)
            }
            .onChange(of: parameters) { newParameters in
                onParameterChange(newParameters)
            }
        }
    }
}

// MARK: - 参数类型标签

enum ImageParameterTab: String, CaseIterable {
    case size = "尺寸"
    case quality = "质量"
    case format = "格式"
    case background = "背景"
}

// MARK: - Tab 选择器

struct TabSelector: View {
    @Binding var selectedTab: ImageParameterTab

    var body: some View {
        HStack(spacing: 0) {
            ForEach(ImageParameterTab.allCases, id: \.self) { tab in
                Button(action: {
                    withAnimation(.easeInOut(duration: 0.2)) {
                        selectedTab = tab
                    }
                }) {
                    Text(tab.rawValue)
                        .font(DesignSystem.Typography.caption)
                        .fontWeight(.medium)
                        .foregroundColor(selectedTab == tab ? DesignSystem.Colors.primary : DesignSystem.Colors
                            .textSecondary
                        )
                        .padding(.horizontal, DesignSystem.Spacing.md)
                        .padding(.vertical, DesignSystem.Spacing.sm)
                        .frame(maxWidth: .infinity, alignment: .center)
                        .contentShape(Rectangle())
                }
                .buttonStyle(.plain)

                if tab != ImageParameterTab.allCases.last {
                    Divider()
                        .frame(height: 20)
                        .overlay(DesignSystem.Colors.border)
                }
            }
        }
        .background(DesignSystem.Colors.backgroundCard)
        .cornerRadius(DesignSystem.Rounded.lg)
        .overlay(
            RoundedRectangle(cornerRadius: DesignSystem.Rounded.lg)
                .stroke(DesignSystem.Colors.border, lineWidth: DesignSystem.BorderWidth.thin)
        )
        .padding(.horizontal, DesignSystem.Spacing.lg)
    }
}

// MARK: - 参数配置区域组件

struct ParameterSection<Content: View>: View {
    let title: String
    let subtitle: String
    let icon: String
    let content: Content

    init(
        title: String,
        subtitle: String,
        icon: String,
        @ViewBuilder content: () -> Content
    ) {
        self.title = title
        self.subtitle = subtitle
        self.icon = icon
        self.content = content()
    }

    var body: some View {
        VStack(spacing: DesignSystem.Spacing.sm) {
            ParameterSectionHeader(title: title, subtitle: subtitle, icon: icon)

            content
        }
    }
}

// MARK: - 参数区域标题组件

struct ParameterSectionHeader: View {
    let title: String
    let subtitle: String
    let icon: String

    var body: some View {
        HStack {
            Image(systemName: icon)
                .foregroundColor(DesignSystem.Colors.primary)
                .font(.system(size: DesignSystem.FontSize.lg, weight: .medium))
                .frame(width: 20, height: 20)

            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(DesignSystem.Typography.caption)
                    .fontWeight(.semibold)
                    .foregroundColor(DesignSystem.Colors.textPrimary)

                Text(subtitle)
                    .font(DesignSystem.Typography.cardSubtitle)
                    .foregroundColor(DesignSystem.Colors.textSecondary)
            }

            Spacer()
        }
        .padding(.vertical, DesignSystem.Spacing.xs)
    }
}

// MARK: - 图片尺寸选择器

struct ImageSizeSelector: View {
    let options: [ImageSize]
    @Binding var selection: ImageSize

    var body: some View {
        HStack(spacing: DesignSystem.Spacing.md) {
            ForEach(options, id: \.rawValue) { option in
                Button(action: {
                    selection = option
                }) {
                    VStack(spacing: DesignSystem.Spacing.xs) {
                        // 纵横比图标
                        RoundedRectangle(cornerRadius: 4)
                            .fill(selection == option ? DesignSystem.Colors.primary : DesignSystem.Colors
                                .backgroundPage
                            )
                            .frame(width: getIconSize(for: option).width, height: getIconSize(for: option).height)
                            .overlay(
                                RoundedRectangle(cornerRadius: 4)
                                    .stroke(
                                        selection == option ? DesignSystem.Colors.primary : DesignSystem.Colors.border,
                                        lineWidth: selection == option ? 2 : 1
                                    )
                            )

                        // 标签
                        Text(option.displayName)
                            .font(DesignSystem.Typography.caption)
                            .fontWeight(.medium)
                            .foregroundColor(selection == option ? DesignSystem.Colors.primary : DesignSystem.Colors
                                .textSecondary
                            )
                    }
                    .padding(.horizontal, DesignSystem.Spacing.sm)
                    .padding(.vertical, DesignSystem.Spacing.sm)
                    .background(
                        selection == option ?
                            DesignSystem.Colors.lightBlue :
                            DesignSystem.Colors.backgroundCard
                    )
                    .cornerRadius(DesignSystem.Rounded.sm)
                }
                .buttonStyle(.plain)
            }
        }
        .frame(maxWidth: .infinity, alignment: .center)
        .padding(.horizontal, DesignSystem.Spacing.sm)
    }

    private func getIconSize(for option: ImageSize) -> CGSize {
        switch option {
        case .square:
            return CGSize(width: 32, height: 32)
        case .landscape:
            return CGSize(width: 40, height: 24)
        case .portrait:
            return CGSize(width: 24, height: 40)
        }
    }
}

// MARK: - 质量选择器

struct QualitySelector: View {
    let options: [ImageQuality]
    @Binding var selection: ImageQuality

    var body: some View {
        HStack(spacing: DesignSystem.Spacing.md) {
            ForEach(options, id: \.rawValue) { option in
                Button(action: {
                    withAnimation(.easeInOut(duration: 0.2)) {
                        selection = option
                    }
                }) {
                    Text(option.displayName)
                        .font(DesignSystem.Typography.caption)
                        .fontWeight(.medium)
                        .foregroundColor(selection == option ? .white : DesignSystem.Colors.textSecondary)
                        .padding(.horizontal, DesignSystem.Spacing.lg)
                        .padding(.vertical, DesignSystem.Spacing.md)
                        .background(
                            RoundedRectangle(cornerRadius: DesignSystem.Rounded.sm)
                                .fill(selection == option ? DesignSystem.Colors.primary : DesignSystem.Colors
                                    .backgroundPage
                                )
                        )
                        .overlay(
                            RoundedRectangle(cornerRadius: DesignSystem.Rounded.sm)
                                .stroke(
                                    selection == option ? DesignSystem.Colors.primary : DesignSystem.Colors.border,
                                    lineWidth: DesignSystem.BorderWidth.thin
                                )
                        )
                }
                .buttonStyle(.plain)
            }
        }
        .frame(maxWidth: .infinity, alignment: .center)
        .padding(.horizontal, DesignSystem.Spacing.sm)
    }

    private func getQualityIcon(for quality: ImageQuality) -> String {
        switch quality {
        case .low:
            return "1.circle"
        case .medium:
            return "2.circle"
        case .high:
            return "3.circle"
        }
    }
}

// MARK: - 格式选择器

struct FormatSelector: View {
    let options: [ImageGenerationFormat]
    @Binding var selection: ImageGenerationFormat

    var body: some View {
        HStack(spacing: DesignSystem.Spacing.md) {
            ForEach(options, id: \.rawValue) { option in
                Button(action: {
                    withAnimation(.easeInOut(duration: 0.2)) {
                        selection = option
                    }
                }) {
                    Text(option.displayName)
                        .font(DesignSystem.Typography.caption)
                        .fontWeight(.medium)
                        .foregroundColor(selection == option ? .white : DesignSystem.Colors.textSecondary)
                        .padding(.horizontal, DesignSystem.Spacing.lg)
                        .padding(.vertical, DesignSystem.Spacing.md)
                        .background(
                            RoundedRectangle(cornerRadius: DesignSystem.Rounded.sm)
                                .fill(selection == option ? DesignSystem.Colors.primary : DesignSystem.Colors
                                    .backgroundPage
                                )
                        )
                        .overlay(
                            RoundedRectangle(cornerRadius: DesignSystem.Rounded.sm)
                                .stroke(
                                    selection == option ? DesignSystem.Colors.primary : DesignSystem.Colors.border,
                                    lineWidth: DesignSystem.BorderWidth.thin
                                )
                        )
                }
                .buttonStyle(.plain)
            }
        }
        .frame(maxWidth: .infinity, alignment: .center)
        .padding(.horizontal, DesignSystem.Spacing.sm)
    }
}

// MARK: - 背景选择器

struct BackgroundSelector: View {
    let options: [ImageBackground]
    @Binding var selection: ImageBackground

    var body: some View {
        HStack(spacing: DesignSystem.Spacing.md) {
            ForEach(options, id: \.rawValue) { option in
                Button(action: {
                    withAnimation(.easeInOut(duration: 0.2)) {
                        selection = option
                    }
                }) {
                    HStack(spacing: DesignSystem.Spacing.xs) {
                        // 背景图标
                        Image(systemName: getBackgroundIcon(for: option))
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(selection == option ? .white : DesignSystem.Colors.textSecondary)

                        Text(option.displayName)
                            .font(DesignSystem.Typography.caption)
                            .fontWeight(.medium)
                            .foregroundColor(selection == option ? .white : DesignSystem.Colors.textSecondary)
                    }
                    .padding(.horizontal, DesignSystem.Spacing.lg)
                    .padding(.vertical, DesignSystem.Spacing.md)
                    .background(
                        RoundedRectangle(cornerRadius: DesignSystem.Rounded.sm)
                            .fill(selection == option ? DesignSystem.Colors.primary : DesignSystem.Colors
                                .backgroundPage
                            )
                    )
                    .overlay(
                        RoundedRectangle(cornerRadius: DesignSystem.Rounded.sm)
                            .stroke(
                                selection == option ? DesignSystem.Colors.primary : DesignSystem.Colors.border,
                                lineWidth: DesignSystem.BorderWidth.thin
                            )
                    )
                }
                .buttonStyle(.plain)
            }
        }
        .frame(maxWidth: .infinity, alignment: .center)
        .padding(.horizontal, DesignSystem.Spacing.sm)
    }

    private func getBackgroundIcon(for background: ImageBackground) -> String {
        switch background {
        case .opaque:
            return "rectangle.fill"
        case .transparent:
            return "rectangle.dashed"
        }
    }
}
