import AVFoundation
import SwiftUI

// MARK: - 语音波形

struct VoiceWaveDisplay: View {
    @ObservedObject var audioManager: AudioLevelManager
    let onClose: () -> Void
    let onConfirm: () -> Void
    let isProcessing: Bool
    
    var body: some View {
        HStack(spacing: DesignSystem.Spacing.sm) {
            Button(action: onClose) {
                Image(systemName: "xmark")
                    .font(.system(size: 18, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
                    .frame(width: 28, height: 28)
                    .contentShape(Circle())
            }
            
            VoiceWaveAnimation(audioManager: audioManager)
                .frame(maxWidth: .infinity)
            
            But<PERSON>(action: onConfirm) {
                Group {
                    if isProcessing {
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle(tint: .white))
                            .scaleEffect(0.6)
                    } else {
                        Image(systemName: "checkmark")
                            .font(.system(size: 14, weight: .bold))
                    }
                }
                .foregroundColor(.white)
                .frame(width: 28, height: 28)
                .background(
                    Circle()
                        .fill(DesignSystem.Colors.primary)
                )
                .opacity(isProcessing ? 0.7 : 1.0)
                .scaleEffect(isProcessing ? 0.95 : 1.0)
                .animation(.easeInOut(duration: 0.15), value: isProcessing)
            }
            .disabled(isProcessing)
        }
        .frame(maxHeight: .infinity)
    }
}

class AudioLevelManager: ObservableObject {
    @Published var averageLevel: Float = 0.0
    @Published var isRecording = false

    private var simulationTimer: Timer?

    private func simulateAudioLevel() {
        let baseLevel: Float = 0.2
        let variableLevel = Float.random(in: 0.1...0.8)
        let smoothedLevel = baseLevel + variableLevel * 0.6

        averageLevel = smoothedLevel
    }

    func startMonitoring() {
        guard simulationTimer == nil else { return }

        isRecording = true

        // 每100ms更新一次音频等级
        simulationTimer = Timer.scheduledTimer(withTimeInterval: 0.1, repeats: true) { _ in
            self.simulateAudioLevel()
        }
    }

    func stopMonitoring() {
        simulationTimer?.invalidate()
        simulationTimer = nil
        isRecording = false
        averageLevel = 0.0
    }

    deinit {
        stopMonitoring()
    }
}

// MARK: - 语音波形动画组件

struct VoiceWaveAnimation: View {
    @ObservedObject var audioManager: AudioLevelManager
    @State private var animationPhases: [Double] = []
    @State private var animationTimer: Timer?
    @State private var containerWidth: CGFloat = 0
    
    private let barWidth: CGFloat = 3
    private let barSpacing: CGFloat = 4
    private let maxHeight: CGFloat = 24
    private let minHeight: CGFloat = 6
    
    // 动态计算波形条数量
    private var barCount: Int {
        guard containerWidth > 0 else { return 0 }
        let totalBarWidth = barWidth + barSpacing
        let availableWidth = containerWidth - barSpacing
        return max(5, Int(availableWidth / totalBarWidth))
    }
    
    var body: some View {
        GeometryReader { geometry in
            HStack(spacing: barSpacing) {
                if barCount > 0 {
                    ForEach(0..<barCount, id: \.self) { index in
                        WaveFormBar(
                            audioLevel: audioManager.averageLevel,
                            phase: index < animationPhases.count ? animationPhases[index] : 0,
                            index: index,
                            totalBars: barCount,
                            barWidth: barWidth,
                            maxHeight: maxHeight,
                            minHeight: minHeight
                        )
                    }
                }
            }
            .frame(maxWidth: .infinity, maxHeight: .infinity)
            .onAppear {
                containerWidth = geometry.size.width
                setupAnimationPhases()
                startAnimation()
            }
            .onChange(of: geometry.size.width) { newWidth in
                containerWidth = newWidth
                setupAnimationPhases()
            }
            .onDisappear {
                stopAnimation()
            }
        }
        .frame(height: maxHeight)
    }
    
    private func setupAnimationPhases() {
        animationPhases = Array(repeating: 0, count: barCount)
    }
    
    private func startAnimation() {
        animationTimer = Timer.scheduledTimer(withTimeInterval: 0.08, repeats: true) { _ in
            for i in 0..<barCount {
                withAnimation(.easeInOut(duration: 0.5 + Double(i) * 0.05)) {
                    if i < animationPhases.count {
                        animationPhases[i] = Double.random(in: 0...1)
                    }
                }
            }
        }
    }
    
    private func stopAnimation() {
        animationTimer?.invalidate()
        animationTimer = nil
        
        for i in 0..<animationPhases.count {
            withAnimation(.easeOut(duration: 0.3)) {
                animationPhases[i] = 0
            }
        }
    }
}

// MARK: - 波形条

private struct WaveFormBar: View {
    let audioLevel: Float
    let phase: Double
    let index: Int
    let totalBars: Int
    let barWidth: CGFloat
    let maxHeight: CGFloat
    let minHeight: CGFloat
    
    private var barHeight: CGFloat {
        // 中间的条形更高，两边逐渐降低
        let centerIndex = Double(totalBars - 1) / 2
        let distanceFromCenter = abs(Double(index) - centerIndex)
        let centerFactor = 1.0 - (distanceFromCenter / centerIndex) * 0.3
        
        // 基于音频级别和动画相位计算高度
        let audioFactor = CGFloat(audioLevel) * 0.7 + 0.3
        let phaseFactor = CGFloat(phase) * 0.4 + 0.6
        
        let height = minHeight + (maxHeight - minHeight) * audioFactor * phaseFactor * CGFloat(centerFactor)
        
        return height
    }
    
    var body: some View {
        RoundedRectangle(cornerRadius: barWidth / 2)
            .fill(DesignSystem.Colors.primary)
            .frame(width: barWidth, height: barHeight)
            .animation(.spring(response: 0.4, dampingFraction: 0.8), value: barHeight)
    }
}
