import SwiftUI

// MARK: - 消息操作按钮组件

/// 消息气泡底部的操作按钮
/// 支持重试、复制、分享等功能，集成设计系统样式
struct MessageActionButton: View {
    let icon: String
    let isSystemIcon: Bool
    let action: () -> Void
    @State private var isPressed = false

    init(icon: String, isSystemIcon: Bool = true, action: @escaping () -> Void) {
        self.icon = icon
        self.isSystemIcon = isSystemIcon
        self.action = action
    }

    var body: some View {
        Button(action: handleButtonTap) {
            Group {
                if isSystemIcon {
                    Image(systemName: icon)
                        .font(.system(size: DesignSystem.FontSize.lg, weight: .medium))
                } else {
                    Image(icon)
                        .resizable()
                        .scaledToFit()
                        .frame(width: 20, height: 20)
                }
            }
            .foregroundColor(DesignSystem.Colors.textSecondary)
            .frame(width: 24, height: 24)
            .background(
                Circle()
                    .fill(isPressed ? DesignSystem.Colors.backgroundCard.opacity(0.8) : Color.clear)
            )
            .scaleEffect(isPressed ? 0.9 : 1.0)
        }
        .buttonStyle(.plain)
        .animation(.easeInOut(duration: 0.1), value: isPressed)
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            isPressed = pressing
        }, perform: {})
    }

    private func handleButtonTap() {
        // 触觉反馈
        let impactFeedback = UIImpactFeedbackGenerator(style: .light)
        impactFeedback.impactOccurred()

        // 执行操作
        action()
    }
}
