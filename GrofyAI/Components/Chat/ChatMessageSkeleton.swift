import SwiftUI

// MARK: - 聊天消息骨架屏组件

struct ChatMessageSkeleton: View {
    var body: some View {
        VStack(spacing: 14) {
            // 显示3-4条骨架消息，模拟真实的聊天记录
            ForEach(0..<4, id: \.self) { index in
                if index % 2 == 0 {
                    // 用户消息骨架屏
                    UserMessageSkeletonView()
                } else {
                    // AI消息骨架屏
                    AIMessageSkeletonView()

                }
            }
        }
        .padding(.vertical, 8)
        .redacted(reason: .placeholder)
    }
}

// MARK: - AI消息骨架屏

private struct AIMessageSkeletonView: View {
    var body: some View {
        HStack(alignment: .top, spacing: DesignSystem.Spacing.sm) {
            VStack(alignment: .leading, spacing: 0) {
                // 模型信息头部
                HStack(spacing: DesignSystem.Spacing.sm) {
                    // 模型图标
                    RoundedRectangle(cornerRadius: 2)
                        .fill(DesignSystem.Colors.backgroundCard)
                        .frame(width: 20, height: 20)
                    
                    // 模型名称
                    RoundedRectangle(cornerRadius: 4)
                        .fill(DesignSystem.Colors.backgroundCard)
                        .frame(width: 80, height: 14)
                    
                    Spacer()
                }
                .padding(.horizontal, DesignSystem.Spacing.lg)
                .padding(.top, DesignSystem.Spacing.md)
                
                // 消息内容区域
                VStack(alignment: .leading, spacing: DesignSystem.Spacing.sm) {
                    // 模拟多行文本内容
                    VStack(alignment: .leading, spacing: 6) {
                        // 第一行（较长）
                        RoundedRectangle(cornerRadius: 4)
                            .fill(DesignSystem.Colors.backgroundCard)
                            .frame(height: 16)
                            .frame(maxWidth: .infinity, alignment: .leading)
                        
                        // 第二行（中等长度）
                        RoundedRectangle(cornerRadius: 4)
                            .fill(DesignSystem.Colors.backgroundCard)
                            .frame(height: 16)
                            .frame(maxWidth: .infinity, alignment: .leading)
                            .scaleEffect(x: 0.8, anchor: .leading)
                        
                        // 第三行（较短）
                        RoundedRectangle(cornerRadius: 4)
                            .fill(DesignSystem.Colors.backgroundCard)
                            .frame(height: 16)
                            .frame(maxWidth: .infinity, alignment: .leading)
                            .scaleEffect(x: 0.6, anchor: .leading)
                    }
                }
                .padding(.horizontal, DesignSystem.Spacing.lg)
                .padding(.bottom, DesignSystem.Spacing.lg)
            }
            .background(DesignSystem.Colors.backgroundCard)
            .clipShape(
                UnevenRoundedRectangle(
                    topLeadingRadius: 0,
                    bottomLeadingRadius: DesignSystem.Rounded.lg,
                    bottomTrailingRadius: DesignSystem.Rounded.lg,
                    topTrailingRadius: DesignSystem.Rounded.lg
                )
            )
            
            Spacer()
        }
        .padding(.horizontal)
    }
}

// MARK: - 用户消息骨架屏

private struct UserMessageSkeletonView: View {
    var body: some View {
        HStack(alignment: .top, spacing: DesignSystem.Spacing.sm) {
            Spacer()
            
            VStack(alignment: .trailing, spacing: 0) {
                // 模拟用户消息内容（通常较短）
                VStack(alignment: .trailing, spacing: 6) {
                    // 第一行（中等长度）
                    RoundedRectangle(cornerRadius: 4)
                        .fill(DesignSystem.Colors.primary.opacity(0.3))
                        .frame(height: 16)
                        .frame(maxWidth: .infinity, alignment: .trailing)
                        .scaleEffect(x: 0.7, anchor: .trailing)
                    
                    // 第二行（较短）
                    RoundedRectangle(cornerRadius: 4)
                        .fill(DesignSystem.Colors.primary.opacity(0.3))
                        .frame(height: 16)
                        .frame(maxWidth: .infinity, alignment: .trailing)
                        .scaleEffect(x: 0.5, anchor: .trailing)
                }
                .padding(.horizontal, DesignSystem.Spacing.lg)
                .padding(.vertical, DesignSystem.Spacing.md)
                .background(DesignSystem.Colors.primary.opacity(0.1))
                .clipShape(
                    UnevenRoundedRectangle(
                        topLeadingRadius: DesignSystem.Rounded.lg,
                        bottomLeadingRadius: DesignSystem.Rounded.lg,
                        bottomTrailingRadius: DesignSystem.Rounded.lg,
                        topTrailingRadius: 0
                    )
                )
            }
        }
        .padding(.horizontal)
    }
}

// MARK: - 预览

#Preview {
    VStack {
        Text("聊天消息骨架屏预览")
            .font(.headline)
            .padding()
        
        ScrollView {
            ChatMessageSkeleton()
        }
        .background(DesignSystem.Colors.backgroundPage)
    }
}
