import SwiftUI

/// 刷新状态指示器组件
struct RefreshStatusIndicator: View {
    let refreshState: EnhancedRefreshController.RefreshState
    let pullProgress: CGFloat

    var body: some View {
        HStack(spacing: DesignSystem.Spacing.sm) {
            statusIcon

            statusText
        }
        .padding(.vertical, DesignSystem.Spacing.sm)
        .opacity(shouldShow ? 1.0 : 0.0)
        .animation(.easeInOut(duration: 0.2), value: refreshState)
    }

    // MARK: - 计算属性

    private var shouldShow: Bool {
        switch refreshState {
        case .idle:
            return false
        case .completed, .failed, .pulling, .readyToRefresh, .refreshing:
            return true
        }
    }

    @ViewBuilder
    private var statusIcon: some View {
        switch refreshState {
        case .idle:
            EmptyView()

        case .pulling, .readyToRefresh:
            Image(systemName: "arrow.down")
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(DesignSystem.Colors.textSecondary)
                .rotationEffect(.degrees(pullProgress >= 1.0 ? 180 : 0))
                .animation(.spring(response: 0.3, dampingFraction: 0.8), value: pullProgress)

        case .refreshing:
            ProgressView()
                .scaleEffect(0.8)
                .progressViewStyle(CircularProgressViewStyle(tint: DesignSystem.Colors.primary))

        case .completed:
            Image(systemName: "checkmark.circle.fill")
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(.green)
                .scaleEffect(1.2)
                .animation(.spring(response: 0.3, dampingFraction: 0.6), value: refreshState)

        case .failed:
            Image(systemName: "exclamationmark.circle.fill")
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(.red)
        }
    }

    @ViewBuilder
    private var statusText: some View {
        switch refreshState {
        case .idle:
            EmptyView()

        case .pulling:
            Text(pullProgress >= 1.0 ? "松开即可刷新" : "下拉刷新")
                .font(DesignSystem.Typography.caption)
                .foregroundColor(DesignSystem.Colors.textSecondary)

        case .readyToRefresh:
            Text("松开即可刷新")
                .font(DesignSystem.Typography.caption)
                .foregroundColor(DesignSystem.Colors.primary)
                .fontWeight(.medium)

        case .refreshing:
            Text("正在刷新...")
                .font(DesignSystem.Typography.caption)
                .foregroundColor(DesignSystem.Colors.textSecondary)

        case .completed:
            Text("刷新完成")
                .font(DesignSystem.Typography.caption)
                .foregroundColor(.green)
                .fontWeight(.medium)

        case .failed:
            Text("刷新失败")
                .font(DesignSystem.Typography.caption)
                .foregroundColor(.red)
        }
    }
}
