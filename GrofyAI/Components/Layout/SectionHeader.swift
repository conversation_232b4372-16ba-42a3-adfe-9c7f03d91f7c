//
//  SectionHeader.swift
//  GrofyAI
//
//  Created by kissy on 2025/6/13.
//

import SwiftUI

struct SectionHeader: View {
    
    // MARK: - Properties
    
    // 主标题
    let title: String
    
    // 弹窗（Alert）的标题
    let helpTitle: String?
    
    // 弹窗（Alert）的详细信息
    let helpMessage: String?
    let custom: Bool?
    
    // 使用 @State 来管理弹窗的显示/隐藏，这是这个视图自己的内部状态
    @State private var showingAlert = false
    
    init(
        title: String,
         helpTitle: String? = nil,
         helpMessage: String? = nil,
        custom: Bool? = false
    ) {
        self.title = title
        self.helpTitle = helpTitle
        self.helpMessage = helpMessage
        self.custom = custom
    }
    
    
    // MARK: - Body
    
    var body: some View {
        HStack(spacing: 8) {
            
            if let _ = custom , custom! == true {
                Text(title)
                    .foregroundColor(DesignSystem.Colors.textPrimary)
            } else {
                Text(title)
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                    .font(.system(size: 14, weight: .medium))
            }
                
            
            // 只有当 helpTitle 和 helpMessage 都存在时，才显示帮助按钮
            if let helpTitle = helpTitle, let helpMessage = helpMessage {
                Button(action: {
                    // 点击按钮时，将 showingAlert 设置为 true，从而触发 .alert
                    self.showingAlert = true
                }) {
                    Image(systemName: "questionmark.circle")
                        .font(.system(size:DesignSystem.FontSize.md))
                        .foregroundColor(.gray.opacity(0.8))
                }
                .alert(helpTitle, isPresented: $showingAlert) {
                    // 这里是按钮 actions
                    Button("确定", role: .cancel) {} // .default 按钮可以简化为这样
                } message: {
                    // 这里是消息 message，它是一个 ViewBuilder
                    // 在这里对 Text 进行修饰是有效的！
                    Text(helpMessage)
                }
            }
            
            Spacer()
        }
    }
}
