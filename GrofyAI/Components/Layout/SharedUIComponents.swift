import SwiftUI

// MARK: - 共享UI组件

/// 圆形进度视图
/// 用于显示TTS合成进度等场景
struct CircularProgressView: View {
    let progress: Float
    let color: Color

    var body: some View {
        ZStack {
            Circle()
                .stroke(color.opacity(0.3), lineWidth: 2)

            Circle()
                .trim(from: 0, to: CGFloat(progress))
                .stroke(color, style: StrokeStyle(lineWidth: 2, lineCap: .round))
                .rotationEffect(.degrees(-90))
                .animation(.easeInOut(duration: 0.2), value: progress)
        }
        .frame(width: 16, height: 16)
    }
}
