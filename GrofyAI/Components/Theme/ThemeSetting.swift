import FlowStacks
import SwiftUI

struct ThemeSettingView: View {
    @AppStorage("colorScheme") private var colorSchemeOption: ColorSchemeOption = .system
    @EnvironmentObject var navigator: FlowPathNavigator

    var body: some View {
        VStack(spacing: 0) {
            Form {
                Section(header: Text("外观")) {
                    Picker("主题", selection: $colorSchemeOption) {
                        ForEach(ColorSchemeOption.allCases, id: \.self) { option in
                            Text(option.rawValue).tag(option)
                        }
                    }
                    .pickerStyle(.inline)
                    .labelsHidden()
                }
            }
            .scrollContentBackground(.hidden)
            .background(DesignSystem.Colors.backgroundPage)
        }
        .navigationBarBackButtonHidden()
        .navigationBarTitleDisplayMode(.inline)
        .toolbar {
            ToolbarItem(placement: .topBarLeading) {
                BackButton(onBack: handleBackTap)
            }
            ToolbarItem(placement: .principal) {
                Text("主题")
                    .font(.system(size: DesignSystem.FontSize.lg, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.textPrimary)
            }
        }
        .hideNavigationBarLine(backgroundColor: DesignSystem.Colors.backgroundPage)
        .background(DesignSystem.Colors.backgroundPage)
    }

    private func handleBackTap() {
        navigator.pop()
    }
}
