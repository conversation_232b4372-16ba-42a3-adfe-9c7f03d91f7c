import Kingfisher
import SwiftUI

// MARK: - 通用模型图标组件

struct ModelIconView: View {
    let iconURL: String?
    let provider: String?
    let size: CGFloat
    let cornerRadius: CGFloat

    init(
        iconURL: String?,
        provider: String?,
        size: CGFloat = 40,
        cornerRadius: CGFloat = 8
    ) {
        self.iconURL = iconURL
        self.provider = provider
        self.size = size
        self.cornerRadius = cornerRadius
    }

    var body: some View {
        Group {
            if let iconURL, !iconURL.isEmpty {
                KFImage(URL(string: iconURL))
                    .placeholder {
                        fallbackIconView
                    }
                    .onFailure { _ in
                        // 图片加载失败时的处理
                    }
                    .fade(duration: 0.2)
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(width: size, height: size)
                    .background(DesignSystem.Colors.backgroundCard)
                    .cornerRadius(cornerRadius)
                    .clipped()
            } else {
                fallbackIconView
                    .background(DesignSystem.Colors.backgroundCard)
                    .cornerRadius(cornerRadius)
            }
        }
    }

    private var fallbackIconView: some View {
        BrandLogoView(size: .custom(size), style: .plain)
            .foregroundColor(DesignSystem.Colors.textTertiary)
    }
}

// MARK: - 紧凑模型图标

struct CompactModelIconView: View {
    let iconURL: String?
    let provider: String?

    var body: some View {
        Group {
            if let iconURL, !iconURL.isEmpty {
                KFImage(URL(string: iconURL))
                    .placeholder {
                        fallbackIconView
                    }
                    .onFailure { _ in
                        // 图片加载失败时显示fallback
                    }
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(width: 20, height: 20)
                    .cornerRadius(4)
                    .clipped()
            } else {
                fallbackIconView
            }
        }
        .frame(width: 20, height: 20)
        .background(DesignSystem.Colors.backgroundCard)
        .cornerRadius(4)
    }

    private var fallbackIconView: some View {
        BrandLogoView.icon()
            .foregroundColor(DesignSystem.Colors.textTertiary)
    }
}
