import SwiftUI

// MARK: - 功能入口卡片组件

struct FeatureCard: View {
    let feature: FeatureItem
    let onTap: () -> Void

    init(
        feature: FeatureItem,
        onTap: @escaping () -> Void
    ) {
        self.feature = feature
        self.onTap = onTap
    }

    var body: some View {
        Button(action: onTap) {
            cardContent
        }
        .buttonStyle(FeatureCardButtonStyle())
    }

    @ViewBuilder
    private var cardContent: some View {
        VStack(alignment: .leading, spacing: 8) {
            // 标题和图标行
            HStack(alignment: .center) {
                Text(feature.title)
                    .cardTitleStyle()
                    .lineLimit(1)
                    .minimumScaleFactor(0.9)
                    .layoutPriority(1)

                Spacer()

                Image(systemName: feature.iconName)
                    .font(.system(size: DesignSystem.FontSize.lg, weight: DesignSystem.FontWeight.medium))
                    .foregroundColor(feature.iconColor)
                    .frame(width: 24, height: 24)
            }

            // 副标题
            if let subtitle = feature.subtitle {
                Text(subtitle)
                    .cardSubtitleStyle()
                    .lineLimit(3)
                    .multilineTextAlignment(.leading)
                    .fixedSize(horizontal: false, vertical: true)
            }
        }
        .padding(12)
        .frame(maxWidth: .infinity, alignment: .topLeading)
        .background(DesignSystem.Colors.backgroundCard)
        .cornerRadius(8)
    }
}

// MARK: - 高度匹配的功能卡片组件

struct HeightMatchingFeatureCard: View {
    let feature: FeatureItem
    let targetHeight: CGFloat?
    let onTap: () -> Void

    init(
        feature: FeatureItem,
        targetHeight: CGFloat? = nil,
        onTap: @escaping () -> Void
    ) {
        self.feature = feature
        self.targetHeight = targetHeight
        self.onTap = onTap
    }

    var body: some View {
        Button(action: onTap) {
            cardContent
        }
        .buttonStyle(FeatureCardButtonStyle())
    }

    @ViewBuilder
    private var cardContent: some View {
        VStack(alignment: .leading, spacing: 8) {
            // 标题和图标行 - 使用Spacer实现两端对齐
            HStack(alignment: .center) {
                Text(feature.title)
                    .cardTitleStyle()
                    .lineLimit(1)
                    .minimumScaleFactor(0.9)
                    .layoutPriority(1)

                Spacer()

                Image(systemName: feature.iconName)
                    .font(.system(size: DesignSystem.FontSize.lg, weight: DesignSystem.FontWeight.medium))
                    .foregroundColor(feature.iconColor)
                    .frame(width: 24, height: 24)
            }

            if let subtitle = feature.subtitle {
                Text(subtitle)
                    .cardSubtitleStyle()
                    .lineLimit(3)
                    .multilineTextAlignment(.leading)
                    .fixedSize(horizontal: false, vertical: true)
            }

            // 如果设置了目标高度，使用Spacer填充剩余空间
            if targetHeight != nil {
                Spacer(minLength: 0)
            }
        }
        .padding(DesignSystem.Spacing.md)
        .frame(maxWidth: .infinity, alignment: .topLeading)
        .frame(height: targetHeight)
        .background(DesignSystem.Colors.backgroundCard)
        .cornerRadius(DesignSystem.Rounded.sm)
    }
}

// MARK: - 自定义按钮样式

struct FeatureCardButtonStyle: ButtonStyle {
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .scaleEffect(configuration.isPressed ? 0.98 : 1.0)
            .opacity(configuration.isPressed ? 0.8 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: configuration.isPressed)
    }
}
