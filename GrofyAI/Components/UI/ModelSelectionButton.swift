import SwiftUI

// MARK: - 模型选择按钮组件

struct ModelSelectionButton: View {
    @ObservedObject private var modelManager = ModelManager.shared
    let onTap: () -> Void
    let style: ModelSelectionButtonStyle

    enum ModelSelectionButtonStyle {
        case compact // 紧凑样式，用于首页导航栏
        case standard // 标准样式，用于对话页面工具栏

        var backgroundColor: Color {
            switch self {
            case .compact:
                return DesignSystem.Colors.backgroundCard
            case .standard:
                return Color.clear
            }
        }

        var cornerRadius: CGFloat {
            switch self {
            case .compact:
                return DesignSystem.Rounded.lg
            case .standard:
                return 0
            }
        }

        var padding: (horizontal: CGFloat, vertical: CGFloat) {
            switch self {
            case .compact:
                return (DesignSystem.Spacing.md, DesignSystem.Spacing.xs)
            case .standard:
                return (0, 0)
            }
        }
    }

    init(style: ModelSelectionButtonStyle = .standard, onTap: @escaping () -> Void) {
        self.style = style
        self.onTap = onTap
    }

    var body: some View {
        Button(action: onTap) {
            let displayInfo = modelManager.getSelectedModelDisplayInfo()

            HStack(alignment: .center, spacing: DesignSystem.Spacing.sm) {
                if let selectedModel = modelManager.selectedModel {
                    CompactModelIconView(
                        iconURL: selectedModel.icon,
                        provider: selectedModel.provider
                    )
                } else {
                    BrandLogoView.icon()
                }

                Text(displayInfo.name)
                    .font(.system(size: DesignSystem.FontSize.lg, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                    .lineLimit(1)

                Image(systemName: "chevron.down")
                    .fontSM(weight: DesignSystem.FontWeight.medium)
                    .foregroundColor(DesignSystem.Colors.textSecondary)
            }
            .padding(.horizontal, style.padding.horizontal)
            .padding(.vertical, style.padding.vertical)
            .background(style.backgroundColor)
            .cornerRadius(style.cornerRadius)
        }
        .buttonStyle(.plain)
    }
}

extension ModelSelectionButton {
    /// 创建紧凑样式的模型选择按钮（用于首页导航栏）
    static func compact(onTap: @escaping () -> Void) -> ModelSelectionButton {
        ModelSelectionButton(style: .compact, onTap: onTap)
    }

    /// 创建标准样式的模型选择按钮（用于对话页面工具栏）
    static func standard(onTap: @escaping () -> Void) -> ModelSelectionButton {
        ModelSelectionButton(style: .standard, onTap: onTap)
    }
}
