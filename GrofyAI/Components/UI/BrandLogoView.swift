import SwiftUI

// MARK: - 品牌Logo尺寸规范

enum BrandLogoSize {
    case icon // 24x24 - 图标场景
    case large // 80x80 - 大图标场景
    case adaptive // 自适应 - 品牌展示场景
    case custom(CGFloat) // 自定义尺寸

    var dimension: CGFloat? {
        switch self {
        case .icon: return 22
        case .large: return 80
        case .adaptive: return nil
        case .custom(let size): return size
        }
    }
}

// MARK: - 品牌Logo样式规范

enum BrandLogoStyle {
    case plain // 纯图标
    case rounded // 带圆角
    case shadowed // 带阴影
    case roundedShadowed // 圆角+阴影

    var cornerRadius: CGFloat {
        switch self {
        case .plain, .shadowed: return 0
        case .rounded, .roundedShadowed: return DesignSystem.Rounded.lg
        }
    }

    var hasShadow: Bool {
        switch self {
        case .plain, .rounded: return false
        case .roundedShadowed, .shadowed: return true
        }
    }
}

// MARK: - 统一品牌Logo组件

struct BrandLogoView: View {
    let size: BrandLogoSize
    let style: BrandLogoStyle
    let opacity: Double

    init(
        size: BrandLogoSize = .adaptive,
        style: BrandLogoStyle = .plain,
        opacity: Double = 1.0
    ) {
        self.size = size
        self.style = style
        self.opacity = opacity
    }

    var body: some View {
        Image("ImageBrandLogo")
            .resizable()
            .aspectRatio(contentMode: .fit)
            .opacity(opacity)
            .conditionalFrame(size: size)
            .conditionalCornerRadius(style.cornerRadius)
            .conditionalShadow(enabled: style.hasShadow)
            .accessibilityLabel("MoonvyAI Logo")
    }
}

// MARK: - 便捷构造方法

extension BrandLogoView {
    /// 图标 - 用于模型选择器等功能性场景
    static func icon() -> BrandLogoView {
        BrandLogoView(size: .icon, style: .plain)
    }

    /// 大图标 - 用于认证、关于页面
    static func largeIcon(withShadow: Bool = true) -> BrandLogoView {
        BrandLogoView(
            size: .large,
            style: withShadow ? .roundedShadowed : .rounded
        )
    }

    /// 品牌展示 - 用于主页等品牌展示场景
    static func brandDisplay() -> BrandLogoView {
        BrandLogoView(size: .adaptive, style: .plain)
    }

    /// 空状态占位符 - 用于聊天空状态
    static func emptyState(opacity: Double = 0.8) -> BrandLogoView {
        BrandLogoView(size: .adaptive, style: .plain, opacity: opacity)
    }
}

// MARK: - View 修饰符扩展

extension View {
    @ViewBuilder
    fileprivate func conditionalFrame(size: BrandLogoSize) -> some View {
        if let dimension = size.dimension {
            frame(width: dimension, height: dimension)
        } else {
            self
        }
    }

    @ViewBuilder
    fileprivate func conditionalCornerRadius(_ radius: CGFloat) -> some View {
        if radius > 0 {
            cornerRadius(radius)
        } else {
            self
        }
    }

    @ViewBuilder
    fileprivate func conditionalShadow(enabled: Bool) -> some View {
        if enabled {
            shadow(
                color: .black.opacity(0.1),
                radius: 4,
                x: 0,
                y: 2
            )
        } else {
            self
        }
    }
}
