//
//  AspectRatioSelector.swift
//  GrofyAI
//
//  Created by kissy on 2025/6/13.
//

import Foundation
import SwiftUI

//// 数据模型，代表一个宽高比选项
//struct AspectRatioItem: Identifiable, Hashable {
//    let id = UUID() // 唯一标识符
//    let ratioText: String // 比例文字，如 "1:1"
//    let description: String? // 描述文字，如 "头像图"
//    let widthRatio: CGFloat // 宽度的相对比例
//    let heightRatio: CGFloat // 高度的相对比例
//}

struct AspectRatioSelector<Item: AspectRatioRepresentable>: View {
    
    // 所有可选项的数据源
    let items: [Item]
    
    // 通过 @Binding 与父视图同步当前选中的项
    @Binding var selectedItem: Item
    
    // 命名空间用于匹配几何体效果（平滑的动画）
    @Namespace private var selectionAnimation
    
    var body: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 10) {
                ForEach(items) { item in
                    cellView(for: item)
                        .onTapGesture {
                            // 使用动画更新选项
                            withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                                selectedItem = item
                            }
                        }
                }
            }
            .padding(.horizontal,5) // 给左右留出一些边距
        }
    }
    
    // 单个单元格的视图构建方法
    @ViewBuilder
    private func cellView(for item: Item) -> some View {
        let isSelected = (item.id == selectedItem.id)
        
        VStack(spacing: 8) {
            // 1. 自定义图标
            AspectRatioIcon(
                widthRatio: item.widthRatio,
                heightRatio: item.heightRatio,
                color: isSelected ? DesignSystem.Colors.primary : DesignSystem.Colors.textSecondary// 主题色
            )
            .frame(width: 30, height: 30)
            
            // 2. 比例文字
            Text(item.ratioText)
                .font(.system(size: 14, weight: .semibold))
            
            // 3. 描述文字
            if let description = item.secondaryDescription {
                Text(description)
                    .font(.system(size: 11))
                    .foregroundColor(.gray)
            }
            
        }
        .foregroundColor(isSelected ? DesignSystem.Colors.primary : DesignSystem.Colors.textSecondary)
        .frame(width: 48, height: 65)
        .overlay(
            RoundedRectangle(cornerRadius: 8)
                .stroke((isSelected ?  DesignSystem.Colors.primary : DesignSystem.Colors.textHint ), lineWidth: 1) // 描边样式
        )
//        .background(DesignSystem.Colors.backgroundPage)
        //        .background(
        //            ZStack {
        //                // 背景色
        //                Color(white: 0.15)
        //
        //                // 选中时的发光背景和边框
        //                if isSelected {
        //                    RoundedRectangle(cornerRadius: 12)
        //                        .fill(Color(red: 88/255, green: 86/255, blue: 214/255).opacity(0.5)) // 选中背景色
        //                        .matchedGeometryEffect(id: "selectionBackground", in: selectionAnimation)
        //
        //                    RoundedRectangle(cornerRadius: 12)
        //                        .stroke(Color.white, lineWidth: 1.5)
        //                        .matchedGeometryEffect(id: "selectionBorder", in: selectionAnimation)
        //                }
        //            }
        //        )
        .cornerRadius(8)
    }
}


// 自定义图标视图
struct AspectRatioIcon: View {
    let widthRatio: CGFloat
    let heightRatio: CGFloat
    let color: Color

    var body: some View {
        // 使用 GeometryReader 来获取可用空间
        GeometryReader { geometry in
            let availableWidth = geometry.size.width
            let availableHeight = geometry.size.height
            
            // 根据比例计算图标的实际尺寸
            let aspectWidth = min(availableWidth, availableHeight * (widthRatio / heightRatio))
            let aspectHeight = min(availableHeight, availableWidth * (heightRatio / widthRatio))
            
            let rect = CGRect(
                x: (availableWidth - aspectWidth) / 2,
                y: (availableHeight - aspectHeight) / 2,
                width: aspectWidth,
                height: aspectHeight
            )

            // 使用 Canvas 绘制，性能更好
            Canvas { context, size in
                // 绘制中心的深色矩形
                let centerRectPath = Path(roundedRect: rect.insetBy(dx: 4, dy: 4), cornerSize: .zero)
                context.fill(centerRectPath, with: .color(color.opacity(0.4)))
                
                // 绘制四个角的标记
                let cornerSize: CGFloat = 5
                var cornerPath = Path()
                // Top-Left
                cornerPath.move(to: CGPoint(x: rect.minX, y: rect.minY + cornerSize))
                cornerPath.addLine(to: CGPoint(x: rect.minX, y: rect.minY))
                cornerPath.addLine(to: CGPoint(x: rect.minX + cornerSize, y: rect.minY))
                // Top-Right
                cornerPath.move(to: CGPoint(x: rect.maxX - cornerSize, y: rect.minY))
                cornerPath.addLine(to: CGPoint(x: rect.maxX, y: rect.minY))
                cornerPath.addLine(to: CGPoint(x: rect.maxX, y: rect.minY + cornerSize))
                // Bottom-Left
                cornerPath.move(to: CGPoint(x: rect.minX, y: rect.maxY - cornerSize))
                cornerPath.addLine(to: CGPoint(x: rect.minX, y: rect.maxY))
                cornerPath.addLine(to: CGPoint(x: rect.minX + cornerSize, y: rect.maxY))
                // Bottom-Right
                cornerPath.move(to: CGPoint(x: rect.maxX - cornerSize, y: rect.maxY))
                cornerPath.addLine(to: CGPoint(x: rect.maxX, y: rect.maxY))
                cornerPath.addLine(to: CGPoint(x: rect.maxX, y: rect.maxY - cornerSize))
                
                context.stroke(cornerPath, with: .color(color), lineWidth: 1.5)
            }
        }
    }
}
