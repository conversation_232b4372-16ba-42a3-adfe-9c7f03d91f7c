//
//  RadioSelector.swift
//  GrofyAI
//
//  Created by kissy on 2025/6/13.
//
import SwiftUI

struct StyleSelector<Item: Hashable & AiDescription, Content: View>: View {
    
    // MARK: - Properties
    
    // 数据源。
    let items: [Item]
    let columns: [GridItem]?
    // 双向绑定到父视图的所选项状态。
    @Binding var selection: Item
    
    // Grid layout configuration.

    let rowHeight: CGFloat?
    
    let displayLimit: Int?
    @State private var isExpanded: Bool = false
    

    let content: (Item, Bool) -> Content
    
    init(
        items: [Item],
        selection: Binding<Item>,
        rowHeight: CGFloat? = nil,
        displayLimit: Int? = 8,
        columns: [GridItem]? = [
            GridItem(.flexible()),
            GridItem(.flexible()),
            GridItem(.flexible()),
            GridItem(.flexible()),
        ],
        @ViewBuilder content: @escaping (Item, Bool) -> Content
    ) {
        self.items = items
        self._selection = selection
        self.rowHeight = rowHeight
        self.columns = columns
        self.content = content
        self.displayLimit = displayLimit
    }
    
    private var shouldShowMoreButton: Bool {
        //如果设置了限制，则显示按钮，不展开视图；
        //项目总数超过限制。
        guard let limit = displayLimit, !isExpanded else { return false }
        return items.count > limit
    }
    
    private var displayedItems: [Item] {
        if shouldShowMoreButton, let limit = displayLimit {
            //在折叠视图中，显示项目的前几项
            //我们显示“limit - 1”项，为“More”按钮留出空间。
            return Array(items.prefix(limit - 1))
        } else {
            // 如果数量 < 10 ,展开功能被禁用，显示所有项。
            return items
        }
    }
    
    // MARK: - Body
    var body: some View {
        LazyVGrid(columns: columns!, spacing: 10) {
            ForEach(displayedItems, id: \.self) { item in
                let button =  Button(action: {
                    // 点击后，用动画更新选择。
                    withAnimation(.easeInOut(duration: 0.2)) {
                        selection = item
                    }
                }) {
                    // 传递的单视图
                    content(item, item == selection)
                }
                
                // 根据rowHeight 来设置高度
                if let height = rowHeight {
                    button.frame(height: height)
                } else {
                    button
                }
            }
            // 更具条件显示更多按钮
            if shouldShowMoreButton {
                moreButtonView
            }
        }
    }
    
    @ViewBuilder
    private var moreButtonView: some View {
        let button = Button(action: {
            withAnimation(.spring()) {
                isExpanded = true
            }
        }) {
            VStack(spacing: 0){
                Image("more")
                    .resizable()
                    .frame(width: 80, height: 80)
                    .clipShape(RoundedRectangle(cornerRadius: 12))
                Text("更多")
                    .font(.system(size: 14, weight: .regular))
                    .frame(maxWidth: .infinity)
            }
            .foregroundColor(.secondary)
            .frame(maxWidth: .infinity, maxHeight: .infinity) // Fill the cell
//            .overlay(
//                RoundedRectangle(cornerRadius: 12)
//                    .stroke( Color(red: 160/255, green: 147/255, blue: 245/255), lineWidth: 1) // 描边样式
//            )
//            .background(
//                RoundedRectangle(cornerRadius: 12)
//                    .fill(.gray.opacity(0.08))
//            )
        }
        
        if let height = rowHeight {
            button.frame(height: height)
        } else {
            button
        }
    }
}



struct StyleOptionCell: View {
    let title: String
    let imageName: String
    let isSelected: Bool
    @Environment(\.colorScheme) private var colorScheme

    
    var body: some View {
        VStack(spacing: 2){
            Image(imageName)
                .resizable()
                .frame(width: 80, height: 80)
                .clipShape(RoundedRectangle(cornerRadius: 12))
            Text(title)
                .font(.system(size: 14, weight: .regular))
                .foregroundColor(isSelected ? .white : DesignSystem.Colors.textSecondary)
                .frame(maxWidth: .infinity)
        }
//        .overlay(
//            RoundedRectangle(cornerRadius: 12)
//                .stroke((isSelected ? Color("primaryColor"): Color("secondBackgroundColor") ), lineWidth: 1) // 描边样式
//        )
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(
                    isSelected ? DesignSystem.Colors.primary: .clear
                )
        )
    }
}
