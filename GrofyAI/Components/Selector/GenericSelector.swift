//
//  GenericSelector.swift
//  GrofyAI
//
//  Created by kissy on 2025/6/23.
//

import SwiftUI

typealias GenericSelectorOptionsProtocol = AiModelProtocol & Identifiable & Hashable

// 一个通用的、可复用的选择器视图
struct GenericSelectorView<OptionType: GenericSelectorOptionsProtocol >: View {
    // 1. 选项列表是从外部传入的
    let options: [OptionType]
    
    // 2. 绑定一个泛型 `OptionType`
    @Binding var selectedOption: OptionType
    
    // 动画命名空间保持不变
    @Namespace private var animation
    
    // 新增一个属性来控制圆角半径，方便统一修改
    private let cornerRadius: CGFloat = 8 // 你可以随意调整这个值
    
    var body: some View {
        HStack(spacing: 0) {
            ForEach(options) { option in
                ZStack {
                    if selectedOption == option {
                        // 1. 将 Capsule() 替换为 RoundedRectangle
                        RoundedRectangle(cornerRadius: cornerRadius)
                            .fill(DesignSystem.Colors.backgroundCard)
                            .matchedGeometryEffect(id: "selection_background", in: animation)
                    }
                    
                    Text(option.description)
                        .font(.system(size: 14, weight: .medium))
                }
                .foregroundColor(selectedOption == option ? DesignSystem.Colors.textPrimary : DesignSystem.Colors.textSecondary)
                .frame(width: 100,height: 30)
                .contentShape(Rectangle()) // 保持点击区域为矩形
                .onTapGesture {
                    withAnimation(.spring(response: 0.4, dampingFraction: 1)) {
                        selectedOption = option
                    }
                }
            }
        }
        .padding(4) // 给 HStack 一点内边距，让背景看起来更和谐
        .background(
            // 2. 将这里的 Capsule() 也替换为 RoundedRectangle
            RoundedRectangle(cornerRadius: cornerRadius + 4) // 背景的圆角可以稍微大一点，以包裹住内层
                .fill(DesignSystem.Colors.backgroundInput)
        )
    }
    
}
