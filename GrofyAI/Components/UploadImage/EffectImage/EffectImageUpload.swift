//
//  EffectImageUpload.swift
//  GrofyAI
//
//  Created by kissy on 2025/7/3.
//
import SwiftUI
import Kingfisher

struct EffectImageUploaderView: View {
    
    // 外部依赖 (无变化)
    let fileService = FileService()
    @Binding var uploadedUrls: [String]
    @Binding var uploadToPixverseID: Int?
    let maxCount: Int
    let threadId: String?
    
    // 内部状态
    @State private var uploadableImages: [UploadableImage] = []
    @State private var selectedImageId: UUID? // [!!] 新增: 追踪当前选中的图片ID
    
    // 控制器状态 (无变化)
    @State private var showActionSheet = false
    @State private var showImagePicker = false
    @State private var showFilePicker = false
    @State private var pickerSourceType: UIImagePickerController.SourceType = .photoLibrary
    
    @State private var newPickedImage: UIImage? //相册
    @State private var newPickedImageData: Data? //文件 或 相机
    @State private var newPickedImageName: String?
    @State private var showCustomCamera = false
    
    init(
        uploadedUrls: Binding<[String]>,
        uploadToPixverseID: Binding<Int?> = .constant(nil),
        maxCount: Int = 5,
        threadId: String? = nil
    ) {
        self._uploadedUrls = uploadedUrls
        self._uploadToPixverseID = uploadToPixverseID
        self.maxCount = maxCount
        self.threadId = threadId
    }
    
    // 计算属性，用于获取当前选中的图片对象
    private var selectedUploadableImage: UploadableImage? {
        // 如果有明确的选中ID，则查找该图片
        if let id = selectedImageId, let selected = uploadableImages.first(where: { $0.id == id }) {
            return selected
        }
        // 如果没有明确的选中ID（例如，初始状态或选中项被删除后），默认显示最后一张图片
        return uploadableImages.last
    }

    // 处理 UIImage (来自相册、相机)
    private func processSelectedImage(_ image: UIImage, name: String?) {
        // 直接调用我们之前写的优化方法
        startUploadProcess(for: image, name: name, shouldResize: true)
    }
    
    private func processSelectedData(_ data: Data, name: String?) {
        // 定义文件大小阈值
        let sizeThresholdInBytes = 500 * 1024
        
        // 从原始数据创建一个 UIImage，用于后续处理和UI显示
        guard let image = UIImage(data: data) else {
            print("❌ 无法从文件数据创建图片")
            // 这里可以显示一个错误提示给用户
            return
        }
        // 同样调用标准化的上传处理流程
        // 这确保了即使用户从文件选择了一个超大的原图，我们依然会对其进行缩放和压缩，保持一致性。
        if data.count < sizeThresholdInBytes {
            print("✅ 图片体积小于 500KB (\(data.count/1024)KB)，跳过缩放步骤。")
            startUploadProcess(for: image, name: name, shouldResize: false) // 传递 shouldResize: false
        } else {
            // 对于大文件，正常进行缩放和压缩
            print("ℹ️ 图片体积大于 500KB，进行标准化处理。")
            startUploadProcess(for: image, name: name, shouldResize: true) // 传递 shouldResize: true
        }
    }
    
    private func startUploadProcess(for image: UIImage, name: String?,shouldResize: Bool) {
        let originalImageName = name ?? "image-\(UUID().uuidString).jpg"
        
        // 核心优化点: 统一对所有来源的图片进行处理
        guard let processed = image.flexibleResizedAndCompressed(
                maxDimension: 1920.0,
                compressionQuality: 0.8,
                isResizeEnabled: shouldResize // 传入控制参数
            ) else {
                print("❌ 图片处理失败")
                return
            }
        
        let processedImage = processed.image
        let imageName = (originalImageName as NSString).deletingPathExtension + ".jpg"
        
        // 后续逻辑完全一样 (替换或添加)
        if let selectedId = selectedImageId, let index = uploadableImages.firstIndex(where: { $0.id == selectedId }) {
            // ... 替换逻辑 ...
            if case .success(let urlToDelete) = uploadableImages[index].status {
                uploadedUrls.removeAll { $0 == urlToDelete }
            }
            uploadableImages[index].image = processedImage
            uploadableImages[index].imageName = imageName
            uploadableImages[index].status = .uploading
            
            let itemToUpdate = uploadableImages[index]
            uploadImage(image: processedImage, name: imageName) { result, img_id in
                handleUploadResult(for: itemToUpdate.id, result: result, img_id: img_id)
            }
        } else if uploadableImages.count < maxCount {
            // ... 添加逻辑 ...
            let newUploadableImage = UploadableImage(image: processedImage, imageName: imageName)
            uploadableImages.append(newUploadableImage)
            selectedImageId = newUploadableImage.id
            
            uploadImage(image: processedImage, name: imageName) { result, img_id in
                handleUploadResult(for: newUploadableImage.id, result: result, img_id: img_id)
            }
        }
    }

    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            // 1. 顶部主预览区
            mainPreviewArea
            
            // 只有当有图片时，才显示下面的声明和缩略图
            if !uploadableImages.isEmpty, maxCount > 1{
            // 3. 底部缩略图滚动区
                thumbnailArea
            }
        }
        .onAppear {
            syncWithUpstreamUrls(uploadedUrls)
            // 首次出现时，如果已有图片，默认选中最后一个
            if selectedImageId == nil {
                selectedImageId = uploadableImages.last?.id
            }
        }
        .onChange(of: uploadedUrls) { newUrls in
            syncWithUpstreamUrls(newUrls)
        }
        .onChange(of: newPickedImage) { image in
            guard let image = image else { return }
            processSelectedImage(image, name: newPickedImageName)
            newPickedImage = nil
            newPickedImageName = nil
        }
        .onChange(of: newPickedImageData) { data in
            guard let data = data else { return }
            // 来自 FilePicker 的 Data，走新的处理路径
            processSelectedData(data, name: newPickedImageName)
            newPickedImageData = nil
            newPickedImageName = nil
        }
        // ActionSheet and Pickers (无变化)
        .actionSheet(isPresented: $showActionSheet) {
            ActionSheet(title: Text("选择图片"), buttons: [
                .default(Text("从相册选择")) { pickerSourceType = .photoLibrary; showImagePicker = true },
                .default(Text("拍照")) { if UIImagePickerController.isSourceTypeAvailable(.camera) { showCustomCamera = true } },
                .default(Text("从文件选择")) { showFilePicker = true },
                .cancel(Text("取消选择"))
            ])
        }
        .sheet(isPresented: $showImagePicker) { ImagePicker(image: $newPickedImage, imageName: $newPickedImageName, sourceType: pickerSourceType) }
        .sheet(isPresented: $showFilePicker) { FilePicker(imageData: $newPickedImageData, imageName: $newPickedImageName) }
        .fullScreenCover(isPresented: $showCustomCamera) { ImagePickerCameraView(capturedImageData: $newPickedImageData, capturedImageName: $newPickedImageName) }
    }
    
    // MARK: - Subviews
    
    @ViewBuilder
    private var mainPreviewArea: some View {
        // 如果有图片，显示大图预览；否则显示上传占位符
        if let imageToPreview = selectedUploadableImage {
            MainImageView(
                item: imageToPreview,
                onDelete: {
                    deleteImage(imageToPreview)
                },
                onTap: {
                    // 点击主预览图，准备替换它，所以直接显示动作表
                    self.showActionSheet = true
                }
            )
            .aspectRatio(1, contentMode: .fit) // 保持1:1比例或根据需要调整
        } else {
            UploadPlaceholderView {
                self.showActionSheet = true
            }
            .aspectRatio(1, contentMode: .fit)
        }
    }

    
    private var thumbnailArea: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 12) {
                ForEach($uploadableImages) { $item in
                    ThumbnailItemView(
                        item: $item,
                        isSelected: item.id == selectedImageId,
                        onSelect: {
                            self.selectedImageId = item.id
                        },
                        onDelete: {
                            deleteImage(item)
                        }
                    )
                }
                
                // 在缩略图列表末尾显示添加按钮
                if uploadableImages.count < maxCount {
                    AddButtonView1 {
                        self.selectedImageId = nil
                        self.showActionSheet = true
                    }
                    .frame(width: 70, height: 70)
                }
            }
            .padding(5)
        }
        .frame(height: 80) // 固定缩略图区域高度
    }
    
    // MARK: - 主要方法
    
    private func deleteImage(_ imageToDelete: UploadableImage) {
        let wasSelected = imageToDelete.id == selectedImageId
        
        // 从数据源中移除
        if case .success(let urlToDelete) = imageToDelete.status {
            uploadedUrls.removeAll { $0 == urlToDelete }
        }
        uploadableImages.removeAll { $0.id == imageToDelete.id }
        
        // 如果删除的是当前选中的图片，则自动选中列表中的最后一张
        if wasSelected {
            selectedImageId = uploadableImages.last?.id
        }
    }
    
    private func syncWithUpstreamUrls(_ upstreamUrls: [String]) {
        // ... 原有的同步逻辑 ...
        let currentlyDisplayedUrls = Set(uploadableImages.compactMap {
            if case .success(let url) = $0.status { return url }
            return nil
        })
        let upstreamUrlSet = Set(upstreamUrls)
        guard currentlyDisplayedUrls != upstreamUrlSet else { return }

        let urlsToRemove = currentlyDisplayedUrls.subtracting(upstreamUrlSet)
        if !urlsToRemove.isEmpty {
            uploadableImages.removeAll { item in
                if case .success(let url) = item.status, urlsToRemove.contains(url) {
                    return true
                }
                return false
            }
        }
        
        let urlsToAdd = upstreamUrlSet.subtracting(currentlyDisplayedUrls)
        if !urlsToAdd.isEmpty {
            let newImages = urlsToAdd.map { url -> UploadableImage in
                UploadableImage(image: UIImage(), imageName: "", status: .success(url: url))
            }
            uploadableImages.append(contentsOf: newImages)
        }
        
        // [!!] 同步后检查当前选中项是否还存在
        if let currentId = selectedImageId, !uploadableImages.contains(where: { $0.id == currentId }) {
            selectedImageId = uploadableImages.last?.id
        }
    }
    
    private func handleUploadResult(for id: UUID, result: Result<String, Error>,img_id: Int?) {
        DispatchQueue.main.async {
            guard let index = uploadableImages.firstIndex(where: { $0.id == id }) else { return }
            
            switch result {
            case .success(let url):
                // 防止重复添加
                if !uploadedUrls.contains(url) {
                    uploadableImages[index].status = .success(url: url)
                    uploadedUrls.append(url)// 更新绑定的外部数据
                }
                
                if img_id != nil {
                    uploadToPixverseID = img_id
                }
                
                
            case .failure:
                uploadableImages[index].status = .failed
            }
        }
    }
    private func uploadImage(image: UIImage, name: String, completion: @escaping (Result<String, Error>,Int?) -> Void) {
        Task {
            do {
                // 直接传递 [UIImage] 数组，选择上传为 PNG 格式
                print("正在上传图片...")
                let format: ImageFormat = .jpeg(compressionQuality: 0.8)

                if uploadToPixverseID == nil ,threadId != nil  {
                    let resultPng = try await fileService.uploadImages(
                        images: [(imageInfo: image, name: name)],
                        threadId: self.threadId ?? "1111-AAAA-2222-BBBB",
                        format: format
                    )
                    //设置上传成功状态
                    completion(.success(resultPng.first!.url),nil)
                    print("✅ PNG 上传成功: \(resultPng)")
                    
                }else {
                    let resultPng = try await fileService.uploadImageToPixverse(
                        images: [(imageInfo: image, name: name)],
                        format: format
                    )
                    //设置上传成功状态
                    completion(.success(resultPng.img_url),resultPng.img_id)
                    print("✅Pixverse  PNG 上传成功: \(resultPng)")
                }
                
            } catch {
                print("❌ 上传失败: \(error.localizedDescription)")
                completion(.failure(error),nil)
            }
        }
    }
}


//MARK: - New & Modified Helper Views

// [!!] 新增：大的上传占位符视图
struct UploadPlaceholderView: View {
    var action: () -> Void

    var body: some View {
        Button(action: action) {
            ZStack {
                RoundedRectangle(cornerRadius: 12)
                    .fill(DesignSystem.Colors.backgroundPage)
//                    .fill(Color(red: 0.17, green: 0.17, blue: 0.18)) // 模拟深色背景
                        

                VStack(spacing: 24) {
                    Image(systemName: "photo.on.rectangle.angled")
                        .font(.system(size: 48))
                        .foregroundColor(.gray)

                    VStack(spacing: 8) {
                        Text("仅支持 JPG 或 PNG 格式的图片")
                        Text("文件大小不超过 10M, 尺寸不小于 300px")
                    }
                    .font(.subheadline)
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                }
                .padding()
            }
        }
        .buttonStyle(.plain)
    }
}

// [!!] 新增：主预览图视图
struct MainImageView: View {
    let item: UploadableImage
    var onDelete: () -> Void // 删除操作的闭包
    var onTap: () -> Void    // 点击操作的闭包
    
    @State private var onFailure = false
    
    var body: some View {
        // 使用 ZStack 来叠加删除按钮
        ZStack(alignment: .topTrailing) {
            // 主要内容区域，负责显示图片和状态覆盖层
            ZStack {
                RoundedRectangle(cornerRadius: 12).fill(Color(UIColor.secondarySystemBackground))
                imageDisplayView.clipShape(RoundedRectangle(cornerRadius: 12))
                overlayContent.clipShape(RoundedRectangle(cornerRadius: 12))
            }
            .onTapGesture(perform: onTap) // 点击整个区域触发 onTap 闭包
            
            // 删除按钮
            Button(action: onDelete) {
                Image(systemName: "xmark.circle.fill")
                    .foregroundColor(.gray)
                    .background(Circle().fill(Color.white))
                    .font(.title3) // 按钮图标更大一些
            }
            .padding(8) // 给予一些边距，而不是使用 offset
        }
    }
    
    

    @ViewBuilder
    private var imageDisplayView: some View {
        switch item.status {
        case .success(let urlString):
//            AsyncImage(url: URL(string: urlString)) { phase in
//                switch phase {
//                case .success(let image): image.resizable().aspectRatio(contentMode: .fit)
//                case .failure: Image(systemName: "photo.fill.on.rectangle.fill").font(.largeTitle).foregroundColor(.gray)
//                case .empty: ProgressView()
//                @unknown default: EmptyView()
//                }
//            }
            if onFailure {
                Image(systemName: "photo.fill.on.rectangle.fill").font(.largeTitle).foregroundColor(.gray)
            }else {
                
                KFImage(URL(string: urlString))
                    .placeholder {
                        // 当图片正在加载时显示的占位符
                        ProgressView()
                    }
                    .onFailure { error in
                        // 可选：当加载失败时，在控制台打印错误
                        onFailure = true
                        print("KFImage failed to load image from \(urlString): \(error.localizedDescription)")
                    }
                    .fade(duration: 0.25) // 添加一个优雅的淡入动画
                    .cacheOriginalImage()
                    .resizable() // 应用于加载成功的图片
                    .aspectRatio(contentMode: .fit)
            }
            
        default: Image(uiImage: item.image).resizable().aspectRatio(contentMode: .fit)
        }
    }

    @ViewBuilder private var overlayContent: some View {
        switch item.status {
        case .uploading: ZStack { Color.black.opacity(0.4); ProgressView().progressViewStyle(.circular).tint(.white).scaleEffect(1.5) }
        case .failed: ZStack { Color.black.opacity(0.4); VStack { Image(systemName: "exclamationmark.triangle.fill").foregroundColor(.red).font(.title); Text("上传失败").foregroundColor(.white).padding(.top, 4) } }
        case .success: EmptyView()
        }
    }
}

// [!!] 新增：底部缩略图项视图
struct ThumbnailItemView: View {
    @Binding var item: UploadableImage
    let isSelected: Bool
    var onSelect: () -> Void
    var onDelete: () -> Void
    @State private var onFailure = false

    var body: some View {
        ZStack(alignment: .topTrailing) {
            ZStack {
                imageDisplayView
                    .background(Color(UIColor.secondarySystemBackground))
                    .clipShape(RoundedRectangle(cornerRadius: 8))
                
                overlayContent.clipShape(RoundedRectangle(cornerRadius: 8))
            }
            .overlay(
                RoundedRectangle(cornerRadius: 8)
                    .stroke(isSelected ? Color.accentColor : Color.clear, lineWidth: 1)
            )
            .scaleEffect(isSelected ? 1.05 : 1.0) // Make it slightly larger when selected
            .shadow(
                color: .black.opacity(isSelected ? 0.3 : 0), // Shadow only appears when selected
                radius: isSelected ? 5 : 0,
                x: 0,
                y: 4
            )
            .onTapGesture(perform: onSelect)
            
            Button(action: onDelete) {
                Image(systemName: "xmark.circle.fill")
                    .foregroundColor(.gray)
                    .background(Circle().fill(Color.white))
                    .font(.title3)
            }
            .offset(x: 0, y: 0)
        }
        .frame(width: 70, height: 70)
    }

    @ViewBuilder private var imageDisplayView: some View {
        switch item.status {
        case .success(let urlString):
            //            AsyncImage(url: URL(string: urlString)) { phase in
            //                if let image = phase.image { image.resizable().aspectRatio(contentMode: .fit) }
            //                else if phase.error != nil { Image(systemName: "wifi.exclamationmark").foregroundColor(.gray) }
            //                else { ProgressView() }
            //            }
            if onFailure {
                Image(systemName: "wifi.exclamationmark").foregroundColor(.gray)
            }else {
                KFImage(URL(string: urlString))
                    .placeholder { ProgressView() }
                    .onFailure { _ in
                        onFailure = true
                    }
                    .fade(duration: 0.25)
                    .cacheOriginalImage()
                    .resizable()
                    .aspectRatio(contentMode: .fit) // .fill 填充整个区域，适合缩略图
            }
        default: Image(uiImage: item.image).resizable().aspectRatio(contentMode: .fit)
        }
    }
    
    @ViewBuilder private var overlayContent: some View {
        switch item.status {
        case .uploading: ZStack { Color.black.opacity(0.5); ProgressView().tint(.white) }
        case .failed: ZStack { Color.black.opacity(0.5); Image(systemName: "exclamationmark.triangle.fill").foregroundColor(.red) }
        case .success: EmptyView()
        }
    }
}

// AddButtonView 保持不变
struct AddButtonView1: View {
    // ... (原代码)
    var action: () -> Void
    @Environment(\.colorScheme) private var colorScheme

    var body: some View {
        Button(action: action) {
            RoundedRectangle(cornerRadius: 12)
                .stroke(style: StrokeStyle(lineWidth: 1, dash: [2]))
                .foregroundColor(DesignSystem.Colors.primary)
                .frame(width: 70, height: 70)
                .overlay(
                    Image(systemName: "plus")
                        .font(.largeTitle)
                        .foregroundColor(
                            colorScheme == .light ? DesignSystem.Colors.primary : .white
                        )
                )
        }
    }
}
