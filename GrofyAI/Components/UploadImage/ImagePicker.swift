//
//  ImagePicker.swift
//  GrofyAI
//
//  Created by kissy on 2025/6/17.
//

//MARK: ImagePicker
import SwiftUI
import PhotosUI

struct ImagePicker: UIViewControllerRepresentable {
    // 输出
    @Binding var image: UIImage?
    @Binding var imageName: String? // 新增：用于传递图片名称
    
    // 输入
    var sourceType: UIImagePickerController.SourceType = .photoLibrary
    
    @Environment(\.presentationMode) private var presentationMode

    func makeUIViewController(context: Context) -> UIImagePickerController {
        let picker = UIImagePickerController()
        picker.sourceType = sourceType
        picker.delegate = context.coordinator
        return picker
    }

    func updateUIViewController(_ uiViewController: UIImagePickerController, context: Context) {}

    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }

    final class Coordinator: NSObject, UIImagePickerControllerDelegate, UINavigationControllerDelegate {
        var parent: ImagePicker

        init(_ parent: ImagePicker) {
            self.parent = parent
        }

        func imagePickerController(_ picker: UIImagePickerController, didFinishPickingMediaWithInfo info: [UIImagePickerController.InfoKey : Any]) {
            // 1. 获取图片 UIImage
            if let uiImage = info[.originalImage] as? UIImage {
                parent.image = uiImage
            }
            
            // 2. 获取图片名称 (优化后的逻辑)
            var fileName: String?

            // 优先尝试从 imageURL 获取文件名
            if let url = info[.imageURL] as? URL {
                fileName = url.lastPathComponent
            }
            // 如果不行，再尝试从 PHAsset 获取
            else if let asset = info[.phAsset] as? PHAsset {
                // 从 PHAsset 中获取文件名
                // 这需要异步请求，但对于 didFinishPickingMediaWithInfo 来说，通常可以快速获取
                let assetResources = PHAssetResource.assetResources(for: asset)
                if let resource = assetResources.first {
                    fileName = resource.originalFilename
                }
            }
            
            // 如果以上方法都失败（例如相机拍摄的照片），生成一个新文件名
            if fileName == nil {
                let timestamp = Int(Date().timeIntervalSince1970)
                fileName = "photo_\(timestamp).jpg"
            }
            
            parent.imageName = fileName

            // 3. 关闭选择器
            parent.presentationMode.wrappedValue.dismiss()
        }
        
        func imagePickerControllerDidCancel(_ picker: UIImagePickerController) {
            parent.presentationMode.wrappedValue.dismiss()
        }
    }
}
