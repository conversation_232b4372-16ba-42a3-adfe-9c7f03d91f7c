//
//  ImageUploaderView.swift
//  GrofyAI
//
//  Created by kissy on 2025/6/17.
//
import SwiftUI

//MARK: UploadableImage
// 用于表示每个图片及其上传状态
struct UploadableImage: Identifiable, Equatable {
    let id = UUID()
    var image: UIImage
    var imageName: String
    var status: UploadStatus = .uploading
    
    static func == (lhs: UploadableImage, rhs: UploadableImage) -> Bool {
        lhs.id == rhs.id && lhs.status == rhs.status
    }
}

//MARK: UploadStatus
enum UploadStatus {
    case uploading
    case success(url:String)
    case failed
    
    static func == (lhs: UploadStatus, rhs: UploadStatus) -> Bool {
        switch (lhs, rhs) {
        case (.uploading, .uploading):
            return true
        case (.success(let lhsUrl), .success(let rhsUrl)):
            return lhsUrl == rhsUrl
        case (.failed, .failed):
            return true
        default:
            return false
        }
    }
}


//MARK: ImageUploaderView
struct ImageUploaderView: View {
    let fileService = FileService()
    
    // 绑定到外部的最终结果（例如图片URL）
    @Binding var uploadedUrls: [String]
    @Binding var uploadToPixverseID: Int?
    // 最大图片数量限制
    let maxCount: Int
    let threadId: String?
    
    // 内部状态
    @State private var uploadableImages: [UploadableImage] = []
    @State private var showActionSheet = false
    @State private var showImagePicker = false
    @State private var showFilePicker = false
    @State private var pickerSourceType: UIImagePickerController.SourceType = .photoLibrary
    
    @State private var newPickedImage: UIImage?
    @State private var newPickedImageData: Data?
    @State private var newPickedImageName: String?
    
    //状态以跟踪正在编辑的图像（如果有的话）。
    @State private var editingImageId: UUID? = nil
    
    @State private var showCustomCamera = false
    
    
    init(
        uploadedUrls: Binding<[String]>,
        uploadToPixverseID: Binding<Int?> = .constant(nil),
        maxCount: Int = 5,
        threadId: String? = nil
    ) {
        self._uploadedUrls = uploadedUrls
        self._uploadToPixverseID = uploadToPixverseID
        self.maxCount = maxCount
        self.threadId = threadId
    }
    
    
    private let scrollThreshold = 3
    
    
    


    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            
            // 图片展示区域
            // 1. 判断是否需要显示添加按钮
            let shouldShowAddButton = uploadableImages.count < maxCount
            // 2. 计算 HStack 中将要显示的项目总数
            let totalItemCount = uploadableImages.count + (shouldShowAddButton ? 1 : 0)
            // 3. 根据总数决定是否使用 ScrollView
            if totalItemCount > scrollThreshold {
                // ----- 版本 A: 当项目多于阈值时，使用 ScrollView -----
                ScrollView(.horizontal, showsIndicators: false) {
                    imageContent
                }
            } else {
                // ----- 版本 B: 当项目少于或等于阈值时，不使用 ScrollView -----
                HStack{
                    imageContent
                    Spacer()
                }
                
            }
        }
        .onChange(of: uploadedUrls) { newUrls in
            syncWithUpstreamUrls(newUrls)
        }
                // [!!] 并且在视图首次出现时也手动调用一次，确保初始状态被加载
        .onAppear {
            syncWithUpstreamUrls(uploadedUrls)
        }
        // 监听新选择的图片
        .onChange(of: newPickedImage) { image in
            guard let image = image else { return }
            // 当图片被选择时，名称也应该被设置了（可能为 nil）
            processSelectedImage(image, name: newPickedImageName)
            
            // 处理完后重置，防止重复触发
            newPickedImage = nil
            newPickedImageName = nil
        }
        .onChange(of: newPickedImageData) { data in
            guard let data = data else { return }
            // 来自 FilePicker 的 Data，走新的处理路径
            processSelectedData(data, name: newPickedImageName)
            newPickedImageData = nil
            newPickedImageName = nil
        }
        // 动作表单：选择图片来源
        .actionSheet(isPresented: $showActionSheet) {
            ActionSheet(title: Text("选择图片"), buttons: [
                .default(Text("从相册选择"), action: {
                    pickerSourceType = .photoLibrary
                    showImagePicker = true
                }),
                .default(Text("拍照"), action: {
                    if UIImagePickerController.isSourceTypeAvailable(.camera) {
                        //                        pickerSourceType = .camera
                        self.showCustomCamera = true
                    } else {
                        // 在模拟器或无摄像头的设备上提示
                        print("相机不可用")
                    }
                }),
                .default(Text("从文件选择"), action: {
                    showFilePicker = true
                }),
                .cancel(Text("取消选择"))
            ])
        }
        // 弹出相册/相机
        .sheet(isPresented: $showImagePicker) {
            ImagePicker(image: $newPickedImage, imageName: $newPickedImageName, sourceType: pickerSourceType)
        }
        // 弹出文件选择器
        .sheet(isPresented: $showFilePicker) {
            FilePicker(imageData: $newPickedImageData, imageName: $newPickedImageName)
        }
        .fullScreenCover(isPresented: $showCustomCamera) {
            ImagePickerCameraView(capturedImageData: $newPickedImageData, capturedImageName: $newPickedImageName)
        }
    }
    
    private func syncWithUpstreamUrls(_ upstreamUrls: [String]) {
        // 1. 获取当前正在显示的图片URL集合
        let currentlyDisplayedUrls = Set(uploadableImages.compactMap {
            if case .success(let url) = $0.status {
                return url
            }
            return nil
        })
        
        // 2. 获取外部传入的URL集合
        let upstreamUrlSet = Set(upstreamUrls)
        
        // 3. 如果两个集合完全相同，则无需任何操作，直接返回。
        //    这可以防止不必要的视图刷新和无限循环。
        guard currentlyDisplayedUrls != upstreamUrlSet else {
            return
        }

        // 4. (移除) 找出需要从界面上移除的图片
        //    这些图片存在于当前界面，但不存在于外部传入的URL列表中
        let urlsToRemove = currentlyDisplayedUrls.subtracting(upstreamUrlSet)
        if !urlsToRemove.isEmpty {
            uploadableImages.removeAll { item in
                if case .success(let url) = item.status, urlsToRemove.contains(url) {
                    return true
                }
                return false
            }
        }
        
        // 5. (添加) 找出需要新添加到界面的图片
        //    这些图片存在于外部传入的URL列表，但不存在于当前界面
        let urlsToAdd = upstreamUrlSet.subtracting(currentlyDisplayedUrls)
        if !urlsToAdd.isEmpty {
            let newImages = urlsToAdd.map { url -> UploadableImage in
                UploadableImage(
                    image: UIImage(systemName: "photo.fill") ?? UIImage(), // 占位图
                    imageName: "loaded_image.jpg",
                    status: .success(url: url)
                )
            }
            uploadableImages.append(contentsOf: newImages)
        }
    }
    
    private var imageContent: some View {
         HStack(spacing: 16) {
             // 已选择的图片列表
             ForEach($uploadableImages) { $item in
                 ImageItemView(
                     item: $item,
                     onDelete: { deleteImage(item) },
                     onTap: {
                         self.editingImageId = item.id
                         self.showActionSheet = true
                     }
                 )
                 .frame(width: 150, height: 150)
             }
             
             // 添加按钮
             if uploadableImages.count < maxCount {
                 AddButtonView {
                     self.editingImageId = nil
                     self.showActionSheet = true
                 }
             }
         }
     }
    
    
    // MARK: - Logic
    private func processSelectedImage(_ image: UIImage, name: String?) {
        // 直接调用我们之前写的优化方法
        startUploadProcess(for: image, name: name)
    }
    
    private func processSelectedData(_ data: Data, name: String?) {
        guard let image = UIImage(data: data) else {
            print("❌ 无法从文件数据创建图片")
            return
        }
        startUploadProcess(for: image, name: name)
    }
    
    private func startUploadProcess(for image: UIImage, name: String?) {
        
        
        let processedImage: UIImage
        let imageName: String
        
        // --- 1. 调用新的公共方法来处理图片 ---
        do {
            // 直接解构返回的元组
            (processedImage, imageName) = try compressionUIImage(originalImage: image, name: name)
        } catch {
            print("❌ \(error.localizedDescription)")
            self.editingImageId = nil // 重置状态
            return
        }
        
        // Case 1: We are editing an existing image
        if let editingId = editingImageId, let index = uploadableImages.firstIndex(where: { $0.id == editingId }) {
            
            // First, if the old image was successfully uploaded, remove its URL from the binding
            if case .success(let urlToDelete) = uploadableImages[index].status {
                uploadedUrls.removeAll { $0 == urlToDelete }
            }
            
            // 使用新图片和新名称创建更新项
            uploadableImages[index].image = processedImage
            uploadableImages[index].imageName = imageName
            uploadableImages[index].status = .uploading
            
            let itemToUpdate = uploadableImages[index]
            
            // 上传新图片
            uploadImage(image: processedImage, name: imageName) { result, img_id in
                handleUploadResult(for: itemToUpdate.id, result: result, img_id: img_id)
            }
            
        }
        // Case 2: We are adding a new image
        else if uploadableImages.count < maxCount {
            let newUploadableImage = UploadableImage(image: image, imageName: imageName)
            uploadableImages.append(newUploadableImage)
            
            uploadImage(image: image, name: imageName) { result, img_id  in
                handleUploadResult(for: newUploadableImage.id, result: result, img_id: img_id)
            }
        }
        
        // Reset the editing state
        self.editingImageId = nil
    }
    
    
    private func handleUploadResult(for id: UUID, result: Result<String, Error>,img_id: Int?) {
        DispatchQueue.main.async {
            guard let index = uploadableImages.firstIndex(where: { $0.id == id }) else { return }
            
            switch result {
            case .success(let url):
                // 防止重复添加
                if !uploadedUrls.contains(url) {
                    uploadableImages[index].status = .success(url: url)
                    uploadedUrls.append(url)// 更新绑定的外部数据
                }
                
                if img_id != nil {
                    uploadToPixverseID = img_id
                }
                
                
            case .failure:
                uploadableImages[index].status = .failed
            }
        }
    }
    
    private func deleteImage(_ imageToDelete: UploadableImage) {
        if case .success(let urlToDelete) = imageToDelete.status {
            uploadedUrls.removeAll { $0 == urlToDelete }
        }else {
            // 如果删除的是一个正在上传或上传失败的图片，它不在 upstreamUrls 里
            // 所以我们需要手动从本地列表移除
            uploadableImages.removeAll { $0.id == imageToDelete.id }
        }
        // (可选) 如果需要，也从已上传的URL列表中移除
        // 这需要更复杂的逻辑，比如根据ID或URL来匹配
    }
    
    // 模拟API调用
    private func uploadImage(image: UIImage, name: String, completion: @escaping (Result<String, Error>,Int?) -> Void) {
        Task {
            do {
                // 直接传递 [UIImage] 数组，选择上传为 PNG 格式
                print("正在上传 PNG 图片...")
                let format: ImageFormat = .jpeg(compressionQuality: 0.8)
                
                if uploadToPixverseID == nil ,threadId != nil  {
                    let resultPng = try await fileService.uploadImages(
                        images: [(imageInfo: image, name: name)],
                        threadId: self.threadId ?? "1111-AAAA-2222-BBBB",
                        format: format
                    )
                    //设置上传成功状态
                    completion(.success(resultPng.first!.url),nil)
                    print("✅ PNG 上传成功: \(resultPng)")
                    
                }else {
                    let resultPng = try await fileService.uploadImageToPixverse(
                        images: [(imageInfo: image, name: name)],
                        format: format
                    )
                    //设置上传成功状态
                    completion(.success(resultPng.img_url),resultPng.img_id)
                    print("✅Pixverse  PNG 上传成功: \(resultPng)")
                }
                
            } catch {
                print("❌ 上传失败: \(error.localizedDescription)")
                completion(.failure(error),nil)
            }
        }
    }
}


// MARK: - AddButtonView

struct AddButtonView: View {
    var action: () -> Void
    @Environment(\.colorScheme) private var colorScheme

    var body: some View {
        Button(action: action) {
            RoundedRectangle(cornerRadius: 12)
                .stroke(style: StrokeStyle(lineWidth: 1, dash: [2]))
                .foregroundColor(DesignSystem.Colors.primary)
                .frame(width: 100, height: 100)
                .overlay(
                    Image(systemName: "plus")
                        .font(.largeTitle)
                        .foregroundColor(
                            colorScheme == .light ? DesignSystem.Colors.primary : .white
                        )
                )
        }
    }
}

//MARK: - ImageItemView
struct ImageItemView: View {
    @Binding var item: UploadableImage
    var onDelete: () -> Void
    var onTap: () -> Void
    
    
    var body: some View {
        ZStack(alignment: .topTrailing) {
            // The main container that defines the 100x100 size and background
            RoundedRectangle(cornerRadius: 10)
                .fill(Color(UIColor.secondarySystemBackground)) // Use a subtle background color
            //                .frame(width: 150, height: 150)
                .overlay(
                    imageDisplayView
                )
                .clipShape(RoundedRectangle(cornerRadius: 10)) // Clip everything to the rounded rectangle shape
                .overlay(
                    // The status overlay remains on top
                    overlayContent
                        .clipShape(RoundedRectangle(cornerRadius: 10))
                )
                .onTapGesture {
                    onTap()
                }
            // 删除按钮 (保持不变)
            Button(action: onDelete) {
                Image(systemName: "xmark.circle.fill")
                    .foregroundColor(.gray)
                    .background(Circle().fill(Color.white))
                    .font(.title2)
            }
            .offset(x: -4, y: 4)
        }
    }
    
    @ViewBuilder
        private var imageDisplayView: some View {
            // 根据状态决定如何显示图片
            switch item.status {
            case .success(let urlString):
                // 如果是成功状态，并且有URL，使用AsyncImage加载网络图片
                AsyncImage(url: URL(string: urlString)) { phase in
                    switch phase {
                    case .success(let image):
                        image
                            .resizable()
                            .aspectRatio(contentMode: .fit)
                            .padding(4)
                    case .failure:
                        // 加载失败时显示错误图标
                        Image(systemName: "photo.fill.on.rectangle.fill")
                            .font(.largeTitle)
                            .foregroundColor(.gray)
                    case .empty:
                        // 正在加载时显示进度条
                        ProgressView()
                    @unknown default:
                        EmptyView()
                    }
                }
            default:
                // 对于 .uploading 或 .failed 状态，显示本地的 UIImage
                // 图像视图使用.fit ，并被放置在容器中
                Image(uiImage: item.image)
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .padding(4)
            }
        }
    
    
    // 为清晰起见，将覆盖逻辑提取到计算属性中
    @ViewBuilder
    private var overlayContent: some View {
        // 单个开关现在控制每个状态的整个覆盖
        switch item.status {
        case .uploading:
            // State: Uploading -> Show both dark background and spinner
            ZStack {
                Color.black.opacity(0.4)
                ProgressView()
                    .progressViewStyle(CircularProgressViewStyle(tint: .white))
                    .scaleEffect(1.5)
            }
        case .failed:
            // State: Failed -> Show both dark background and error icon
            ZStack {
                Color.black.opacity(0.4)
                Image(systemName: "exclamationmark.triangle.fill")
                    .foregroundColor(.red)
                    .font(.title)
            }
        case .success:
            // State: Success -> Show nothing in the overlay
            EmptyView()
        }
    }
}
