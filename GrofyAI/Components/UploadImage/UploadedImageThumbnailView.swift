import Kingfisher
import SwiftUI

// MARK: - 上传图片缩略图管理组件

struct UploadedImageThumbnailView: View {
    @Binding var uploadedImages: [String]
    let onRemove: (String) -> Void
    let uploadingCount: Int

    private let thumbnailSize: CGFloat = 64

    var body: some View {
        if !uploadedImages.isEmpty || uploadingCount > 0 {
            VStack(alignment: .leading, spacing: DesignSystem.Spacing.xs) {
                HStack {
                    Text("已上传图片")
                        .font(DesignSystem.Typography.caption)
                        .foregroundColor(DesignSystem.Colors.textSecondary)

                    Spacer()

                    Text("\(uploadedImages.count) 张")
                        .font(DesignSystem.Typography.caption)
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                }
                .padding(.horizontal, DesignSystem.Spacing.lg)

                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: DesignSystem.Spacing.sm) {
                        ForEach(Array(uploadedImages.enumerated()), id: \.offset) { _, imageUrl in
                            ThumbnailImageView(
                                imageUrl: imageUrl,
                                size: thumbnailSize,
                                onRemove: {
                                    onRemove(imageUrl)
                                }
                            )
                        }

                        ForEach(0..<uploadingCount, id: \.self) { _ in
                            LoadingThumbnailView(size: thumbnailSize)
                        }
                    }
                    .padding(.horizontal, DesignSystem.Spacing.lg)
                    .padding(.vertical, 3)
                }
                .clipped(antialiased: false)
            }
            .padding(.vertical, DesignSystem.Spacing.sm)
            .background(DesignSystem.Colors.backgroundCard)
            .cornerRadius(DesignSystem.Rounded.md)
        }
    }
}

// MARK: - 上传中缩略图

private struct LoadingThumbnailView: View {
    let size: CGFloat

    var body: some View {
        RoundedRectangle(cornerRadius: DesignSystem.Rounded.sm)
            .fill(DesignSystem.Colors.backgroundPage)
            .overlay {
                VStack(spacing: DesignSystem.Spacing.xs) {
                    ProgressView()
                        .tint(DesignSystem.Colors.primary)
                        .scaleEffect(0.7)

                    Text("上传中")
                        .font(DesignSystem.Typography.caption)
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                }
            }
            .frame(width: size, height: size)
            .overlay(
                RoundedRectangle(cornerRadius: DesignSystem.Rounded.sm)
                    .stroke(DesignSystem.Colors.border, lineWidth: DesignSystem.BorderWidth.thin)
            )
    }
}

// MARK: - 单个缩略图组件

private struct ThumbnailImageView: View {
    let imageUrl: String
    let size: CGFloat
    let onRemove: () -> Void

    @State private var isLoading = true
    @State private var loadingFailed = false

    var body: some View {
        ZStack {
            Group {
                if loadingFailed {
                    errorStateView
                } else {
                    imageContentView
                }
            }
            .frame(width: size, height: size)
            .cornerRadius(DesignSystem.Rounded.sm)
            .overlay(
                RoundedRectangle(cornerRadius: DesignSystem.Rounded.sm)
                    .stroke(
                        loadingFailed ? Color.clear : DesignSystem.Colors.border,
                        lineWidth: DesignSystem.BorderWidth.thin
                    )
            )

            removeButton
                .offset(x: size / 2 - 4, y: -size / 2 + 4)
        }
        .frame(width: size, height: size)
    }

    @ViewBuilder
    private var imageContentView: some View {
        KFImage(URL(string: imageUrl))
            .placeholder {
                loadingStateView
            }
            .onSuccess { _ in
                withAnimation(.easeInOut(duration: 0.2)) {
                    isLoading = false
                }
            }
            .onFailure { _ in
                withAnimation(.easeInOut(duration: 0.2)) {
                    isLoading = false
                    loadingFailed = true
                }
            }
            .retry(maxCount: 2)
            .fade(duration: 0.25)
            .resizable()
            .aspectRatio(contentMode: .fill)
            .clipped()
    }

    @ViewBuilder
    private var loadingStateView: some View {
        RoundedRectangle(cornerRadius: DesignSystem.Rounded.sm)
            .fill(DesignSystem.Colors.backgroundPage)
            .overlay {
                VStack(spacing: DesignSystem.Spacing.xs) {
                    ProgressView()
                        .tint(DesignSystem.Colors.primary)
                        .scaleEffect(0.7)

                    Text("加载中")
                        .font(DesignSystem.Typography.caption)
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                }
            }
    }

    @ViewBuilder
    private var errorStateView: some View {
        RoundedRectangle(cornerRadius: DesignSystem.Rounded.sm)
            .fill(DesignSystem.Colors.error.opacity(0.1))
            .overlay {
                VStack(spacing: DesignSystem.Spacing.xs) {
                    ZStack {
                        Circle()
                            .fill(DesignSystem.Colors.error.opacity(0.15))
                            .frame(width: 24, height: 24)

                        Image(systemName: "exclamationmark.triangle.fill")
                            .font(.system(size: 12, weight: .medium))
                            .foregroundColor(DesignSystem.Colors.error)
                    }

                    VStack(spacing: 2) {
                        Text("上传失败")
                            .font(DesignSystem.Typography.caption)
                            .foregroundColor(DesignSystem.Colors.error)
                            .multilineTextAlignment(.center)

                        Button("点击重试") {
                            withAnimation(.easeInOut(duration: 0.2)) {
                                loadingFailed = false
                                isLoading = true
                            }
                        }
                        .font(.system(size: 9, weight: .medium))
                        .foregroundColor(DesignSystem.Colors.error)
                        .buttonStyle(.plain)
                    }
                }
            }
            .overlay(
                RoundedRectangle(cornerRadius: DesignSystem.Rounded.sm)
                    .stroke(DesignSystem.Colors.error.opacity(0.3), lineWidth: 1)
            )
    }

    @ViewBuilder
    private var removeButton: some View {
        Button(action: {
            // 添加触觉反馈
            let impactFeedback = UIImpactFeedbackGenerator(style: .light)
            impactFeedback.impactOccurred()

            onRemove()
        }) {
            Image(systemName: "xmark.circle.fill")
                .font(.system(size: 13))
                .foregroundColor(.white)
                .background(
                    Circle()
                        .fill(Color.gray.opacity(0.8))
                        .frame(width: 14, height: 14)
                )
        }
        .buttonStyle(.plain)
    }
}
