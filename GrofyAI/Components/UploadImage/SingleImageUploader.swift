//
//  SingleImageUploader.swift
//  GrofyAI
//
//  Created by kissy on 2025/6/17.
//
import SwiftUI

/// 一个适配器视图，用于将处理多图的 `ImageUploaderView` 适配为仅处理单张图片。
/// 它对外暴露一个 String 绑定，在内部管理与 `ImageUploaderView` 的数组绑定，并确保两者状态同步。
struct SingleImageUploader: View {
    
    // 对外暴露的接口：一个单值的 String 绑定
    @Binding var imageUrl: String
    
    // 内部状态：一个数组，用于与我们现有的多值组件通信
    @State private var imageUrls: [String]  = []
    
    let threadId: String
    
    // 1. 自定义初始化器
        // 这是实现健壮数据同步的关键
        init(imageUrl: Binding<String>, threadId: String) {
            // 使用 _property 语法来直接访问属性包装器本身
            self._imageUrl = imageUrl
            self.threadId = threadId
            
            // 在初始化时，就用外部 Binding 的初始值来设置内部 @State 的初始值
            // 这样就保证了视图一创建，内外状态就是同步的
            self._imageUrls = State(initialValue: [imageUrl.wrappedValue].filter { !$0.isEmpty })
        }
    
    var body: some View {
        // 使用我们已有的、为多值设计的组件，
        // 并将 maxCount 硬编码为 1，强制其为单图模式。
        ImageUploaderView(
            uploadedUrls: $imageUrls,
            maxCount: 1,
            threadId: threadId
        )
        // 当内部数组变化时（例如，用户上传或删除了图片），更新外部的单值字符串
        .onChange(of: imageUrls) { newUrls in
            // 适配逻辑：当内部数组变化时，更新外部的单值字符串
            // 如果新数组的第一个元素与外部值不同，则更新外部值
            let newFirstUrl = newUrls.first ?? ""
            if imageUrl != newFirstUrl {
                imageUrl = newFirstUrl
            }
        }
        // 当外部字符串变化时（例如，父视图重置了表单），更新内部的数组
        .onChange(of: imageUrl) { newUrl in
            // 如果外部新值与内部数组所代表的值不同，则更新内部数组
            let internalUrl = imageUrls.first ?? ""
            if newUrl != internalUrl {
                imageUrls = [newUrl].filter { !$0.isEmpty }
            }
        }
    }
}
