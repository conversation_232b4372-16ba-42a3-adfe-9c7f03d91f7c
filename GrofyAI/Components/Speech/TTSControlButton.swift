import SwiftUI

// MARK: - TTS控制按钮组件

struct TTSControlButton: View {
    let text: String
    let isEnabled: Bool
    let action: () -> Void
    @ObservedObject var speechService: AzureSpeechSDKService

    @State private var cachedButtonColor: Color
    @State private var cachedOpacity: Double

    init(text: String, isEnabled: Bool = true, speechService: AzureSpeechSDKService, action: @escaping () -> Void) {
        self.text = text
        self.isEnabled = isEnabled
        self.speechService = speechService
        self.action = action

        let initialColor = Self.buttonColor(for: speechService.ttsDisplayState)
        let initialOpacity = isEnabled ? 1.0 : 0.6

        _cachedButtonColor = State(initialValue: initialColor)
        _cachedOpacity = State(initialValue: initialOpacity)
    }

    var body: some View {
        Button(action: action) {
            HStack(spacing: DesignSystem.Spacing.xs) {
                if speechService.ttsDisplayState.isActive {
                    HStack(spacing: 4) {
                        switch speechService.ttsDisplayState {
                        case .preparing:
                            ProgressView()
                                .scaleEffect(0.8)
                                .progressViewStyle(CircularProgressViewStyle(tint: cachedButtonColor))

                            Text("准备中...")
                                .font(.caption2)
                                .foregroundColor(cachedButtonColor)
                                .transition(.opacity.combined(with: .scale))

                        case .preparingCached:
                            ProgressView()
                                .scaleEffect(0.8)
                                .progressViewStyle(CircularProgressViewStyle(tint: cachedButtonColor))

                            Text("缓存准备中...")
                                .font(.caption2)
                                .foregroundColor(cachedButtonColor)
                                .transition(.opacity.combined(with: .scale))

                        case .synthesizing(let progress):
                            if progress > 0 {
                                CircularProgressView(progress: progress, color: cachedButtonColor)
                                    .scaleEffect(0.8)
                            } else {
                                ProgressView()
                                    .scaleEffect(0.8)
                                    .progressViewStyle(CircularProgressViewStyle(tint: cachedButtonColor))
                            }

                            Text("合成中...")
                                .font(.caption2)
                                .foregroundColor(cachedButtonColor)
                                .transition(.opacity.combined(with: .scale))

                        case .playing where speechService.isPlayingText(text):
                            Image(systemName: buttonIcon)
                                .foregroundColor(cachedButtonColor)
                                .font(.system(size: DesignSystem.FontSize.lg, weight: DesignSystem.FontWeight.medium))

                            TTSPlaybackIndicator()
                                .transition(.opacity.combined(with: .scale))

                        case .error:
                            Image(systemName: "exclamationmark.triangle.fill")
                                .foregroundColor(.red)
                                .font(.caption)
                                .transition(.opacity.combined(with: .scale))

                        default:
                            EmptyView()
                        }
                    }
                } else {
                    Image(systemName: buttonIcon)
                        .foregroundColor(cachedButtonColor)
                        .font(.system(size: DesignSystem.FontSize.lg, weight: DesignSystem.FontWeight.medium))
                        .animation(.easeInOut(duration: 0.2), value: cachedButtonColor)
                }
            }
            .animation(.easeInOut(duration: 0.3), value: speechService.state)
        }
        .buttonStyle(.plain)
        .disabled(!isButtonEnabled)
        .opacity(cachedOpacity)
        .scaleEffect(speechService.isSynthesizing ? 1.05 : 1.0)
        .animation(.easeInOut(duration: 0.2), value: speechService.isSynthesizing)
        .onChange(of: speechService.ttsDisplayState) { _ in
            updateCachedStyles()
        }
        .onChange(of: isEnabled) { enabled in
            cachedOpacity = enabled ? 1.0 : 0.6
        }
    }

    // MARK: - 计算属性

    private var buttonIcon: String {
        switch speechService.ttsDisplayState {
        case .preparing:
            return "clock.circle"
        case .preparingCached:
            return "tray.circle"
        case .synthesizing:
            return "waveform.circle"
        case .playing where speechService.isPlayingText(text):
            return "pause.circle.fill"
        case .paused:
            return "play.circle.fill"
        case .error:
            return "exclamationmark.circle.fill"
        default:
            return "speaker.wave.2.circle"
        }
    }

    private var isButtonEnabled: Bool {
        return isEnabled && !text.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
    }

    // MARK: - 样式更新

    private func updateCachedStyles() {
        cachedButtonColor = Self.buttonColor(for: speechService.ttsDisplayState)
    }

    private static func buttonColor(for displayState: TTSDisplayState) -> Color {
        switch displayState {
        case .preparing:
            return .blue
        case .preparingCached:
            return .cyan
        case .synthesizing:
            return .orange
        case .playing:
            return DesignSystem.Colors.primary
        case .paused:
            return DesignSystem.Colors.primary.opacity(0.7)
        case .error:
            return .red
        default:
            return DesignSystem.Colors.textSecondary
        }
    }
}

// MARK: - TTS播放状态可视化指示器

struct TTSPlaybackIndicator: View {
    @State private var animationPhase: Double = 0

    var body: some View {
        HStack(spacing: 2) {
            ForEach(0..<3, id: \.self) { index in
                RoundedRectangle(cornerRadius: 1)
                    .fill(DesignSystem.Colors.primary)
                    .frame(width: 3, height: 8)
                    .scaleEffect(y: animationScale(for: index))
                    .animation(
                        .easeInOut(duration: 0.6)
                            .repeatForever()
                            .delay(Double(index) * 0.2),
                        value: animationPhase
                    )
            }
        }
        .onAppear {
            animationPhase = 1.0
        }
        .onDisappear {
            animationPhase = 0.0
        }
    }

    private func animationScale(for index: Int) -> CGFloat {
        let scales: [CGFloat] = [0.5, 1.0, 0.7]
        return scales[index]
    }
}
