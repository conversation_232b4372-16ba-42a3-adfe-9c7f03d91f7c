import SwiftUI

// MARK: - Membership导航栏组件

struct MembershipNavigationBar: View {
    @Binding var selectedTab: Int
    let tabs: [(name: String, themeColor: Color, gradientColors: [Color])]
    let onBack: () -> Void
    @Namespace private var tabNamespace

    var body: some View {
        HStack {
            MembershipBackButton(onBack: onBack)
                .padding(.leading, DesignSystem.Spacing.lg)

            Spacer()

            // 中间标签组 - 使用Spacer实现居中
            HStack(spacing: 30) {
                ForEach(0..<tabs.count, id: \.self) { index in
                    VStack(spacing: 8) {
                        Text(tabs[index].name)
                            .font(.system(size: 17, weight: selectedTab == index ? .semibold : .regular))
                            .foregroundColor(selectedTab == index ? .white : Color.white.opacity(0.6))

                        if selectedTab == index {
                            Capsule()
                                .fill(
                                    LinearGradient(
                                        gradient: Gradient(stops: [
                                            .init(color: tabs[index].gradientColors[0], location: 0.0),
                                            .init(color: tabs[index].gradientColors[1], location: 0.5),
                                            .init(
                                                color: tabs[index].gradientColors.count > 2 ? tabs[index]
                                                    .gradientColors[2] : tabs[index].gradientColors[1],
                                                location: 1.0
                                            ),
                                        ]),
                                        startPoint: .leading,
                                        endPoint: .trailing
                                    )
                                )
                                .frame(width: 40, height: 3)
                                .mask(
                                    TaperedIndicator()
                                        .padding(.leading, 3)
                                )
                                .matchedGeometryEffect(id: "tabIndicator", in: tabNamespace)
                        } else {
                            Capsule()
                                .fill(Color.clear)
                                .frame(width: 40, height: 3)
                        }
                    }
                    .animation(.easeInOut(duration: 0.3), value: selectedTab)
                    .onTapGesture {
                        withAnimation(.easeInOut(duration: 0.3)) {
                            selectedTab = index
                        }
                    }
                }
            }

            Spacer()
        }
        .frame(height: 56)
        .background(
            DesignSystem.Colors.membershipPurchaseBackground
                .ignoresSafeArea(.all, edges: .all)
        )
    }
}

// MARK: - 梯形指示器Shape

struct TaperedIndicator: Shape {
    let cornerRadius: CGFloat = 1.5

    func path(in rect: CGRect) -> Path {
        Path { path in
            let midY = rect.height / 2

            // 左侧圆角开始
            path.move(to: CGPoint(x: cornerRadius, y: 0))

            // 右上边缘
            path.addLine(to: CGPoint(x: rect.width * 0.3, y: midY * 0.2))
            path.addLine(to: CGPoint(x: rect.width - cornerRadius, y: midY * 0.8))

            // 右侧圆角
            path.addQuadCurve(
                to: CGPoint(x: rect.width - cornerRadius, y: midY * 1.2),
                control: CGPoint(x: rect.width, y: midY)
            )

            // 右下边缘
            path.addLine(to: CGPoint(x: rect.width * 0.3, y: midY * 1.8))
            path.addLine(to: CGPoint(x: cornerRadius, y: rect.height))

            // 左下圆角
            path.addQuadCurve(
                to: CGPoint(x: 0, y: rect.height - cornerRadius),
                control: CGPoint(x: 0, y: rect.height)
            )

            // 左侧边缘
            path.addLine(to: CGPoint(x: 0, y: cornerRadius))

            // 左上圆角
            path.addQuadCurve(
                to: CGPoint(x: cornerRadius, y: 0),
                control: CGPoint(x: 0, y: 0)
            )

            path.closeSubpath()
        }
    }
}

// MARK: - 会员页面专用返回按钮

struct MembershipBackButton: View {
    let onBack: () -> Void

    var body: some View {
        Button(action: onBack) {
            Image(systemName: "chevron.left")
                .font(.system(size: 18, weight: .medium))
                .foregroundColor(.white)
        }
        .buttonStyle(PlainButtonStyle())
        .contentShape(Rectangle())
    }
}
