import Foundation

// MARK: - 文件对话参数类型

struct FileChatCompletionReq: Codable {
    let knowledgeId: Int
    let messages: String
    let threadId: String
    let parentId: String?
    let isFirst: Bool

    private enum CodingKeys: String, CodingKey {
        case knowledgeId = "knowledge_id"
        case messages
        case threadId = "thread_id"
        case parentId = "parent_id"
        case isFirst = "is_first"
    }
}
