//
//  StableDiffusion3_5ImageReq.swift
//  GrofyAI
//
//  Created by kissy on 2025/6/19.
//

//MARK: StableDiffusion3_5ImageReq
struct StableDiffusion3_5ImageReq:Encodable{
    //会话 Id
    let threadId: String
    //提示词
    let prompt: String
    //生成模式：TEXT_TO_IMAGE 文生图｜IMAGE_TO_IMAGE 图生图,可用值:TEXT_TO_IMAGE,IMAGE_TO_IMAGE
    let mode: StableDiffusion3_5ImageAi.Mode.RawValue
    //参考图,当mode为IMAGE_TO_IMAGE时该项必须
    let imageUrl: String?
    //这个参数控制图像参数对生成图像的影响程度。值为0将产生与输入完全相同的图像。值为1则相当于你根本没有传入任何图像，该参数仅在IMAGE_TO_IMAGE有效。
    let strength: Int?
    //长和宽的比率,可用值:RATIO_16_9,RATIO_9_16,RATIO_21_9,RATIO_9_21,RATIO_3_2,RATIO_2_3,RATIO_4_5,RATIO_5_4,RATIO_1_1
    let aspect_ratio: StableDiffusion3_5ImageAi.AspectRatio.RawValue?
    //模型,可用值:SD3_5_LARGE,SD3_5_LARGE_TURBO,SD3_5_MEDIUM
    let model: StableDiffusion3_5ImageAi.Model.RawValue?
    //输出格式:模型,可用值:JPEG,PNG
    let output_format: StableDiffusion3_5ImageAi.OutputFormat.RawValue?
    //输出风格,可用值:STYLE_3D_MODEL,STYLE_ANALOG_FILM,STYLE_ANIME,STYLE_CINEMATIC,STYLE_COMIC_BOOK,STYLE_DIGITAL_ART,STYLE_ENHANCE,STYLE_FANTASY_ART,STYLE_ISOMETRIC,STYLE_LINE_ART,STYLE_LOW_POLY,STYLE_MODELING_COMPOUND,STYLE_NEON_PUNK,STYLE_ORIGAMI,STYLE_PHOTOGRAPHIC,STYLE_PIXEL_ART,STYLE_TILE_TEXTURE
    let style_preset: StableDiffusion3_5ImageAi.StylePreset.RawValue?
    //负面提示词
    let negative_prompt: String?
}
