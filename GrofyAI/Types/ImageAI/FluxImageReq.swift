//
//  FluxImageReq.swift
//  GrofyAI
//
//  Created by kissy on 2025/6/19.
//

//MARK: FluxImageReq
struct FluxImageReq: Codable{
    //会话 Id
    let threadId: String
    //Flux 生图模型,可用值:FLUX_DEV, FLUX_PRO_1_1, FLUX_PRO_1_1_ULTRA,
    let model: FluxImageAi.Model.RawValue
    //提示词:用于图像生成的提示词
    let prompt: String
    //参考图，以base64格式提供
    let image_url : String?
    //提示词上采样:是否对提示词执行上采样。如果启用，会自动修改提示词以获得更具创造性的生成结果
    let prompt_upsampling: Bool
    //用于重现性的可选种子值。如果未提供，将使用随机种子
    let seed: Int?
    //图像的长宽比，范围在21:9到9:21之间,可用值:RATIO_16_9,RATIO_9_16,RATIO_21_9,RATIO_9_21,RATIO_3_2,RATIO_2_3,RATIO_4_5,RATIO_5_4,RATIO_1_1,
    let aspect_ratio: FluxImageAi.AspectRatio.RawValue
    //安全容忍度:输入和输出审核的容忍级别。范围在0到6之间，0表示最严格，6表示最宽松,
    let safety_tolerance: Int?
    //输出格式:生成图像的输出格式。可以是'jpeg'或'png'
    let output_format: FluxImageAi.OutputFormat.RawValue
    //原始模式:生成较少处理、更自然的图像
    let raw: Bool?
    //混合度:提示词和图像提示之间的混合程度,
    let image_prompt_strength: Double?
}
