//
//  MidjourneyImageReq.swift
//  GrofyAI
//
//  Created by kissy on 2025/6/19.
//

//MARK: MidjourneyImageReq
struct MidjourneyImageReq:Encodable{
    let threadId: String
    //提示词:用于图像生成的提示词
    let prompt: String
    //参考图:参考图
    let base64Array: [String]?
    //宽高比,可用值:RATIO_16_9,RATIO_9_16,RATIO_21_9,RATIO_9_21,RATIO_3_2,RATIO_2_3,RATIO_4_5,RATIO_5_4,RATIO_1_1
    let aspectRatio: MidjourneyImageAi.AspectRatio.RawValue?
    //风格,可用值:CYBERPUNK,WARFRAME,ACGN,JAPANESE_MANGA,INK_WASH,ORIGINAL,LANDSCAPE,ILLUSTRATION,MANGA,MODERN_ORGANIC,GENESIS,POSTER_STYLE,SURREALISM,SKETCH,REALISM,WATERCOLOR,CUBISM,BLACK_AND_WHITE,FILM_PHOTOGRAPHY,CINEMATIC
    let style: MidjourneyImageAi.Style.RawValue?
    //modes,可用值:RELAX,FAST,TURBO
    let modes: MidjourneyImageAi.Mode.RawValue?
}
