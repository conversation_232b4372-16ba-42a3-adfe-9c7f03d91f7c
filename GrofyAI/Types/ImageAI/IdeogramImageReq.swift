//
//  IdeogramImageReq.swift
//  GrofyAI
//
//  Created by kissy on 2025/6/19.
//

//MARK: IdeogramImageReq
struct IdeogramImageReq:Encodable{
    //会话 Id
    let threadId: String
    //提示词:用于图像生成的提示词
    let prompt: String
    //随机种子
    let seed: Int?
    //长宽比:图像的长宽比，用于确定图像的分辨率。不能与resolution同时使用,可用值:ASPECT_3_1,ASPECT_1_3,ASPECT_1_1,ASPECT_3_4,ASPECT_4_3,ASPECT_2_3,ASPECT_3_2,ASPECT_16_9,ASPECT_9_16,ASPECT_16_10,ASPECT_10_16,
    let aspect_ratio: IdeogramImageAi.AspectRatio.RawValue?
    //模型:用于生成或编辑图像的模型。/generate 和 /remix支持所有模型类型，但/edit仅支持V_2和V_2_TURBO,可用值:V_2,V_2_TURBO
    let model: IdeogramImageAi.Model.RawValue
    let speed: IdeogramImageAi.Speed.RawValue
    //Magic Prompt选项:确定是否在生成请求时使用MagicPrompt,可用值:AUTO,ON,OFF
    let magic_prompt: IdeogramImageAi.MagicPromptOption.RawValue?
    //风格类型:生成时使用的风格类型；这仅适用于V_2及以上版本的模型，不应为V_1版本的模型指定,可用值:AUTO,GENERAL,REALISTIC,DESIGN,RENDER_3D,ANIME
    let style_type: IdeogramImageAi.StyleType.RawValue?
    //反向提示词:仅适用于V_1、V_1_TURBO、V_2和V_2_TURBO模型版本。描述要从图像中排除的内容。提示词中的描述优先于反向提示词中的描述
    let negative_prompt: String?
    let image_Url: String?
}
