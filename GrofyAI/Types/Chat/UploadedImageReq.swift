import Foundation

/// 上传图片请求参数，用于图片对话API
struct UploadedImageReq: Codable {
    let file_id: String
    let file_name: String
    let file_url: String

    init(fileId: String, fileName: String, fileUrl: String) {
        file_id = fileId
        file_name = fileName
        file_url = fileUrl
    }

    /// 从UploadImagesRes转换
    init(from uploadRes: UploadImagesRes) {
        file_id = uploadRes.effectiveFileId
        file_name = uploadRes.originalName
        file_url = uploadRes.url
    }
}
