import Foundation

enum AuthSource: String, Codable {
    case google = "GOOGLE"
    case apple = "APPLE"
    case email = "EMAIL"

    var displayName: String {
        switch self {
        case .google:
            return "Google"
        case .apple:
            return "Apple"
        case .email:
            return "Email"
        }
    }
}

struct UserExistsData: Codable {
    let exists: Bool
    let authSource: AuthSource?
    let passwordEnable: Bool?
}
