import Foundation

// MARK: - 格式支持类型

struct FormatSupport: Codable {
    let text: Bool?
    let image: Bool?
    let audio: Bool?
    let function: Bool?

    // 提供安全的访问方法
    var safeText: Bool {
        return text ?? false
    }

    var safeImage: Bool {
        return image ?? false
    }

    var safeAudio: Bool {
        return audio ?? false
    }

    var safeFunction: Bool {
        return function ?? false
    }
}

// MARK: - 模型级别枚举

enum ModelLevel: String, Codable {
    case basic = "BASIC"
    case advanced = "ADVANCED"
    case expert = "EXPERT"

    var displayName: String {
        switch self {
        case .basic: return "基础"
        case .advanced: return "高级"
        case .expert: return "专家"
        }
    }
}

// MARK: - LLM响应类型

struct LLMListRes: Codable {
    let id: Int?
    let name: String?
    let code: String?
    let provider: String?
    let releaseDate: String?
    let icon: String?
    let summary: String?
    let level: String?
    let cost: Int?
    let inputFormat: FormatSupport?
    let translations: String?

    private enum CodingKeys: String, CodingKey {
        case id
        case name
        case code
        case provider
        case icon
        case summary
        case level
        case releaseDate
        case cost
        case inputFormat
        case translations
    }

    // MARK: - 便利属性，提供默认值

    /// 安全的ID，提供默认值
    var safeId: Int {
        return id ?? 0
    }

    /// 安全的模型名称，提供默认值
    var safeName: String {
        return name ?? "未知模型"
    }

    /// 安全的模型代码，提供默认值
    var safeCode: String {
        return code ?? "unknown"
    }

    /// 安全的提供商名称，提供默认值
    var safeProvider: String {
        return provider ?? "未知提供商"
    }

    /// 安全的摘要，提供默认值
    var safeSummary: String {
        return summary ?? "暂无描述"
    }

    /// 安全的级别，提供默认值（转换为枚举）
    var safeLevel: ModelLevel {
        guard let levelString = level else { return .basic }
        return ModelLevel(rawValue: levelString) ?? .basic
    }


}
