//
//  PollingResultReq.swift
//  GrofyAI
//
//  Created by kissy on 2025/6/20.
//
import Foundation

enum AgentType: String, Decodable {
    case image = "IMAGE"
    case video = "VIDEO"
    case effect = "EFFECT"
}


//MARK: UserArtWorksPageReq
struct UserArtWorksPageReq : Encodable{
    let size: String
    let current: String
    let agentType: AgentType.RawValue
}


//MARK: UserArtWorksPageRes
typealias UserArtWorksPageRes = [String:[ArtWorksCell]]

struct ArtWorksCell: Identifiable, Equatable {
    let id: Int
    let taskId: String?
    let title: String?
    let subTitle: String?
    let cover: String?
    let categoryId: Int?
    let links: [String]?
    let status: UserArtWorkRes.Status?
    let provider: String?
    let model: String?
    let agentSubtype: String?
    let agentType: AgentType
    let ownerType: String?
    let statusDescribe: String?
    let createDate: String?
    let creationDay: String?
    let updateDate: String
    
    // 将 inputFormat 定义为一个字符串到任意类型的字典
    let inputFormat: [String: Any]
}

extension ArtWorksCell: Decodable {
    // 定义顶层 JSON 的 key
    enum CodingKeys: String, CodingKey {
        case id,taskId,title,subTitle,cover,categoryId,links, status, provider, model,agentSubtype,agentType,ownerType,statusDescribe, creationDay,createDate, updateDate, inputFormat
    }
    
    // 自定义解码器
    init(from decoder: Decoder) throws {
        // 1. 获取顶层容器
        let container = try decoder.container(keyedBy: CodingKeys.self)
        
        // 2. 解码所有常规属性， 使用 decode 对非可选属性解码
        self.id = try container.decode(Int.self, forKey: .id)
        //        self.status = try container.decode(UserArtWorkRes.Status.self, forKey: .status)
        //        self.provider = try container.decode(String.self, forKey: .provider)
        //        self.model = try container.decode(String.self, forKey: .model)
        self.agentType = try container.decode(AgentType.self, forKey: .agentType)
        //        self.creationDay = try container.decode(String.self, forKey: .creationDay)
        self.updateDate = try container.decode(String.self, forKey: .updateDate)
        
        //        self.taskId = try container.decode(String.self, forKey: .taskId)
        //        self.title = try container.decode(String.self, forKey: .title)
        //        self.subTitle = try container.decode(String.self, forKey: .subTitle)
        //        self.cover = try container.decode(String.self, forKey: .cover)
        //        self.categoryId = try container.decode(Int.self, forKey: .categoryId)
        //        self.ownerType = try container.decode(String.self, forKey: .ownerType)
        
        //使用 decodeIfPresent 对可选属性解码
        self.links = try container.decodeIfPresent([String].self, forKey: .links)
        self.statusDescribe = try container.decodeIfPresent(String.self, forKey: .statusDescribe)
        self.createDate = try container.decodeIfPresent(String.self, forKey: .createDate)
        self.agentSubtype = try container.decodeIfPresent(String.self, forKey: .agentSubtype)
        self.status = try container.decodeIfPresent(UserArtWorkRes.Status.self, forKey: .status)
        self.taskId = try container.decodeIfPresent(String.self, forKey: .taskId)
        self.title = try container.decodeIfPresent(String.self, forKey: .title)
        self.subTitle = try container.decodeIfPresent(String.self, forKey: .subTitle)
        self.cover = try container.decodeIfPresent(String.self, forKey: .cover)
        self.categoryId = try container.decodeIfPresent(Int.self, forKey: .categoryId)
        self.ownerType = try container.decodeIfPresent(String.self, forKey: .ownerType)
        
        self.provider = try container.decodeIfPresent(String.self, forKey: .provider)
        self.model = try container.decodeIfPresent(String.self, forKey: .model)
        self.creationDay = try container.decodeIfPresent(String.self, forKey: .creationDay)
        
        // 创建一个用于存储结果的临时字典
        // 1，将JSON对象解码到类型安全的[String: JSONValue]字典中。
        // JSONValue是可解码的
        let inputFormatValues = try container.decode([String: JSONValue].self, forKey: .inputFormat)
        
        // 使用helper属性将其转换为所需的[String: Any]类型
        self.inputFormat = inputFormatValues.mapValues { $0.anyValue }
        
        //        // 3. 解码 inputFormat 字符串
        //        let inputFormatString = try container.decode(String.self, forKey: .inputFormat)
        //        
        //        // 4. 将字符串转换为 Data
        //        guard let inputFormatData = inputFormatString.data(using: .utf8) else {
        //            throw DecodingError.dataCorruptedError(
        //                forKey: .inputFormat,
        //                in: container,
        //                debugDescription: "InputFormat string could not be converted to Data."
        //            )
        //        }
        //        
        //        // 5. 使用 JSONSerialization 将 Data 解析为 Any
        //        let jsonObject = try JSONSerialization.jsonObject(with: inputFormatData, options: [])
        //        
        //        //        // 6. 将 Any 类型转换为 [String: Any] 字典
        //        //        guard let formatDictionary = jsonObject as? [String: Any] else {
        //        //            throw DecodingError.dataCorruptedError(forKey: .inputFormat, in: container, debugDescription: "InputFormat JSON was not a dictionary.")
        //        //        }
        //        //
        //        //        self.inputFormat = formatDictionary
        //        
        //        
        //        // 4. 第一步：使用 JSONSerialization 解析为 [String: Any]
        //        guard let anyFormatDictionary = jsonObject as? [String: Any] else {
        //            // 如果 jsonObject 不是一个字典，就抛出错误
        //            throw DecodingError.dataCorruptedError(
        //                forKey: .inputFormat,
        //                in: container,
        //                debugDescription: "InputFormat JSON was not a dictionary of [String: Any]."
        //            )
        //        }
        //        
        ////        // 5. 第二步：手动将 [String: Any] 转换为 [String: String]
        ////        var stringFormatDictionary: [String: String] = [:]
        ////        for (key, value) in anyFormatDictionary {
        ////            // 尝试将 Any 类型的值转换为 String
        ////            if let stringValue = value as? String {
        ////                stringFormatDictionary[key] = stringValue
        ////            } else {
        ////                // 如果字典中有一个值不是字符串，则解码失败。
        ////                // 抛出一个清晰的错误，说明哪个 key 的值类型不匹配。
        ////                throw DecodingError.dataCorruptedError(
        ////                    forKey: .inputFormat,
        ////                    in: container,
        ////                    debugDescription: "The value for key '\(key)' in inputFormat was not a String."
        ////                )
        ////            }
        ////        }
        //        
        //        self.inputFormat = anyFormatDictionary
    }
}




// 1. 定义一个代表 UI 分区的 ViewModel
//    它应该是可识别的 (Identifiable) 和可哈希的 (Hashable)，方便与现代 UI 框架配合
struct ArtWorkSection: Identifiable, Hashable {
    let id: Date   // 使用 Date 作为唯一标识符，便于排序
    let dateText: String // 预先格式化好的日期字符串，如 "2025年6月20日"
    let weekdayText: String // 预先格式化好的星期字符串，如 "星期五"
    let artworks: [ArtWorksCell] // 该分区下的所有作品
    
    // ArtWorksCell 也需要是 Hashable
}

// 2. ArtWorksCell 也需要遵循 Hashable
//    如果 id 是唯一的，可以这样做：
extension ArtWorksCell: Hashable {
    // 编译器通常可以自动合成，如果不行再手动实现
    public static func == (lhs: ArtWorksCell, rhs: ArtWorksCell) -> Bool {
        return lhs.id == rhs.id && lhs.status == rhs.status // 可以只比较关心的字段
    }
    
    public func hash(into hasher: inout Hasher) {
        hasher.combine(id)
    }
}


struct AnyCodingKey: CodingKey {
    var stringValue: String
    var intValue: Int?

    init?(stringValue: String) {
        self.stringValue = stringValue
    }

    init?(intValue: Int) {
        self.stringValue = "\(intValue)"
        self.intValue = intValue
    }
}


enum JSONValue: Decodable, Equatable {
    case string(String)
    case int(Int)
    case double(Double)
    case bool(Bool)
    case object([String: JSONValue])
    case array([JSONValue])
    case null
    
    // Custom decoder
    init(from decoder: Decoder) throws {
        let container = try decoder.singleValueContainer()

        if let value = try? container.decode(Bool.self) {
            self = .bool(value)
        } else if let value = try? container.decode(Int.self) {
            self = .int(value)
        } else if let value = try? container.decode(Double.self) {
            self = .double(value)
        } else if let value = try? container.decode(String.self) {
            self = .string(value)
        } else if let value = try? container.decode([String: JSONValue].self) {
            // Recursive decoding for nested objects
            self = .object(value)
        } else if let value = try? container.decode([JSONValue].self) {
            // Recursive decoding for nested arrays
            self = .array(value)
        } else if container.decodeNil() {
            self = .null
        } else {
            throw DecodingError.typeMismatch(JSONValue.self, DecodingError.Context(codingPath: container.codingPath, debugDescription: "Unsupported JSON value"))
        }
    }
    
    // A helper to convert our specific JSONValue back to a generic [String: Any]
    // This is what your struct's property needs.
    var anyValue: Any {
        switch self {
        case .string(let value):
            return value
        case .int(let value):
            return value
        case .double(let value):
            return value
        case .bool(let value):
            return value
        case .object(let value):
            // Recursively convert the inner dictionary
            return value.mapValues { $0.anyValue }
        case .array(let value):
            // Recursively convert the inner array
            return value.map { $0.anyValue }
        case .null:
            return NSNull() // Use NSNull for nil/null values in an [String: Any] context
        }
    }
}
