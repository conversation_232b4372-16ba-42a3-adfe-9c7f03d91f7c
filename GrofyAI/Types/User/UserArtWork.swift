//
//  PollingResultRes.swift
//  GrofyAI
//
//  Created by kissy on 2025/6/20.
//
import Foundation

//MARK: - 请求参数
struct UserArtWorkReq : Encodable{
    let id: Int
}



//MARK: - 响应参数
struct UserArtWorkRes: Decodable {
    //PENDING、RUNNING、SUCCESS、FAILED、UN_KNOWN
    enum Status: String, Decodable {
        case un_known = "UN_KNOWN"
        case success = "SUCCESS"
        case failed = "FAILED"
        case pending = "PENDING"
        case running = "RUNNING"
        
        case history = "HISTORY"
    }
    
    
    var id: Int
    var links: [String]?
    var status: Status?
    var provider: String?
    var model: String?
    var agentType: AgentType?
    var statusDescribe: String?
    var createDate: String?
    var creationDay: String?
    var updateDate: String?
    
    // 使用 [String: AnyCodable] 来处理动态对象
    private let inputFormatCodable: [String: RobustAnyCodable]
    
    // 提供一个计算属性，方便地以 [String: Any] 的形式访问数据
    var inputFormat: [String: Any]? {
        return inputFormatCodable.mapValues { $0.value }
    }

    // 自定义 CodingKeys，将 JSON 中的 "inputFormat" 映射到我们的私有属性
    enum CodingKeys: String, CodingKey {
        case id, links, provider, model, agentType, createDate, creationDay, updateDate, status, statusDescribe
        case inputFormatCodable = "inputFormat"
    }
}

extension UserArtWorkRes {
    
    /// 一个便利初始化器，允许我们用常规类型创建实例，尤其方便测试和本地数据创建。
    init(
        id: Int,
        links: [String]? = nil,
        status: Status? = nil,
        provider: String? = nil,
        model: String? = nil,
        agentType: AgentType? = nil,
        statusDescribe: String? = nil,
        createDate: String? = nil,
        creationDay: String? = nil,
        updateDate: String? = nil,
        inputFormat: [String: Any]? = nil // 接收更友好的 [String: Any]?
    ) {
        self.id = id
        self.links = links
        self.status = status
        self.provider = provider
        self.model = model
        self.agentType = agentType
        self.statusDescribe = statusDescribe
        self.createDate = createDate
        self.creationDay = creationDay
        self.updateDate = updateDate
        
        // 关键步骤：将传入的 [String: Any]? 转换为内部存储的 [String: RobustAnyCodable]
        if let inputFormat = inputFormat  {
            // 使用 mapValues 将字典中的每个 value 包装成 RobustAnyCodable
            self.inputFormatCodable = inputFormat.mapValues { RobustAnyCodable($0) }
        } else {
            // 如果传入的是 nil，就初始化一个空字典
            self.inputFormatCodable = [:]
        }
    }
}
