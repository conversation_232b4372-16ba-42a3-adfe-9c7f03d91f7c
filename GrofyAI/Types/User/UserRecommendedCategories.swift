//
//  User.swift
//  GrofyAI
//
//  Created by kissy on 2025/7/14.
//

//MARK: - 请求参数
struct RecommendedCategoriesListReq : Encodable {
    let type: AgentType.RawValue
    let tenantId: Int
}

struct RecommendedCategoriesDetailReq: Encodable {
    let size: String
    let current: String
    let agentType: AgentType.RawValue
    let categoryId: String
}


//MARK: - 响应参数
struct RecommendedCategoriesListRes: Decodable,Identifiable, Hashable {
    let id: Int
    let title: String
    let type: String
    let description: String?
    let locale: String?
}

