import Foundation

// MARK: - 用户个人信息模型

struct UserProfile: Codable {
    let id: Int?
    let nickname: String?
    let avatar: String?
    let email: String?
    let phone: String?
    let memberLevel: String?
    let memberLevelName: String?
    let points: Int?
    let totalPoints: Int?
    let expireDate: Int?
    let createDate: String?
    let lastLoginDate: String?

    /// 昵称
    var safeNickname: String {
        return nickname ?? "用户"
    }

    /// 头像 url
    var safeAvatar: String {
        return avatar ?? ""
    }

    /// 会员等级名称
    var safeMemberLevelName: String {
        return memberLevelName ?? "普通用户"
    }

    /// 积分值
    var safePoints: Int {
        return points ?? 0
    }

    /// 总积分值
    var safeTotalPoints: Int {
        return totalPoints ?? 0
    }

    /// 是否为VIP或更高级别用户
    var isVIP: Bool {
        return MembershipLevel.isVIP(memberLevel)
    }

    /// 是否为付费会员
    var isPaidMember: Bool {
        return MembershipLevel.isPaidMember(memberLevel)
    }

    /// 获取会员等级枚举
    var membershipLevel: MembershipLevel {
        return MembershipLevel.from(memberLevel)
    }
}

// MARK: - 用户收藏模型

/// 用户收藏项目模型
struct UserFavorite: Codable, Identifiable {
    let id: Int
    let userId: Int?
    let itemId: Int
    let itemType: FavoriteType
    let title: String?
    let thumbnail: String?
    let createDate: String?

    /// 收藏类型枚举
    enum FavoriteType: String, Codable, CaseIterable {
        case artwork = "ARTWORK"
        case knowledge = "KNOWLEDGE"
        case chat = "CHAT"

        var displayName: String {
            switch self {
            case .artwork: return "作品"
            case .knowledge: return "知识库"
            case .chat: return "对话"
            }
        }

        var iconName: String {
            switch self {
            case .artwork: return "photo.on.rectangle.angled"
            case .knowledge: return "book"
            case .chat: return "message"
            }
        }
    }

    /// 标题
    var safeTitle: String {
        return title ?? "未命名"
    }

    /// 缩略图
    var safeThumbnail: String {
        return thumbnail ?? ""
    }

    /// 格式化的创建时间
    var formattedCreateDate: String {
        guard let createDate else { return "未知时间" }

        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss.SSSSSS"

        if let date = formatter.date(from: createDate) {
            let displayFormatter = DateFormatter()
            displayFormatter.dateFormat = "MM月dd日"
            return displayFormatter.string(from: date)
        }

        return createDate
    }
}

// MARK: - 用户消息模型

/// 用户消息模型
struct UserMessage: Codable, Identifiable, Hashable {
    let id: Int
    let userId: Int?
    let title: String
    let content: String?
    let type: MessageType
    let isRead: Bool
    let createDate: String?

    /// 消息类型枚举
    enum MessageType: String, Codable, CaseIterable {
        case system = "SYSTEM"
        case promotion = "PROMOTION"
        case update = "UPDATE"
        case notification = "NOTIFICATION"

        var displayName: String {
            switch self {
            case .system: return "系统消息"
            case .promotion: return "推广消息"
            case .update: return "更新通知"
            case .notification: return "通知消息"
            }
        }

        var iconName: String {
            switch self {
            case .system: return "gear"
            case .promotion: return "megaphone"
            case .update: return "arrow.up.circle"
            case .notification: return "bell"
            }
        }

        var iconColor: String {
            switch self {
            case .system: return "ColorBrandPrimary"
            case .promotion: return "ColorBrandGradient"
            case .update: return "ColorSuccess"
            case .notification: return "ColorWarning"
            }
        }
    }

    /// 内容
    var safeContent: String {
        return content ?? ""
    }

    /// 格式化的创建时间
    var formattedCreateDate: String {
        guard let createDate else { return "未知时间" }

        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss.SSSSSS"

        if let date = formatter.date(from: createDate) {
            let displayFormatter = DateFormatter()
            displayFormatter.dateFormat = "MM月dd日 HH:mm"
            return displayFormatter.string(from: date)
        }

        return createDate
    }
}

// MARK: - 用户统计信息模型

/// 用户统计信息模型
struct UserStats: Codable {
    let artworkCount: Int?
    let favoriteCount: Int?
    let chatCount: Int?
    let knowledgeCount: Int?
    let unreadMessageCount: Int?

    /// 作品数量
    var safeArtworkCount: Int {
        return artworkCount ?? 0
    }

    /// 收藏数量
    var safeFavoriteCount: Int {
        return favoriteCount ?? 0
    }

    /// 对话数量
    var safeChatCount: Int {
        return chatCount ?? 0
    }

    /// 知识库数量
    var safeKnowledgeCount: Int {
        return knowledgeCount ?? 0
    }

    /// 未读消息数量
    var safeUnreadMessageCount: Int {
        return unreadMessageCount ?? 0
    }

    /// 是否有未读消息
    var hasUnreadMessages: Bool {
        return safeUnreadMessageCount > 0
    }
}
