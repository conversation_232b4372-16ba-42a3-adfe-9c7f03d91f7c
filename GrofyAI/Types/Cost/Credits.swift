//
//  CreationCostItem.swift
//  GrofyAI
//
//  Created by kissy on 2025/7/2.
//
import Foundation
//MARK: - 请求参数类型
struct CreditsConsumeReq: Encodable {
    let size: String
    let current: String
}

//MARK: - 响应结果类型

// 对应 "data" 数组中的每一个对象
struct CreditsItem: Codable, Identifiable {
    let id: Int
    let modelId: String?
    let unionId: String
    let provider: String
    let name: String?
    let spec: String?
    let duration: Int?
    let aspectRatio: String?
    let quality: String?
    let type: String?
    let chargeType: String?
    let credit: Int?
}

//积分消耗返回类型
struct CreditsConsumeRes: Codable, Identifiable {
    let id: Int
    let creditCostsKey: String?
    let terminal: String?
    let provider: String?
    let name: String?
    let spec: String?
    let duration: Int?
    let aspectRatio: String?
    let quality: String?
    let type: String?
    let amount: Int?
    let costType: String?
    let credits: Int?
    let costCredits: Int?
    let costPremiumCredits: Int?
    let createDate: String?
}
