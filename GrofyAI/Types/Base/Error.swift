import Foundation

struct BusinessError: Error, LocalizedError {
    let code: Int
    let message: String

    init(code: Int, message: String) {
        self.code = code
        self.message = message
    }

    var errorDescription: String? {
        return message
    }

    var localizedDescription: String {
        return message
    }
}

enum NetworkError: Error {
    case noConnection
    case noData
    case timeout
    case authorizationError
    case decodingError(Error)
    case requestCancelled
    case unknown(Error)

    var localizedDescription: String {
        switch self {
        case .noConnection: return "网络连接不可用"
        case .noData: return "无可用内容"
        case .timeout: return "请求超时"
        case .authorizationError: return "授权失败"
        case .decodingError(let error): return "数据解析错误: \(error.localizedDescription)"
        case .requestCancelled: return "请求已取消"
        case .unknown(let error): return "未知错误: \(error.localizedDescription)"
        }
    }
}
