import Foundation

// MARK: - 基础响应类型

struct OnlyCodeRes: Codable, Hashable, Sendable {
    let code: Int
    let msg: String
}

// MARK: - 泛型响应类型

struct Res<T: Decodable>: Decodable {
    var code: Int
    var msg: String
    var data: T

    init(code: Int, msg: String, data: T) {
        self.code = code
        self.msg = msg
        self.data = data
    }

    private enum CodingKeys: String, CodingKey {
        case code
        case msg
        case data
    }

    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)

        code = try container.decode(Int.self, forKey: .code)
        msg = try container.decode(String.self, forKey: .msg)

        data = try container.decode(T.self, forKey: .data)
    }
}

// MARK: - 分页响应类型

struct PageRes<T: Decodable>: Decodable {
    var current: Int?
    var size: Int?
    var total: Int?
    var data: T

    var safeCurrent: Int {
        return current ?? 1
    }

    var safeSize: Int {
        return size ?? 10
    }

    var safeTotal: Int {
        return total ?? 0
    }
}

// MARK: - SSE响应类型

struct SSEResponse: Decodable {
    let code: Int
    let type: String
    let content: AnyCodable?
    let event: String?
    let node: String?

    private enum CodingKeys: String, CodingKey {
        case code
        case type
        case content
        case event
        case node
    }

    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        code = try container.decode(Int.self, forKey: .code)
        type = try container.decode(String.self, forKey: .type)
        content = try container.decodeIfPresent(AnyCodable.self, forKey: .content)
        event = try container.decodeIfPresent(String.self, forKey: .event)
        node = try container.decodeIfPresent(String.self, forKey: .node)
    }
}
