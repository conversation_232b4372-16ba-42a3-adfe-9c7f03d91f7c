import Alamofire

enum HttpMethodType: String, Codable {
    case get = "GET"
    case post = "POST"
    case put = "PUT"
    case patch = "PATCH"
    case delete = "DELETE"

    var af: HTTPMethod {
        switch self {
        case .get: return .get
        case .post: return .post
        case .put: return .put
        case .patch: return .patch
        case .delete: return .delete
        }
    }

    var encoding: ParameterEncoding {
        switch self {
        case .delete, .get: URLEncoding.queryString
        default: JSONEncoding.default
        }
    }
}
