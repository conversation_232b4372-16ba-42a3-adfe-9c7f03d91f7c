import Foundation

/**
 统一的聊天完成请求类型，支持三种不同的聊天模式：
 - chat: 智能体聊天对话
 - knowledge: 知识库对话
 - file: 文件对话

 解码策略：
 1. 优先检查显式的 `type` 字段
 2. 基于特征字段自动识别类型：
    - 存在 `categoryId` → 知识库对话
    - 存在 `knowledgeId` → 文件对话
    - 其他情况 → 智能体聊天

 使用示例：
 ```swift
 // 创建智能体聊天请求
 let chatReq = UnifiedChatCompletionReq.chat(ChatCompletionReq(...))

 // 从JSON解码
 let unified = try JSONDecoder().decode(UnifiedChatCompletionReq.self, from: jsonData)

 // 安全提取具体类型
 if let chatReq = unified.chatRequest {
     // 处理智能体聊天
 }
 ```
 */
enum UnifiedChatCompletionReq: Codable {
    case chat(ChatCompletionReq)
    case knowledge(KnowledgeChatCompletionReq)
    case file(FileChatCompletionReq)

    // MARK: - 解码错误类型
    enum DecodingError: Error, LocalizedError {
        case ambiguousType(String)
        case unsupportedType(String)
        case missingRequiredFields([String])
        case invalidData(String)

        var errorDescription: String? {
            switch self {
            case .ambiguousType(let details):
                return "无法确定请求类型: \(details)"
            case .unsupportedType(let type):
                return "不支持的请求类型: \(type)"
            case .missingRequiredFields(let fields):
                return "缺少必需字段: \(fields.joined(separator: ", "))"
            case .invalidData(let details):
                return "无效的数据格式: \(details)"
            }
        }
    }

    // MARK: - 编解码键
    private enum CodingKeys: String, CodingKey {
        case type
        case modelId = "model_id"
        case categoryId = "category_id"
        case knowledgeId = "knowledge_id"
        case messages
        case threadId = "thread_id"
        case thinking
        case networking
        case parentId = "parent_id"
        case isFirst = "is_first"
        case uploadedFiles = "uploaded_files"
        case uploadedImages = "uploaded_images"
    }

    private enum RequestTypeValue: String, Codable {
        case chat
        case knowledge
        case file
    }

    // MARK: - 解码实现
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)

        // 策略1: 检查显式的type字段
        if let typeString = try container.decodeIfPresent(String.self, forKey: .type),
           let requestType = RequestTypeValue(rawValue: typeString) {
            try self.init(fromExplicitType: requestType, decoder: decoder)
            return
        }

        // 策略2: 基于特征字段自动识别
        try self.init(fromFieldAnalysis: container, decoder: decoder)
    }

    /// 基于显式type字段解码
    private init(fromExplicitType type: RequestTypeValue, decoder: Decoder) throws {
        switch type {
        case .chat:
            self = .chat(try ChatCompletionReq(from: decoder))
        case .knowledge:
            self = .knowledge(try KnowledgeChatCompletionReq(from: decoder))
        case .file:
            self = .file(try FileChatCompletionReq(from: decoder))
        }
    }

    /// 基于字段分析自动识别类型
    private init(fromFieldAnalysis container: KeyedDecodingContainer<CodingKeys>, decoder: Decoder) throws {
        let hasCategoryId = container.contains(.categoryId)
        let hasKnowledgeId = container.contains(.knowledgeId)
        let hasThinking = container.contains(.thinking)
        let hasNetworking = container.contains(.networking)
        let hasUploadedFiles = container.contains(.uploadedFiles)
        let hasUploadedImages = container.contains(.uploadedImages)

        // 验证基础必需字段
        guard container.contains(.messages) else {
            throw DecodingError.missingRequiredFields(["messages"])
        }

        // 类型识别逻辑
        if hasKnowledgeId {
            // 文件对话：必须有knowledgeId，不能有categoryId
            if hasCategoryId {
                throw DecodingError.ambiguousType("同时存在knowledgeId和categoryId字段")
            }
            self = .file(try FileChatCompletionReq(from: decoder))

        } else if hasCategoryId {
            // 知识库对话：必须有categoryId，不能有智能体特有字段
            if hasThinking || hasNetworking || hasUploadedFiles || hasUploadedImages {
                throw DecodingError.ambiguousType("知识库对话不应包含thinking、networking或文件上传字段")
            }
            self = .knowledge(try KnowledgeChatCompletionReq(from: decoder))

        } else {
            // 智能体聊天：默认类型，可能包含thinking、networking等字段
            self = .chat(try ChatCompletionReq(from: decoder))
        }
    }

    // MARK: - 编码实现
    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)

        switch self {
        case .chat(let req):
            try container.encode("chat", forKey: .type)
            try req.encode(to: encoder)

        case .knowledge(let req):
            try container.encode("knowledge", forKey: .type)
            try req.encode(to: encoder)

        case .file(let req):
            try container.encode("file", forKey: .type)
            try req.encode(to: encoder)
        }
    }
}

// MARK: - 便利方法扩展
extension UnifiedChatCompletionReq {

    /// 安全提取智能体聊天请求
    var chatRequest: ChatCompletionReq? {
        if case .chat(let req) = self {
            return req
        }
        return nil
    }

    /// 安全提取知识库对话请求
    var knowledgeRequest: KnowledgeChatCompletionReq? {
        if case .knowledge(let req) = self {
            return req
        }
        return nil
    }

    /// 安全提取文件对话请求
    var fileRequest: FileChatCompletionReq? {
        if case .file(let req) = self {
            return req
        }
        return nil
    }

    /// 获取请求类型标识
    var requestType: String {
        switch self {
        case .chat:
            return "chat"
        case .knowledge:
            return "knowledge"
        case .file:
            return "file"
        }
    }

    /// 获取通用的messages字段
    var messages: String {
        switch self {
        case .chat(let req):
            return req.messages
        case .knowledge(let req):
            return req.messages
        case .file(let req):
            return req.messages
        }
    }

    /// 获取通用的modelId字段（文件对话没有此字段）
    var modelId: Int? {
        switch self {
        case .chat(let req):
            return req.modelId
        case .knowledge(let req):
            return req.modelId
        case .file:
            return nil
        }
    }

    /// 获取通用的threadId字段
    var threadId: String? {
        switch self {
        case .chat(let req):
            return req.threadId
        case .knowledge(let req):
            return req.threadId
        case .file(let req):
            return req.threadId
        }
    }

    /// 判断是否为首次对话
    var isFirst: Bool? {
        switch self {
        case .chat(let req):
            return req.isFirst
        case .knowledge(let req):
            return req.isFirst
        case .file(let req):
            return req.isFirst
        }
    }
}

// MARK: - API请求序列化
extension UnifiedChatCompletionReq {

    /// 转换为API请求参数字典（不包含type字段）
    /// - Returns: 用于发送给后端API的参数字典
    func toAPIParameters() -> [String: Any]? {
        switch self {
        case .chat(let req):
            return req.toDictionary()
        case .knowledge(let req):
            return req.toDictionary()
        case .file(let req):
            return req.toDictionary()
        }
    }
}

// MARK: - 创建便利方法
extension UnifiedChatCompletionReq {

    /// 创建智能体聊天请求
    static func createChatRequest(
        modelId: Int,
        messages: String,
        threadId: String,
        thinking: Bool? = nil,
        networking: Bool? = nil,
        parentId: String? = nil,
        isFirst: Bool,
        uploadedFiles: [UploadedFile]? = nil,
        uploadedImages: [UploadedImage]? = nil
    ) -> UnifiedChatCompletionReq {
        let req = ChatCompletionReq(
            modelId: modelId,
            messages: messages,
            threadId: threadId,
            thinking: thinking,
            networking: networking,
            parentId: parentId,
            isFirst: isFirst,
            uploadedFiles: uploadedFiles,
            uploadedImages: uploadedImages
        )
        return .chat(req)
    }

    /// 创建知识库对话请求
    static func createKnowledgeRequest(
        modelId: Int,
        categoryId: Int,
        messages: String,
        threadId: String,
        parentId: String? = nil,
        isFirst: Bool
    ) -> UnifiedChatCompletionReq {
        let req = KnowledgeChatCompletionReq(
            modelId: modelId,
            categoryId: categoryId,
            messages: messages,
            threadId: threadId,
            parentId: parentId,
            isFirst: isFirst
        )
        return .knowledge(req)
    }

    /// 创建文件对话请求
    static func createFileRequest(
        knowledgeId: Int,
        messages: String,
        threadId: String,
        parentId: String? = nil,
        isFirst: Bool
    ) -> UnifiedChatCompletionReq {
        let req = FileChatCompletionReq(
            knowledgeId: knowledgeId,
            messages: messages,
            threadId: threadId,
            parentId: parentId,
            isFirst: isFirst
        )
        return .file(req)
    }
}
