import Foundation

// MARK: - 文件上传类型

struct UploadedFile: Codable {
    let fileId: String
    let fileName: String
    let fileUrl: String

    private enum CodingKeys: String, CodingKey {
        case fileId
        case fileName
        case fileUrl
    }
}

struct UploadedImage: Codable {
    let fileId: String
    let fileName: String
    let fileUrl: String

    private enum CodingKeys: String, CodingKey {
        case fileId
        case fileName
        case fileUrl
    }
}
