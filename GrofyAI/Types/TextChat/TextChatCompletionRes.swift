
import Foundation

// MARK: - 基础响应协议

protocol BaseSSEResponse: Decodable {
    var code: Int { get }
}

// MARK: - 文本对话

struct TextResponse: BaseSSEResponse {
    let code: Int
    let content: String
    var type: String { "text" }
}

// MARK: - 思考内容

struct ReasoningTextResponse: BaseSSEResponse {
    let code: Int
    let content: String
    var type: String { "reasoning_text" }
}

// MARK: - 深度搜索

struct ToolDeepSearchResponse: BaseSSEResponse {
    let code: Int
    let content: [SearchResult]
    var type: String { "tool_deep_search" }
}

// MARK: - 联网搜索

struct EventResponse: BaseSSEResponse {
    let code: Int
    let type: EventType
    let event: String?
    let node: String?
    let content: AnyCodable?

    enum EventType: String, Decodable {
        case status
        case ragIndex = "rag_index"
    }
}

// MARK: - 统一响应枚举

enum ChatCompletionPes: Decodable {
    case text(code: Int, content: String)
    case reasoningText(code: Int, content: String)
    case toolDeepSearch(code: Int, content: [SearchResult])
    case event(code: Int, type: EventResponse.EventType, event: String?, node: String?, content: AnyCodable?)

    private enum CodingKeys: String, CodingKey {
        case code
        case type
        case content
        case event
        case node
    }

    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        let code = try container.decode(Int.self, forKey: .code)
        let type = try container.decode(String.self, forKey: .type)

        switch type {
        case "text":
            let content = try container.decode(String.self, forKey: .content)
            self = .text(code: code, content: content)
        case "reasoning_text":
            let content = try container.decode(String.self, forKey: .content)
            self = .reasoningText(code: code, content: content)
        case "tool_deep_search":
            let content = try container.decode([SearchResult].self, forKey: .content)
            self = .toolDeepSearch(code: code, content: content)
        case "rag_index", "status":
            let eventType = EventResponse.EventType(rawValue: type)!
            let event = try container.decodeIfPresent(String.self, forKey: .event)
            let node = try container.decodeIfPresent(String.self, forKey: .node)
            let content = try container.decodeIfPresent(AnyCodable.self, forKey: .content)
            self = .event(code: code, type: eventType, event: event, node: node, content: content)
        default:
            throw DecodingError.dataCorrupted(
                DecodingError.Context(codingPath: decoder.codingPath, debugDescription: "Unknown type: \(type)")
            )
        }
    }
}

// MARK: - SSE响应转换函数

func convertSSEResponseToChatCompletion(_ sseResponse: SSEResponse) -> ChatCompletionPes? {
    let code = sseResponse.code

    switch sseResponse.type {
    case "text":
        guard let content = sseResponse.content?.value as? String else { return nil }
        return .text(code: code, content: content)

    case "reasoning_text":
        guard let content = sseResponse.content?.value as? String else { return nil }
        return .reasoningText(code: code, content: content)

    case "tool_deep_search":
        guard let contentArray = sseResponse.content?.value as? [[String: Any]] else { return nil }
        do {
            let jsonData = try JSONSerialization.data(withJSONObject: contentArray)
            let searchResults = try JSONDecoder().decode([SearchResult].self, from: jsonData)
            return .toolDeepSearch(code: code, content: searchResults)
        } catch {
            print("转换tool_deep_search失败: \(error)")
            return nil
        }

    case "status":
        let eventType: EventResponse.EventType = .status
        return .event(
            code: code,
            type: eventType,
            event: sseResponse.event,
            node: sseResponse.node,
            content: sseResponse.content
        )

    case "rag_index":
        let eventType: EventResponse.EventType = .ragIndex
        return .event(
            code: code,
            type: eventType,
            event: sseResponse.event,
            node: sseResponse.node,
            content: sseResponse.content
        )

    case "tool_image_generation":
        return nil

    default:
        print("未知的SSE响应类型: \(sseResponse.type)")
        return nil
    }
}

// MARK: - 辅助类型

struct AnyCodable: Codable {
    let value: Any

    init(_ value: (some Any)?) {
        self.value = value ?? ()
    }

    init(from decoder: Decoder) throws {
        let container = try decoder.singleValueContainer()

        if let intValue = try? container.decode(Int.self) {
            value = intValue
        } else if let stringValue = try? container.decode(String.self) {
            value = stringValue
        } else if let boolValue = try? container.decode(Bool.self) {
            value = boolValue
        } else if let arrayValue = try? container.decode([AnyCodable].self) {
            value = arrayValue.map(\.value)
        } else if let dictValue = try? container.decode([String: AnyCodable].self) {
            value = dictValue.mapValues { $0.value }
        } else {
            value = ()
        }
    }

    func encode(to encoder: Encoder) throws {
        var container = encoder.singleValueContainer()

        switch value {
        case let intValue as Int:
            try container.encode(intValue)
        case let stringValue as String:
            try container.encode(stringValue)
        case let boolValue as Bool:
            try container.encode(boolValue)
        default:
            try container.encodeNil()
        }
    }
}
