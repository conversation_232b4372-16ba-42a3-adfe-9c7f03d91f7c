import Foundation

// MARK: - 智能体聊天对话参数类型

struct ChatCompletionReq: Codable {
    let modelId: Int
    let messages: String
    let threadId: String
    let thinking: Bool?
    let networking: Bool?
    let parentId: String?
    let isFirst: Bool
    let uploadedFiles: [UploadedFile]?
    let uploadedImages: [UploadedImage]?

    private enum CodingKeys: String, CodingKey {
        case modelId = "model_id"
        case messages
        case threadId = "thread_id"
        case thinking
        case networking
        case parentId = "parent_id"
        case isFirst = "is_first"
        case uploadedFiles = "uploaded_files"
        case uploadedImages = "uploaded_images"
    }
}
