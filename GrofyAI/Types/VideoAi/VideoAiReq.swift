//
//  VideoAiReq.swift
//  GrofyAI
//
//  Created by kissy on 2025/4/29.
//


//MARK: Wanx_t2v_VideoReq
struct Wanx_t2v_VideoReq : Encodable{
    let threadId: String
    let model: WanxVideoAi.T2V.Model.RawValue
    let prompt: String
    let size: WanxVideoAi.T2V.Size.RawValue
    let resolution: WanxVideoAi.T2V.Resolution.RawValue?
    let prompt_extend: Bool?
    let duration: WanxVideoAi.T2V.Duration.RawValue?
    let seed: Int?
}

//MARK: Wanx_i2v_VideoReq
struct Wanx_i2v_VideoReq: Encodable {
    let threadId: String
    let model: WanxVideoAi.I2V.Model.RawValue
    let prompt: String
    let img_url: String
    let resolution: WanxVideoAi.I2V.Resolution.RawValue?
    let prompt_extend: Bool?
    let duration: WanxVideoAi.I2V.Duration.RawValue?
    let seed: Int?
}

//MARK: Kling_t2v_VideoReq
struct Kling_t2v_VideoReq: Encodable {
    let threadId: String
    let model_name: KlingVideoAi.T2V.Model.RawValue
    let prompt: String
    let negative_prompt: String
    let cfg_scale: Double?
    let mode: KlingVideoAi.T2V.Mode.RawValue?
    let type: KlingVideoAi.T2V.CameraMovementType.RawValue?
    let config: KlingConfig?
    let aspect_ratio: KlingVideoAi.T2V.AspectRatio.RawValue?
    let duration: Int?
    let callback_url: String?
}

struct KlingConfig: Encodable {
    var horizontal: Int?
    var vertical: Int?
    var pan: Int?
    var tilt: Int?
    var roll: Int?
    var zoom: Int?
}

//MARK: Kling_i2v_VideoReq
struct Kling_i2v_VideoReq: Encodable {
    let threadId: String
    let model_name: KlingVideoAi.I2V.Model.RawValue
    let prompt: String
    let negative_prompt: String
    let cfg_scale: Double?
    let mode: KlingVideoAi.I2V.Mode.RawValue?
    let type: KlingVideoAi.I2V.CameraMovementType.RawValue?
    let config: KlingConfig?
    let aspect_ratio: KlingVideoAi.I2V.AspectRatio.RawValue?
    let duration: Int?
    let callback_url: String?
    let image: String
    let image_tail: String
}

//MARK: Minimax_t2v_VideoReq
struct Minimax_t2v_VideoReq: Encodable {
    let threadId: String
    let model: MinimaxVideoAi.T2V.Model.RawValue
    let prompt: String
    let prompt_optimizer: Bool?
}

//MARK: Minimax_i2v_VideoReq
struct Minimax_i2v_VideoReq: Encodable {
    let threadId: String
    let first_frame_imagestring: String
    let model: MinimaxVideoAi.I2V.Model.RawValue
    let prompt: String
    let prompt_optimizer: Bool?
}

//MARK: Pixverse_t2v_VideoReq
struct Pixverse_t2v_VideoReq: Encodable {
    let threadId: String
    let prompt: String
    let negative_prompt: String?
    let aspect_ratio: PixverseVideoAi.T2V.AspectRatio.RawValue
    //视频时长:生成视频时长，单位：秒 (5, 8) 秒,  当model=v3.5 只支持 5,8;  当quality=1080p 不支持 8秒),示例值(5)
    let duration: PixverseVideoAi.T2V.Duration.RawValue
    let model: PixverseVideoAi.T2V.Model.RawValue
    //运动模式:运动模式 (normal, fast）, fast 仅在 duration=5 时可用; quality=1080p 不支持 fast)
    let motion_mode: PixverseVideoAi.T2V.MotionMode.RawValue?
    // 视频质量:视频质量:540p,720p,1080p
    let quality: PixverseVideoAi.T2V.Quality.RawValue
    let seed: Int?
    //风格:风格 (当 model=v3.5 时有效, "anime", "3d_animation", "clay", "comic", "cyberpunk") 除非需要，否则不要包含风格参数
    let style: PixverseVideoAi.T2V.Style.RawValue?
}

//MARK: Pixverse_i2v_VideoReq
struct Pixverse_i2v_VideoReq: Encodable {
    let threadId: String
    let img_id: Int
    let prompt: String
    let negative_prompt: String?
    //视频时长:生成视频时长，单位：秒 (5, 8) 秒,  当model=v3.5 只支持 5,8;  当quality=1080p 不支持 8秒),示例值(5)
    let duration: PixverseVideoAi.I2V.Duration.RawValue
    let model: PixverseVideoAi.I2V.Model.RawValue
    //运动模式:运动模式 (normal, fast）, fast 仅在 duration=5 时可用; quality=1080p 不支持 fast)
    let motion_mode: PixverseVideoAi.I2V.MotionMode.RawValue?
    // 视频质量:视频质量:540p,720p,1080p
    let quality: PixverseVideoAi.I2V.Quality.RawValue
    let seed: Int?
    //风格:风格 (当 model=v3.5 时有效, "anime", "3d_animation", "clay", "comic", "cyberpunk") 除非需要，否则不要包含风格参数
    let style: PixverseVideoAi.I2V.Style.RawValue?
}

//MARK: Runway_i2v_VideoReq
struct Runway_i2v_VideoReq: Encodable {
    let threadId: String
    let firstImage: String
    let lastImage: String
    let model: RunwayVideoAi.Model.RawValue
    let ratio: RunwayVideoAi.Ratio.RawValue?
    let promptText: String?
    let seed: Int?
    let duration: RunwayVideoAi.Duration.RawValue?
}

//MARK: Effect_video_VideoReq
struct Effect_video_VideoReq: Encodable {
    let threadId: String
    let type: EffectVideoAi.EffectType.RawValue
    let image: String?
    let images: [String]?
}
