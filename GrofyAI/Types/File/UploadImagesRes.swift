//
//  UploadImagesRes.swift
//  GrofyAI
//
//  Created by kissy on 2025/6/19.
//
struct UploadImagesRes : Decodable{
    // 附件 id
    let id: Int
    // 文件ID
    let fileId: String?
    // 附件名称
    let name: String
    // 附件原始名
    let originalName: String
    // 图片在线地址
    let url: String
    
    var effectiveFileId: String {
        return fileId ?? String(id)
    }
}

struct UploadImageToPixverseRes : Decodable {
    let img_id: Int
    let img_url: String
}
