import Foundation

// MARK: - 知识库文件上传响应

/// 知识库文件上传响应项
struct KnowledgeFileUploadResult: Codable {
    let status: String
    let fileId: String?
    let fileName: String
    let fileUrl: String?
    let message: String?

    private enum CodingKeys: String, CodingKey {
        case status
        case fileId = "file_id"
        case fileName = "file_name"
        case fileUrl = "file_url"
        case message
    }

    var isSuccess: Bool {
        return status == "success"
    }

    var isFailure: Bool {
        return status == "error"
    }
}

/// 知识库文件上传响应类型别名
typealias KnowledgeFileUploadResponse = [KnowledgeFileUploadResult]
