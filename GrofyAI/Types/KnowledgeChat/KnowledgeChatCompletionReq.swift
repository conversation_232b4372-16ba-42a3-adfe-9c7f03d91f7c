import Foundation

// MARK: - 知识库对话参数类型

struct KnowledgeChatCompletionReq: Codable {
    let modelId: Int
    let categoryId: Int
    let messages: String
    let threadId: String
    let parentId: String?
    let isFirst: Bool

    private enum CodingKeys: String, CodingKey {
        case modelId = "model_id"
        case categoryId = "category_id"
        case messages
        case threadId = "thread_id"
        case parentId = "parent_id"
        case isFirst = "is_first"
    }
}
