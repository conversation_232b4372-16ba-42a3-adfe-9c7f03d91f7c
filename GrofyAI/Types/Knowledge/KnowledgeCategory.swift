import SwiftUI

// MARK: - 知识库分类模型

/// 知识库分类模型
struct KnowledgeCategory: Codable, Identifiable {
    let id: Int
    let title: String
    let favicon: String?
    let content: String?
    let createDate: String?

    /// 格式化的创建时间
    var formattedCreateDate: String {
        guard let createDate else {
            return "未知时间"
        }

        if let date = DateUtils.parseAPIDate(createDate) {
            return DateUtils.formatFriendlyDate(date)
        }

        // 如果解析失败，返回原始字符串的简化版本
        if createDate.contains("T") {
            let components = createDate.components(separatedBy: "T")
            if let dateComponent = components.first {
                return dateComponent
            }
        }

        return createDate
    }

    /// 内容摘要（限制长度）
    var contentSummary: String {
        guard let content, !content.isEmpty else {
            return "暂无描述"
        }

        let maxLength = 50
        if content.count > maxLength {
            let index = content.index(content.startIndex, offsetBy: maxLength)
            return String(content[..<index]) + "..."
        }

        return content
    }

    /// 是否有图标
    var hasIcon: Bool {
        guard let favicon else { return false }
        return !favicon.isEmpty
    }
}

// MARK: - 扩展方法

// MARK: - 预定义颜色常量
private enum KnowledgeCategoryConstants {
    static let categoryColors: [Color] = [
        DesignSystem.Colors.primary,
        .blue,
        .green,
        .orange,
        .purple,
        .pink,
    ]
}

extension KnowledgeCategory {
    var displayIconName: String {
        if id == 0 {
            return "person.crop.circle.fill"
        }
        return "folder.fill"
    }

    /// 获取显示用的图标颜色
    var displayIconColor: Color {
        if id == 0 {
            return DesignSystem.Colors.primary
        }
        let colorIndex = abs(id) % KnowledgeCategoryConstants.categoryColors.count
        return KnowledgeCategoryConstants.categoryColors[colorIndex]
    }

    /// 个人知识库的静态表示
    static let personalKnowledgeBase = KnowledgeCategory(
        id: 0,
        title: "个人知识库",
        favicon: nil,
        content: "管理您的个人文件和文档",
        createDate: nil
    )
}
