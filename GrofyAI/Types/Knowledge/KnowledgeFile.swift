
import Foundation
import SwiftUI

// MARK: - 知识库排序类型枚举

/// 知识库排序类型
enum KnowledgeOrderBy: String, CaseIterable {
    case createDate = "CREATE_DATE"
    case title = "TITLE"
    case size = "SIZE"

    var displayName: String {
        switch self {
        case .createDate: return "创建时间"
        case .title: return "标题"
        case .size: return "文件大小"
        }
    }
}

// MARK: - 知识库文件分页响应模型

/// 知识库文件分页数据
struct KnowledgeFilePageData: Codable {
    let files: [KnowledgeFile]

    init(from decoder: Decoder) throws {
        let container = try decoder.singleValueContainer()

        do {
            files = try container.decode([KnowledgeFile].self)
        } catch {
            files = []
        }
    }

    func encode(to encoder: Encoder) throws {
        var container = encoder.singleValueContainer()
        try container.encode(files)
    }
}

typealias KnowledgeFilePageResponse = PageRes<KnowledgeFilePageData>

// MARK: - 知识库文件模型

/// 知识库文件模型
struct KnowledgeFile: Codable, Identifiable, Equatable {
    let id: Int
    let categoryId: Int?
    let fileId: String?
    let title: String?
    let type: String?
    let link: String?
    let size: Int?
    let tags: [String]?
    let fileStatus: String?
    let vectorStatus: String?
    let createDate: String?
    let threadId: String?
}

// MARK: - 扩展方法

extension KnowledgeFile {
    /// 格式化的文件大小
    var formattedSize: String {
        return size.formattedFileSize(fallback: "未知大小")
    }

    /// 格式化的创建日期
    var formattedCreateDate: String {
        guard let createDate else {
            return "未知时间"
        }

        if let date = DateUtils.parseAPIDate(createDate) {
            return DateUtils.formatShortDateTime(date)
        }

        if createDate.contains("T") {
            let components = createDate.components(separatedBy: "T")
            if components.count >= 2 {
                let dateComponent = components[0]
                let timeComponent = components[1].components(separatedBy: ".")[0] // 移除毫秒部分
                return "\(dateComponent) \(timeComponent)"
            }
        }

        return createDate
    }

    /// 获取文件类别
    var fileCategory: MessageFile.FileCategory {
        guard let type else {
            return .unknown
        }
        return MessageFile.FileCategory(from: type)
    }

    /// 根据文件类型返回图标和颜色
    var typeIcon: (iconName: String, color: Color) {
        let tempMessageFile = MessageFile(
            id: fileId ?? "",
            name: title ?? "",
            type: type ?? "",
            url: link ?? ""
        )
        let iconName = tempMessageFile.iconName
        let colorName = tempMessageFile.typeColor

        let color: Color = {
            switch colorName {
            case "blue": return .blue
            case "red": return .red
            case "green": return .green
            case "orange": return .orange
            case "purple": return .purple
            case "pink": return .pink
            case "gray": return .gray
            case "cyan": return .cyan
            case "indigo": return .indigo
            case "brown": return .brown
            default: return .gray
            }
        }()

        return (iconName, color)
    }

    /// 文件类型显示名称
    var typeDisplayName: String {
        let tempMessageFile = MessageFile(
            id: fileId ?? "",
            name: title ?? "",
            type: type ?? "",
            url: link ?? ""
        )
        return tempMessageFile.displayName
    }

    /// 文件状态是否就绪
    var isReady: Bool {
        guard let fileStatus else { return false }
        return fileStatus.uppercased() == "READY"
    }

    /// 向量化状态是否就绪
    var isVectorReady: Bool {
        guard let vectorStatus else { return false }
        return vectorStatus.uppercased() == "READY"
    }

    /// 是否有关联的对话线程
    var hasThread: Bool {
        guard let threadId else { return false }
        return !threadId.isEmpty
    }

    /// 状态显示文本
    var statusText: String {
        if !isReady {
            return "处理中..."
        } else if !isVectorReady {
            return "索引中..."
        } else {
            return "可对话"
        }
    }

    /// 状态图标名称
    var statusIcon: String {
        if !isReady {
            return "clock.fill"
        } else if !isVectorReady {
            return "magnifyingglass.circle.fill"
        } else {
            return "checkmark.circle.fill"
        }
    }

    /// 状态显示颜色
    var statusColor: Color {
        if !isReady {
            return .orange
        } else if !isVectorReady {
            return .blue
        } else {
            return .green
        }
    }
}
