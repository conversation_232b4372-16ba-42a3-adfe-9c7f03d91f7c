import Foundation

enum FilesApi {
    case uploadImages(threadId: String)
    case uploadImageToPixverse
    case uploadKnowledgeFiles(categoryId: Int)
    case getKnowledgeFiles(categoryId: Int?, page: Int?, size: Int?, type: String?, orderBy: String?, title: String?)
    case getKnowledgeCategories
    case deleteKnowledgeFiles
    case deleteKnowledgeCategories
    case updateKnowledgeCategory
    case createKnowledgeCategory
    case updateKnowledgeFileTags
    case moveKnowledgeFile
    case download
    
    var path: String {
        switch self {
        case .uploadImages(let threadId):
            return "api/know/attachment/v1/image/\(threadId)"
        case .download:
            return "api/system/v1/download"
        case .uploadImageToPixverse:
            return "api/know/attachment/v1/file/pixverse"
        case .uploadKnowledgeFiles:
            return "kapi/files/docs"
        case .getKnowledgeFiles(let categoryId, let page, let size, let type, let orderBy, let title):
            var path = "api/know/v1/page"

            let queryParams = [
                categoryId.map { "categoryId=\($0)" },
                page.map { "current=\($0)" },
                size.map { "size=\($0)" },
                type?.isEmpty == false ? "type=\(type!)" : nil,
                orderBy?.isEmpty == false ? "orderBy=\(orderBy!)" : nil,
                title?
                    .isEmpty == false ?
                    "title=\(title!.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) ?? title!)" : nil,
            ].compactMap(\.self)

            if !queryParams.isEmpty {
                path += "?" + queryParams.joined(separator: "&")
            }

            return path
        case .getKnowledgeCategories:
            return "api/know/category/v1/list"
        case .deleteKnowledgeFiles:
            return "api/know/v1/delete"
        case .deleteKnowledgeCategories:
            return "api/know/category/v1/delete"
        case .updateKnowledgeCategory:
            return "api/know/category/v1/update"
        case .createKnowledgeCategory:
            return "api/know/category/v1/create"
        case .updateKnowledgeFileTags:
            return "api/know/v1/updateTags"
        case .moveKnowledgeFile:
            return "api/know/v1/detail"
        }
    }
}
