//
//  ImageAiApi.swift
//  GrofyAI
//
//  Created by kissy on 2025/4/27.
//

enum ImageAiApi {
    case openAi, flux, ideogram, midjourney, stable_diffusion_3_5

    var api_path: String {
        switch self {
        case .openAi:
            return "api/image/generate/v1/openai"
        case .flux:
            return "api/image/generate/v1/flux"
        case .ideogram:
            return "api/image/generate/v1/ideogram"
        case .midjourney:
            return "api/image/generate/v1/midjourney"
        case .stable_diffusion_3_5:
            return "api/image/generate/v1/sd35"
        }
    }
}
