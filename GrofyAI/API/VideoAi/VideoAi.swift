//
//  VideoAi.swift
//  GrofyAI
//
//  Created by kissy on 2025/4/28.
//

enum VideoAiApi {
    //万象
    case wanx_t2v, wanx_i2v
    //可灵
    case kling_t2v, kling_i2v
    //海螺
    case minimax_t2v, minimax_i2v
    //pixverse
    case pixverse_t2v, pixverse_i2v
    
    case runway_i2v, effect_video
    
    var api_path: String {
        switch self {
            //万象
        case .wanx_t2v:
            return "api/video/generate/v1/wanx_t2v"
        case .wanx_i2v:
            return "api/video/generate/v1/wanx_i2v"
            //可灵
        case .kling_t2v:
            return "api/video/generate/v1/kling_t2v"
        case .kling_i2v:
            return "api/video/generate/v1/kling_i2v"
            //海螺
        case .minimax_t2v:
            return "api/video/generate/v1/minimax_t2v"
        case .minimax_i2v:
            return "api/video/generate/v1/minimax_i2v"
            //pixverse
        case .pixverse_t2v:
            return "api/video/generate/v2/pixverse_t2v"
        case .pixverse_i2v:
            return "api/video/generate/v2/pixverse_i2v"
            //runway
        case .runway_i2v:
            return "api/video/generate/v1/runway_i2v"
            //特效
        case .effect_video:
            return "api/video/generate/v1/effect_video"
        }
    }
}
