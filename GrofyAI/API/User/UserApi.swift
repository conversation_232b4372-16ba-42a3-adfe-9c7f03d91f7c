enum UserApi {
    case artWork
    case artWorksPage
    case deleteArtWorks
    case profile
    case favorites
    case messages
    case stats
    case updateProfile
    case recommendedCategoriesList
    case recommendedCategoriesDetail
    


    var path: String {
        switch self {
        case .artWork:
            return "api/artworks/v1/detail"
        case .artWorksPage:
            return "api/artworks/v1/page"
        case .deleteArtWorks:
            return "api/artworks/v1/deleteArtworks"
        case .profile:
            return "api/member/v1/profile"
        case .favorites:
            return "api/member/v1/favorites"
        case .messages:
            return "api/member/v1/messages"
        case .stats:
            return "api/member/v1/stats"
        case .updateProfile:
            return "api/member/v1/profile/update"
        case .recommendedCategoriesList:
            return "api/artworks/v1/catList"
        case .recommendedCategoriesDetail:
            return "api/artworks/v1/systemPage"
        }
    }
}
