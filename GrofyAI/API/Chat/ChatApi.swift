enum ChatApi {
    case completions
    case ragCompletions
    case imageCompletions
    case historyPage
    case deleteHistory
    case historyDetail
    case imageRecognition

    var path: String {
        switch self {
        case .completions:
            return "kapi/ai/chat/v1/completions"
        case .ragCompletions:
            return "kapi/ai/rag/v1/completions"
        case .imageCompletions:
            return "kapi/ai/chatimg/v1/completions"
        case .historyPage:
            return "api/member/v1/historyPage"
        case .deleteHistory:
            return "api/member/v1/historyDelete"
        case .historyDetail:
            return "kapi/agents/history/detail"
        case .imageRecognition:
            return "kapi/ai/vision/v1/completions"
        }
    }
}
