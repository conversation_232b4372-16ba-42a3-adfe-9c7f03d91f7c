enum AuthApi {
    case login
    case register
    case checkUserExists
    case sendVerificationCode
    case deleteAccount
    case refreshToken

    var path: String {
        switch self {
        case .login:
            return "api/iam/grofy/v1/login"
        case .register:
            return "api/iam/grofy/v1/register"
        case .checkUserExists:
            return "api/iam/grofy/v1/exists"
        case .sendVerificationCode:
            return "api/iam/grofy/v1/sendEmail"
        case .deleteAccount:
            return "api/member/v1/delete"
        case .refreshToken:
            return "api/iam/grofy/v1/refreshToken"
        }
    }
}
