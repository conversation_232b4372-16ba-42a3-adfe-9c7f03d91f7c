import Alamofire
import Foundation

// MARK: - 请求拦截器

final class AuthInterceptor: RequestInterceptor {
    func adapt(
        _ urlRequest: URLRequest,
        for session: Session,
        completion: @escaping (Result<URLRequest, Error>) -> Void
    ) {
        var req = urlRequest
        if let token = AuthStore.shared.getAccessToken() {
            req.setValue("Bearer \(token)", forHTTPHeaderField: "Authorization")
        }
        req.setValue(Language.deviceLanguage, forHTTPHeaderField: "locale")
        req.setValue(DateUtils.formatDateWithWeekday(), forHTTPHeaderField: "localtime")

        let method = req.httpMethod ?? "UNKNOWN"
        let url = req.url?.absoluteString ?? "unknown URL"

        var logMessage = "\n\(DateUtils.formatTimeOnly()) ==> \(method) \(url)\nRequest Headers: \(req.headers)"

        if let body = req.httpBody, !body.isEmpty {
            if let bodyString = String(data: body, encoding: .utf8) {
                logMessage += "\nRequest Body: \(bodyString)"
            } else {
                logMessage += "\nRequest Body: [binary data \(body.count) bytes]"
            }
        }

        print(logMessage)

        completion(.success(req))
    }
}

// MARK: - 核心网络层

final class HttpCore {
    static let shared = HttpCore()

    private let networkMonitor = NetworkMonitor.shared
    private var requests = [String: Request]()
    private let session: Session

    init() {
        // 配置 Session
        let configuration = URLSessionConfiguration.af.default
        configuration.timeoutIntervalForRequest = AppConfig.API.timeoutInterval

        session = Session(configuration: configuration, interceptor: AuthInterceptor())
    }

    @discardableResult
    func request<T: Decodable>(
        _ endpoint: String,
        method: HttpMethodType,
        parameters: [String: Any]? = nil,
        headers: [String: String]? = nil
    ) async throws -> Res<T> {
        guard networkMonitor.isReachable else {
            print("\(DateUtils.formatTimeOnly()) ❌ 网络不可用")
            throw NetworkError.noConnection
        }

        let url = "\(AppConfig.API.baseURL)/\(endpoint)"

        var customHeaders: HTTPHeaders?
        if let headers, !headers.isEmpty {
            customHeaders = HTTPHeaders(headers)
        }

        var dataResponse: DataResponse<Data, AFError>?

        do {
            let request = session.request(
                url,
                method: method.af,
                parameters: parameters,
                encoding: method.encoding,
                headers: customHeaders,
                requestModifier: { $0.timeoutInterval = AppConfig.API.timeoutInterval }
            )

            if let customHeaders {
                print("\(DateUtils.formatTimeOnly()) Custom Headers: \(customHeaders)")
            }

            let dataTask = request.validate(statusCode: 200..<300).serializingData()
            dataResponse = await dataTask.response

            do {
                let data = try await dataTask.value
                let result = try JSONDecoder().decode(Res<T>.self, from: data)

                if let httpResponse = dataResponse?.response {
                    let urlPath = httpResponse.url?.path ?? "unknown"
                    print(
                        "\(DateUtils.formatTimeOnly()) <== \(httpResponse.statusCode) \(method.af.rawValue.uppercased()) \(urlPath)"
                    )
                }

                return result

            } catch let error as AFError {
                if let responseCode = error.responseCode {
                    print(
                        "\(DateUtils.formatTimeOnly()) ❌ \(method.af.rawValue.uppercased()) Failed: \(responseCode) - \(error.localizedDescription)"
                    )

                    // 尝试解析服务器错误响应
                    if let data = dataResponse?.data,
                       let errorResponse = try? JSONDecoder().decode(OnlyCodeRes.self, from: data)
                    {
                        print(
                            "\(DateUtils.formatTimeOnly()) Server Error: \(errorResponse.msg) (Code: \(errorResponse.code))"
                        )
                        throw BusinessError(code: errorResponse.code, message: errorResponse.msg)
                    }
                } else {
                    print(
                        "\(DateUtils.formatTimeOnly()) ❌ \(method.af.rawValue.uppercased()) Failed: \(error.localizedDescription)"
                    )
                }

                throw NetworkError.unknown(error)
            }
        } catch {
            print(
                "\(DateUtils.formatTimeOnly()) ❌ \(method.af.rawValue.uppercased()) Error: \(error.localizedDescription)"
            )

            // 在通用错误处理中也尝试解析服务器错误响应
            if let data = dataResponse?.data,
               let errorResponse = try? JSONDecoder().decode(OnlyCodeRes.self, from: data)
            {
                print(
                    "\(DateUtils.formatTimeOnly()) Server Error (Generic): \(errorResponse.msg) (Code: \(errorResponse.code))"
                )
                throw BusinessError(code: errorResponse.code, message: errorResponse.msg)
            }

            throw NetworkError.unknown(error)
        }
    }

    @discardableResult
    func upload<T: Decodable>(
        _ endpoint: String,
        files: [UploadData],
        parameters: [String: String]? = nil,
        headers: [String: String]? = nil
    ) async throws -> Res<T> {
        guard networkMonitor.isReachable else {
            print("\(DateUtils.formatTimeOnly()) ❌ 网络不可用")
            throw NetworkError.noConnection
        }

        let url = "\(AppConfig.API.baseURL)/\(endpoint)"

        var customHeaders: HTTPHeaders?
        if let headers, !headers.isEmpty {
            customHeaders = HTTPHeaders(headers)
        }

        var dataResponse: DataResponse<Data, AFError>?

        do {
            // 使用 session.upload 来处理 multipart/form-data
            let request = session.upload(
                multipartFormData: { multipartFormData in
                    // 附加文件数据
                    for file in files {
                        // withName: 后端接收文件的字段名，通常是 "file" 或 "files"
                        multipartFormData.append(
                            file.data,
                            withName: file.fieldName,
                            fileName: file.fileName,
                            mimeType: file.mimeType
                        )
                    }

                    // 附加其他文本参数
                    if let parameters {
                        for (key, value) in parameters {
                            if let data = value.data(using: .utf8) {
                                multipartFormData.append(data, withName: key)
                            }
                        }
                    }
                },
                to: url,
                method: .post,
                headers: customHeaders
            )

            print("\(DateUtils.formatTimeOnly()) Upload Files: \(files.count)")
            if let customHeaders {
                print("\(DateUtils.formatTimeOnly()) Upload Headers: \(customHeaders)")
            }

            let dataTask = request.validate(statusCode: 200..<300).serializingData()
            dataResponse = await dataTask.response

            let data = try await dataTask.value
            let result = try JSONDecoder().decode(Res<T>.self, from: data)

            if let response = dataResponse?.response {
                let urlPath = response.url?.path ?? "unknown"
                print("\(DateUtils.formatTimeOnly()) <== \(response.statusCode) POST \(urlPath)")
            }

            return result

        } catch let error as AFError {
            if let responseCode = error.responseCode {
                print("\(DateUtils.formatTimeOnly()) ❌ UPLOAD Failed: \(responseCode) - \(error.localizedDescription)")

                // 尝试解析服务器错误响应
                if let data = dataResponse?.data,
                   let errorResponse = try? JSONDecoder().decode(OnlyCodeRes.self, from: data)
                {
                    print(
                        "\(DateUtils.formatTimeOnly()) Server Error: \(errorResponse.msg) (Code: \(errorResponse.code))"
                    )
                    throw BusinessError(code: errorResponse.code, message: errorResponse.msg)
                }
            } else {
                print("\(DateUtils.formatTimeOnly()) ❌ UPLOAD Failed: \(error.localizedDescription)")
            }
            throw NetworkError.unknown(error)
        } catch {
            print("\(DateUtils.formatTimeOnly()) ❌ UPLOAD Error: \(error.localizedDescription)")

            // 在通用错误处理中也尝试解析服务器错误响应
            if let data = dataResponse?.data,
               let errorResponse = try? JSONDecoder().decode(OnlyCodeRes.self, from: data)
            {
                print(
                    "\(DateUtils.formatTimeOnly()) Server Error (Generic): \(errorResponse.msg) (Code: \(errorResponse.code))"
                )
                throw BusinessError(code: errorResponse.code, message: errorResponse.msg)
            }

            throw NetworkError.unknown(error)
        }
    }

    /// 上传文件方法（简单响应，不包装在Res中）
    /// 用于处理直接返回数组或简单对象的上传API
    @discardableResult
    func uploadSimple<T: Decodable>(
        _ endpoint: String,
        files: [UploadData],
        parameters: [String: String]? = nil,
        headers: [String: String]? = nil
    ) async throws -> T {
        guard networkMonitor.isReachable else {
            print("\(DateUtils.formatTimeOnly()) ❌ 网络不可用")
            throw NetworkError.noConnection
        }

        let url = "\(AppConfig.API.baseURL)/\(endpoint)"

        var customHeaders: HTTPHeaders?
        if let headers, !headers.isEmpty {
            customHeaders = HTTPHeaders(headers)
        }

        var dataResponse: DataResponse<Data, AFError>?

        do {
            // 使用 session.upload 来处理 multipart/form-data
            let request = session.upload(
                multipartFormData: { multipartFormData in
                    // 附加文件数据
                    for file in files {
                        multipartFormData.append(
                            file.data,
                            withName: file.fieldName,
                            fileName: file.fileName,
                            mimeType: file.mimeType
                        )
                    }

                    // 附加其他文本参数
                    if let parameters {
                        for (key, value) in parameters {
                            if let data = value.data(using: .utf8) {
                                multipartFormData.append(data, withName: key)
                            }
                        }
                    }
                },
                to: url,
                method: .post,
                headers: customHeaders
            )

            print("\(DateUtils.formatTimeOnly()) Upload Simple Files: \(files.count)")

            let dataTask = request.validate(statusCode: 200..<300).serializingData()
            dataResponse = await dataTask.response

            do {
                let data = try await dataTask.value
                let result = try JSONDecoder().decode(T.self, from: data)

                if let httpResponse = dataResponse?.response {
                    let urlPath = httpResponse.url?.path ?? "unknown"
                    print("\(DateUtils.formatTimeOnly()) <== \(httpResponse.statusCode) POST \(urlPath)")
                }

                return result

            } catch let error as AFError {
                if let responseCode = error.responseCode {
                    print(
                        "\(DateUtils.formatTimeOnly()) ❌ UPLOAD SIMPLE Failed: \(responseCode) - \(error.localizedDescription)"
                    )

                    // 尝试解析服务器错误响应
                    if let data = dataResponse?.data,
                       let errorResponse = try? JSONDecoder().decode(OnlyCodeRes.self, from: data)
                    {
                        print(
                            "\(DateUtils.formatTimeOnly()) Server Error: \(errorResponse.msg) (Code: \(errorResponse.code))"
                        )
                        throw BusinessError(code: errorResponse.code, message: errorResponse.msg)
                    }
                } else {
                    print("\(DateUtils.formatTimeOnly()) ❌ UPLOAD SIMPLE Failed: \(error.localizedDescription)")
                }
                throw NetworkError.unknown(error)
            }
        } catch {
            print("\(DateUtils.formatTimeOnly()) ❌ UPLOAD SIMPLE Error: \(error.localizedDescription)")

            // 在通用错误处理中也尝试解析服务器错误响应
            if let data = dataResponse?.data,
               let errorResponse = try? JSONDecoder().decode(OnlyCodeRes.self, from: data)
            {
                print(
                    "\(DateUtils.formatTimeOnly()) Server Error (Generic): \(errorResponse.msg) (Code: \(errorResponse.code))"
                )
                throw BusinessError(code: errorResponse.code, message: errorResponse.msg)
            }

            throw NetworkError.unknown(error)
        }
    }

    // MARK: - 分页请求方法

    @discardableResult
    func requestPage<T: Decodable>(
        _ endpoint: String,
        method: HttpMethodType,
        parameters: [String: Any]? = nil,
        headers: [String: String]? = nil
    ) async throws -> PageRes<T> {
        guard networkMonitor.isReachable else {
            throw NetworkError.noConnection
        }

        let url = "\(AppConfig.API.baseURL)/\(endpoint)"

        var customHeaders: HTTPHeaders?
        if let headers, !headers.isEmpty {
            customHeaders = HTTPHeaders(headers)
        }

        var dataResponse: DataResponse<Data, AFError>?

        do {
            let request = session.request(
                url,
                method: method.af,
                parameters: parameters,
                encoding: method.encoding,
                headers: customHeaders,
                requestModifier: { $0.timeoutInterval = AppConfig.API.timeoutInterval }
            )

            if let customHeaders {
                print("\(DateUtils.formatTimeOnly()) Page Headers: \(customHeaders)")
            }

            let dataTask = request.validate(statusCode: 200..<300).serializingData()
            dataResponse = await dataTask.response

            do {
                let data = try await dataTask.value
                let result = try JSONDecoder().decode(PageRes<T>.self, from: data)

                if let response = dataResponse?.response {
                    let urlPath = response.url?.path ?? "unknown"
                    print(
                        "\(DateUtils.formatTimeOnly()) <== \(response.statusCode) \(method.af.rawValue.uppercased()) \(urlPath)"
                    )
                }

                return result

            } catch let error as AFError {
                if let responseCode = error.responseCode {
                    print("\(DateUtils.formatTimeOnly()) ❌ PAGE Failed: \(responseCode) - \(error.localizedDescription)"
                    )

                    // 尝试解析服务器错误响应
                    if let data = dataResponse?.data,
                       let errorResponse = try? JSONDecoder().decode(OnlyCodeRes.self, from: data)
                    {
                        print(
                            "\(DateUtils.formatTimeOnly()) Server Error: \(errorResponse.msg) (Code: \(errorResponse.code))"
                        )
                        throw BusinessError(code: errorResponse.code, message: errorResponse.msg)
                    }

                    if case .responseSerializationFailed(let reason) = error {
                        print("\(DateUtils.formatTimeOnly()) Serialization Failed: \(reason)")
                        if let underlyingError = error.underlyingError {
                            print("\(DateUtils.formatTimeOnly()) Underlying Error: \(underlyingError)")
                        }
                    }
                } else {
                    print("\(DateUtils.formatTimeOnly()) ❌ PAGE Failed: \(error.localizedDescription)")
                }

                throw NetworkError.unknown(error)
            }
        } catch {
            print("\(DateUtils.formatTimeOnly()) ❌ PAGE Error: \(error.localizedDescription)")

            // 在通用错误处理中也尝试解析服务器错误响应
            if let data = dataResponse?.data,
               let errorResponse = try? JSONDecoder().decode(OnlyCodeRes.self, from: data)
            {
                print(
                    "\(DateUtils.formatTimeOnly()) Server Error (Generic): \(errorResponse.msg) (Code: \(errorResponse.code))"
                )
                throw BusinessError(code: errorResponse.code, message: errorResponse.msg)
            }

            throw NetworkError.unknown(error)
        }
    }

    // MARK: - 简单请求方法（不包装在Res中）

    @discardableResult
    func requestSimple<T: Decodable>(
        _ endpoint: String,
        method: HttpMethodType,
        parameters: [String: Any]? = nil,
        headers: [String: String]? = nil
    ) async throws -> T {
        guard networkMonitor.isReachable else {
            print("\(DateUtils.formatTimeOnly()) ❌ 网络不可用")
            throw NetworkError.noConnection
        }

        let url = "\(AppConfig.API.baseURL)/\(endpoint)"

        var customHeaders: HTTPHeaders?
        if let headers, !headers.isEmpty {
            customHeaders = HTTPHeaders(headers)
        }

        var dataResponse: DataResponse<Data, AFError>?

        do {
            let request = session.request(
                url,
                method: method.af,
                parameters: parameters,
                encoding: method.encoding,
                headers: customHeaders,
                requestModifier: { $0.timeoutInterval = AppConfig.API.timeoutInterval }
            )

            let dataTask = request.validate(statusCode: 200..<300).serializingData()
            dataResponse = await dataTask.response

            do {
                let data = try await dataTask.value
                let result = try JSONDecoder().decode(T.self, from: data)

                if let httpResponse = dataResponse?.response {
                    let urlPath = httpResponse.url?.path ?? "unknown"
                    print(
                        "\(DateUtils.formatTimeOnly()) <== \(httpResponse.statusCode) \(method.af.rawValue.uppercased()) \(urlPath)"
                    )
                }

                return result

            } catch let error as AFError {
                if let responseCode = error.responseCode {
                    print(
                        "\(DateUtils.formatTimeOnly()) ❌ SIMPLE Failed: \(responseCode) - \(error.localizedDescription)"
                    )

                    // 尝试解析服务器错误响应
                    if let data = dataResponse?.data,
                       let errorResponse = try? JSONDecoder().decode(OnlyCodeRes.self, from: data)
                    {
                        print(
                            "\(DateUtils.formatTimeOnly()) Server Error: \(errorResponse.msg) (Code: \(errorResponse.code))"
                        )
                        throw BusinessError(code: errorResponse.code, message: errorResponse.msg)
                    }
                } else {
                    print("\(DateUtils.formatTimeOnly()) ❌ SIMPLE Failed: \(error.localizedDescription)")
                }
                throw NetworkError.unknown(error)
            }
        } catch {
            print("\(DateUtils.formatTimeOnly()) ❌ SIMPLE Error: \(error.localizedDescription)")

            // 在通用错误处理中也尝试解析服务器错误响应
            if let data = dataResponse?.data,
               let errorResponse = try? JSONDecoder().decode(OnlyCodeRes.self, from: data)
            {
                print(
                    "\(DateUtils.formatTimeOnly()) Server Error (Generic): \(errorResponse.msg) (Code: \(errorResponse.code))"
                )
                throw BusinessError(code: errorResponse.code, message: errorResponse.msg)
            }

            throw NetworkError.unknown(error)
        }
    }

    // MARK: requestData：专用于文件下载

    /// 执行网络请求并返回原始二进制数据。
    /// 非常适合下载文件、图片等。
    /// 此方法会验证状态码，但不会尝试解码 JSON。
    func requestData(
        _ endpoint: String,
        method: HttpMethodType,
        parameters: [String: Any]? = nil,
        headers: [String: String]? = nil
    ) async throws -> Data {
        guard networkMonitor.isReachable else {
            print("\(DateUtils.formatTimeOnly()) ❌ 网络不可用")
            throw NetworkError.noConnection
        }

        let url = "\(AppConfig.API.baseURL)/\(endpoint)"

        var customHeaders: HTTPHeaders?
        if let headers, !headers.isEmpty {
            customHeaders = HTTPHeaders(headers)
        }

        // 我们仍然使用 serializingData()，但之后会直接返回它的 'value'
        let dataTask = session.request(
            url,
            method: method.af,
            parameters: parameters,
            encoding: method.encoding,
            headers: customHeaders,
            requestModifier: { $0.timeoutInterval = AppConfig.API.timeoutInterval }
        )
        .validate(statusCode: 200..<300) // 至关重要：如果不是成功状态码则失败
        .serializingData()

        if let customHeaders {
            print("\(DateUtils.formatTimeOnly()) Custom Headers: \(customHeaders)")
        }

        let response = await dataTask.response

        do {
            // 等待最终数据。如果验证失败，这里会抛出异常。
            let data = try await dataTask.value

            if let httpResponse = response.response {
                let urlPath = httpResponse.url?.path ?? "unknown"
                print(
                    "\(DateUtils.formatTimeOnly()) <== \(httpResponse.statusCode) \(method.af.rawValue.uppercased()) \(urlPath) [Data: \(data.count) bytes]"
                )
            }

            // 主要区别：直接返回原始数据！
            return data

        } catch let error as AFError {
            // 这个错误处理块仍然非常有用，因为下载也可能因为服务器错误而失败（例如“文件未找到”）。
            // 我们可以尝试解析这个错误信息。
            if let responseCode = error.responseCode {
                print(
                    "\(DateUtils.formatTimeOnly()) ❌ \(method.af.rawValue.uppercased()) Failed: \(responseCode) - \(error.localizedDescription)"
                )
                if let data = response.data,
                   let errorResponse = try? JSONDecoder().decode(OnlyCodeRes.self, from: data) // 或者一个通用的 ErrorRes
                {
                    print(
                        "\(DateUtils.formatTimeOnly()) Server Error: \(errorResponse.msg) (Code: \(errorResponse.code))"
                    )
                    throw BusinessError(code: errorResponse.code, message: errorResponse.msg)
                }
            }
            throw NetworkError.unknown(error)
        } catch {
            // 通用 catch 块
            print(
                "\(DateUtils.formatTimeOnly()) ❌ \(method.af.rawValue.uppercased()) Error: \(error.localizedDescription)"
            )
            throw NetworkError.unknown(error)
        }
    }
}
