import Foundation

final class Http {
    static let shared = Http()
    private let http: HttpCore

    init() {
        http = HttpCore()
    }

    // MARK: GET

    func GET<T: Decodable>(
        _ endpoint: String,
        params: [String: Any]? = nil,
        headers: [String: String]? = nil
    ) async throws -> T {
        let res: Res<T> = try await http.request(
            endpoint,
            method: .get,
            parameters: params,
            headers: headers
        )

        guard res.code == 200 else {
            throw BusinessError(code: res.code, message: res.msg)
        }

        return res.data
    }

    // MARK: POST

    func POST<T: Decodable>(
        _ endpoint: String,
        body: [String: Any]? = nil,
        headers: [String: String]? = nil
    ) async throws -> T {
        let res: Res<T> = try await http.request(
            endpoint,
            method: .post,
            parameters: body,
            headers: headers
        )

        guard res.code == 200 else {
            throw BusinessError(code: res.code, message: res.msg)
        }

        return res.data
    }

    // MARK: PUT

    func PUT<T: Decodable>(
        _ endpoint: String,
        body: [String: Any]? = nil,
        headers: [String: String]? = nil
    ) async throws -> T {
        let res: Res<T> = try await http.request(
            endpoint,
            method: .put,
            parameters: body,
            headers: headers
        )

        guard res.code == 200 else {
            throw BusinessError(code: res.code, message: res.msg)
        }

        return res.data
    }

    // MARK: PATCH

    func PATCH<T: Decodable>(
        _ endpoint: String,
        body: [String: Any]? = nil,
        headers: [String: String]? = nil
    ) async throws -> T {
        let res: Res<T> = try await http.request(
            endpoint,
            method: .patch,
            parameters: body,
            headers: headers
        )

        guard res.code == 200 else {
            throw BusinessError(code: res.code, message: res.msg)
        }

        return res.data
    }

    // MARK: DELETE

    func DELETE<T: Decodable>(
        _ endpoint: String,
        params: [String: Any]? = nil,
        headers: [String: String]? = nil
    ) async throws -> T {
        let res: Res<T> = try await http.request(
            endpoint,
            method: .delete,
            parameters: params,
            headers: headers
        )

        guard res.code == 200 else {
            throw BusinessError(code: res.code, message: res.msg)
        }

        return res.data
    }

    // MARK: UPLOAD

    func UPLOAD<T: Decodable>(
        _ endpoint: String,
        files: [UploadData],
        params: [String: String]? = nil, // 这里的参数是和文件一起发送的文本字段
        headers: [String: String]? = nil
    ) async throws -> T {
        // 调用 HttpCore 中新创建的 upload 方法
        let res: Res<T> = try await http.upload(
            endpoint,
            files: files,
            parameters: params,
            headers: headers
        )

        // 保持风格一致：检查业务错误码
        guard res.code == 200 else {
            throw BusinessError(code: res.code, message: res.msg)
        }

        // 保持风格一致：返回解包后的数据
        return res.data
    }

    // MARK: GETPAGE

    func GETPAGE<T: Decodable>(
        _ endpoint: String,
        params: [String: Any]? = nil,
        headers: [String: String]? = nil
    ) async throws -> T {
        let res: PageRes<T> = try await http.requestPage(
            endpoint,
            method: .get,
            parameters: params,
            headers: headers
        )

        return res.data
    }

    // MARK: POSTPAGE

    func POSTPAGE<T: Decodable>(
        _ endpoint: String,
        body: [String: Any]? = nil,
        headers: [String: String]? = nil
    ) async throws -> T {
        let res: PageRes<T> = try await http.requestPage(
            endpoint,
            method: .post,
            parameters: body,
            headers: headers
        )

        return res.data
    }

    // MARK: - 简单响应方法（不包装在Res中）

    /// GET请求，处理简单的响应结构（不包装在Res中）
    /// 用于账号注销等返回简单结构的API
    func GET_SIMPLE<T: Decodable>(
        _ endpoint: String,
        params: [String: Any]? = nil,
        headers: [String: String]? = nil
    ) async throws -> T {
        return try await http.requestSimple(
            endpoint,
            method: .get,
            parameters: params,
            headers: headers
        )
    }

    /// POST请求，处理简单的响应结构（不包装在Res中）
    /// 用于删除等返回简单结构的API
    func POST_SIMPLE<T: Decodable>(
        _ endpoint: String,
        body: [String: Any]? = nil,
        headers: [String: String]? = nil
    ) async throws -> T {
        return try await http.requestSimple(
            endpoint,
            method: .post,
            parameters: body,
            headers: headers
        )
    }

    /// UPLOAD请求，处理简单的响应结构（不包装在Res中）
    /// 用于知识库文件上传等直接返回数组的API
    func UPLOAD_SIMPLE<T: Decodable>(
        _ endpoint: String,
        files: [UploadData],
        params: [String: String]? = nil,
        headers: [String: String]? = nil
    ) async throws -> T {
        return try await http.uploadSimple(
            endpoint,
            files: files,
            parameters: params,
            headers: headers
        )
    }

    /// GET请求，处理直接返回数组的响应（不包装在Res中）
    /// 用于历史对话详情等返回不规则数组的API
    func GET_ARRAY<T: Decodable>(
        _ endpoint: String,
        params: [String: Any]? = nil,
        headers: [String: String]? = nil
    ) async throws -> T {
        return try await http.requestSimple(
            endpoint,
            method: .get,
            parameters: params,
            headers: headers
        )
    }

    // DOWNLOAD
    func DOWNLOAD(
        _ endpoint: String,
        params: [String: Any]? = nil,
        headers: [String: String]? = nil
    ) async throws -> Data {
        // 这个方法只是简单地调用我们新的 'requestData' 工作方法。
        // 按照惯例，我们使用 .get 方法进行下载。
        let data = try await http.requestData(
            endpoint,
            method: .get,
            parameters: params,
            headers: headers
        )
        return data
    }
}
