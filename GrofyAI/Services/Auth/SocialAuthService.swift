import Foundation

class SocialAuthService {
    // MARK: - Apple登录

    func authenticateWithApple(
        authorizationCode: String,
        identityToken: String,
        state: String?,
        userInfo: AppleUserInfo?
    ) async throws -> SocialAuthResponse {
        let request = AppleAuthRequest(
            terminal: SocialAuthConfig.terminal,
            locale: Language.deviceLanguage,
            code: authorizationCode,
            idToken: identityToken,
            state: state,
            user: userInfo
        )

        do {
            var requestBody: [String: Any] = [
                "terminal": request.terminal,
                "locale": request.locale,
                "code": request.code,
                "id_token": request.idToken,
            ]

            if let state = request.state, !state.isEmpty {
                requestBody["state"] = state
            }

            if let userInfo = request.user {
                requestBody["user"] = encodeUserInfo(userInfo)
            } else {
                requestBody["user"] = [String: Any]()
            }

            let response: SocialAuthResponse = try await Http.shared.POST(
                SocialAuthApi.apple.path,
                body: requestBody
            )

            return response

        } catch let error as BusinessError {
            throw SocialAuthError.appleSignInFailed(error.message)
        } catch {
            throw SocialAuthError.networkError(error.localizedDescription)
        }
    }

    // MARK: - Google登录

    func authenticateWithGoogle(idToken: String) async throws -> SocialAuthResponse {
        let request = GoogleAuthRequest(
            token: idToken,
            terminal: SocialAuthConfig.terminal,
            locale: Language.deviceLanguage
        )

        do {
            let response: SocialAuthResponse = try await Http.shared.POST(
                SocialAuthApi.google.path,
                body: [
                    "token": request.token,
                    "terminal": request.terminal,
                    "locale": request.locale,
                ]
            )

            return response

        } catch let error as BusinessError {
            throw SocialAuthError.googleSignInFailed(error.message)
        } catch {
            throw SocialAuthError.networkError(error.localizedDescription)
        }
    }

    // MARK: - 私有辅助方法

    private func encodeUserInfo(_ userInfo: AppleUserInfo?) -> [String: Any] {
        guard let userInfo else {
            return [String: Any]()
        }

        var userDict: [String: Any] = [:]

        if let email = userInfo.email {
            userDict["email"] = email
        }

        if let name = userInfo.name {
            var nameDict: [String: Any] = [:]
            if let firstName = name.firstName {
                nameDict["firstName"] = firstName
            }
            if let lastName = name.lastName {
                nameDict["lastName"] = lastName
            }
            if !nameDict.isEmpty {
                userDict["name"] = nameDict
            }
        }

        return userDict
    }
}
