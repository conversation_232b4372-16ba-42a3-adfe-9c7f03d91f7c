import Foundation

class RegisterService {
    func register(req: RegisterReq) async throws -> OnlyCodeRes {
        let res: OnlyCodeRes = try await Http.shared.POST(
            AuthApi.register.path,
            body: [
                "email": req.email,
                "password": req.password,
                "terminal": req.terminal,
                "locale": req.locale,
                "code": req.code,
            ]
        )

        return res
    }
}
