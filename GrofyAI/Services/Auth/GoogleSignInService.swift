import Foundation
import GoogleSignIn
import UI<PERSON>it

@MainActor
class GoogleSignInService: ObservableObject {
    private let socialAuthService = SocialAuthService()

    // MARK: - Google登录

    func signInWithGoogle() async throws -> SocialAuthResponse {
        return try await withCheckedThrowingContinuation { continuation in
            guard let presentingViewController = getRootViewController() else {
                continuation.resume(throwing: SocialAuthError.googleSignInFailed("无法获取根视图控制器"))
                return
            }

            GIDSignIn.sharedInstance.signIn(withPresenting: presentingViewController) { [weak self] result, error in
                if let error {
                    let nsError = error as NSError
                    if nsError.code == GIDSignInError.canceled.rawValue {
                        continuation.resume(throwing: SocialAuthError.authorizationCancelled)
                    } else {
                        continuation.resume(throwing: SocialAuthError.googleSignInFailed(error.localizedDescription))
                    }
                    return
                }

                guard let result,
                      let idToken = result.user.idToken?.tokenString
                else {
                    continuation.resume(throwing: SocialAuthError.missingCredentials)
                    return
                }

                Task {
                    do {
                        let response = try await self?.socialAuthService.authenticateWithGoogle(idToken: idToken)
                        if let response {
                            continuation.resume(returning: response)
                        } else {
                            continuation.resume(throwing: SocialAuthError.googleSignInFailed("服务不可用"))
                        }
                    } catch {
                        continuation.resume(throwing: error)
                    }
                }
            }
        }
    }

    private func getRootViewController() -> UIViewController? {
        guard let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
              let window = windowScene.windows.first
        else {
            return nil
        }
        return window.rootViewController
    }

    func signOut() {
        GIDSignIn.sharedInstance.signOut()
    }
}
