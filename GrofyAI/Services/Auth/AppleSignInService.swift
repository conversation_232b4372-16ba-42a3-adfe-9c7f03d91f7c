import AuthenticationServices
import Combine
import Foundation

@MainActor
class AppleSignInService: NSObject, ObservableObject {
    private let socialAuthService = SocialAuthService()

    private var currentDelegate: AppleSignInDelegate?
    private var currentController: ASAuthorizationController?
    private var currentContextProvider: ASAuthorizationControllerPresentationContextProviding?

    // MARK: - Apple登录

    func signInWithApple() async throws -> SocialAuthResponse {
        guard currentController == nil else {
            throw SocialAuthError.appleSignInFailed("另一个Apple登录流程已在进行中")
        }

        return try await withCheckedThrowingContinuation { continuation in
            let request = ASAuthorizationAppleIDProvider().createRequest()
            request.requestedScopes = [.fullName, .email]

            let authorizationController = ASAuthorizationController(authorizationRequests: [request])
            let delegate = AppleSignInDelegate(
                continuation: continuation,
                socialAuthService: socialAuthService,
                cleanup: { [weak self] in
                    self?.currentDelegate = nil
                    self?.currentController = nil
                    self?.currentContextProvider = nil
                }
            )
            let contextProvider = AppleSignInPresentationContextProvider()

            self.currentDelegate = delegate
            self.currentController = authorizationController
            self.currentContextProvider = contextProvider

            authorizationController.delegate = delegate
            authorizationController.presentationContextProvider = contextProvider
            authorizationController.performRequests()
        }
    }
}

// MARK: - Apple登录委托

private class AppleSignInDelegate: NSObject, ASAuthorizationControllerDelegate {
    private var continuation: CheckedContinuation<SocialAuthResponse, Error>?
    private let socialAuthService: SocialAuthService
    private let cleanup: () -> Void

    init(
        continuation: CheckedContinuation<SocialAuthResponse, Error>,
        socialAuthService: SocialAuthService,
        cleanup: @escaping () -> Void
    ) {
        self.continuation = continuation
        self.socialAuthService = socialAuthService
        self.cleanup = cleanup
        super.init()
    }

    private func resumeContinuation(with result: Result<SocialAuthResponse, Error>) {
        guard let continuation else {
            print("⚠️ AppleSignInDelegate: Continuation已被使用或为nil")
            return
        }

        self.continuation = nil

        switch result {
        case .success(let response):
            continuation.resume(returning: response)
        case .failure(let error):
            continuation.resume(throwing: error)
        }

        cleanup()
    }

    func authorizationController(
        controller: ASAuthorizationController,
        didCompleteWithAuthorization authorization: ASAuthorization
    ) {
        guard let appleIDCredential = authorization.credential as? ASAuthorizationAppleIDCredential else {
            resumeContinuation(with: .failure(SocialAuthError.missingCredentials))
            return
        }

        // 获取授权码
        guard let authorizationCodeData = appleIDCredential.authorizationCode,
              let authorizationCode = String(data: authorizationCodeData, encoding: .utf8)
        else {
            resumeContinuation(with: .failure(SocialAuthError.missingCredentials))
            return
        }

        // 获取身份令牌
        guard let identityTokenData = appleIDCredential.identityToken,
              let identityToken = String(data: identityTokenData, encoding: .utf8)
        else {
            resumeContinuation(with: .failure(SocialAuthError.missingCredentials))
            return
        }

        let state = appleIDCredential.state

        var userInfo: AppleUserInfo? = nil

        // 检查是否有用户信息（首次授权时提供）
        let hasUserName = appleIDCredential.fullName != nil
        let hasEmail = appleIDCredential.email != nil

        if hasUserName || hasEmail {
            var name: AppleUserName? = nil
            if let fullName = appleIDCredential.fullName {
                name = AppleUserName(
                    firstName: fullName.givenName,
                    lastName: fullName.familyName
                )
            }

            userInfo = AppleUserInfo(
                name: name,
                email: appleIDCredential.email
            )
        }

        Task.detached { [socialAuthService] in
            do {
                let response = try await socialAuthService.authenticateWithApple(
                    authorizationCode: authorizationCode,
                    identityToken: identityToken,
                    state: state,
                    userInfo: userInfo
                )
                await MainActor.run { [weak self] in
                    self?.resumeContinuation(with: .success(response))
                }
            } catch {
                await MainActor.run { [weak self] in
                    self?.resumeContinuation(with: .failure(error))
                }
            }
        }
    }

    func authorizationController(controller: ASAuthorizationController, didCompleteWithError error: Error) {
        let socialAuthError = if let authError = error as? ASAuthorizationError {
            switch authError.code {
            case .canceled:
                SocialAuthError.authorizationCancelled
            case .failed:
                SocialAuthError.appleSignInFailed("授权失败")
            case .invalidResponse:
                SocialAuthError.invalidResponse
            case .notHandled:
                SocialAuthError.appleSignInFailed("请求未处理")
            case .unknown:
                if isUserCancellation(authError) {
                    SocialAuthError.authorizationCancelled
                } else {
                    SocialAuthError.appleSignInFailed("未知错误")
                }
            case .notInteractive:
                SocialAuthError.appleSignInFailed("非交互式环境")
            case .matchedExcludedCredential:
                SocialAuthError.appleSignInFailed("凭据已被排除")
            @unknown default:
                if isUserCancellation(authError) {
                    SocialAuthError.authorizationCancelled
                } else {
                    SocialAuthError.appleSignInFailed("未知授权错误: \(authError.localizedDescription)")
                }
            }
        } else {
            SocialAuthError.appleSignInFailed(error.localizedDescription)
        }

        resumeContinuation(with: .failure(socialAuthError))
    }

    /// 检查是否为用户取消操作
    private func isUserCancellation(_ error: ASAuthorizationError) -> Bool {
        // Apple Sign-in 用户取消相关的错误码：
        // 1000: ASAuthorizationError.unknown - 用户取消时经常出现
        // 1001: ASAuthorizationError.canceled - 标准的取消代码
        // 1004: ASAuthorizationError.notHandled - 有时也表示用户取消
        let userCancellationCodes: [Int] = [1000, 1001, 1004]
        return userCancellationCodes.contains(error.code.rawValue)
    }
}

// MARK: - 展示上下文提供者

private class AppleSignInPresentationContextProvider: NSObject, ASAuthorizationControllerPresentationContextProviding {
    func presentationAnchor(for controller: ASAuthorizationController) -> ASPresentationAnchor {
        guard let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
              let window = windowScene.windows.first
        else {
            fatalError("无法获取窗口")
        }
        return window
    }
}
