import Foundation

class SendVerificationCodeService {
    func sendVerificationCode(email: String, emailType: EmailType = .register) async throws -> OnlyCodeRes {
        let req = SendVerificationCodeReq(
            emailType: emailType.rawValue,
            email: email
        )

        let res: OnlyCodeRes = try await Http.shared.POST_SIMPLE(
            AuthApi.sendVerificationCode.path,
            body: [
                "emailType": req.emailType,
                "email": req.email,
            ]
        )

        return res
    }
}
