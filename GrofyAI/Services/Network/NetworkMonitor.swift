import Alamofire
import Foundation

// MARK: - 网络监听器

final class NetworkMonitor {
    static let shared = NetworkMonitor()

    private let reachability = NetworkReachabilityManager()
    private(set) var isReachable = true

    private init() {}

    func startMonitoring() {
        reachability?.startListening(onQueue: .main) { [weak self] status in
            guard let self else { return }

            let wasReachable = isReachable

            switch status {
            case .reachable(.cellular), .reachable(.ethernetOrWiFi):
                isReachable = true
                if !wasReachable {
                    print("\(DateUtils.formatTimeOnly()) ✅ 网络已恢复")
                    handleNetworkRecovery()
                }

            case .notReachable:
                isReachable = false
                if wasReachable {
                    print("\(DateUtils.formatTimeOnly()) ❌ 网络已断开")
                    Task { @MainActor in
                        ToastManager.shared.showError("网络连接已断开")
                    }
                }

            case .unknown:
                break
            }
        }
    }

    private func handleNetworkRecovery() {
        guard AuthStore.shared.hasValidToken() else {
            print("\(DateUtils.formatTimeOnly()) ⚠️ 网络恢复：无token，跳过处理")
            return
        }

        Task {
            _ = await AuthStore.shared.validateTokenWithResult(context: "网络恢复")
        }
    }

    func stopMonitoring() {
        reachability?.stopListening()
    }
}
