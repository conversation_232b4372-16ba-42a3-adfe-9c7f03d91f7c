import Foundation
import SwiftUI

struct DownloadReq: Encodable {
    let url: String
}

/// 图片上传前的预处理错误
enum ImageUploadPreProcessingError: Error, LocalizedError {
    case conversionFailed(fileName: String)
    case tooLarge(fileName: String, sizeInMB: Double, maxSizeInMB: Int)

    var errorDescription: String? {
        switch self {
        case .conversionFailed(let fileName):
            return "图片 '\(fileName)' 格式转换失败。"
        case .tooLarge(let fileName, let sizeInMB, let maxSizeInMB):
            return String(format: "图片 '\(fileName)' 体积过大 (%.2f MB)，超过了 \(maxSizeInMB)MB 的限制。", sizeInMB)
        }
    }
}


class FileService {
    func download(req: DownloadReq) async throws -> Data {
        let res: Data = try await Http.shared.DOWNLOAD(FilesApi.download.path, params: req.toDictionary())
        return res
    }

    func uploadImages(
        images: [(imageInfo: UIImage, name: String)],
        threadId: String,
        format: ImageFormat = .jpeg(compressionQuality: 0.8)
    ) async throws -> [UploadImagesRes] {
        
        // 定义 10MB 的字节数
        let maxSizeInBytes = 10 * 1024 * 1024
        
        var filesToUpload: [UploadData] = []

        for (index, image) in images.enumerated() {
            let fileData: Data?
            let fileName: String
            let mimeType: String

            // 根据选择的格式进行转换
            switch format {
            case .jpeg(let quality):
                fileData = image.imageInfo.jpegData(compressionQuality: quality)
                fileName = image.name
                mimeType = "image/jpeg"

            case .png:
                fileData = image.imageInfo.pngData()
                fileName = image.name
                mimeType = "image/png"
            }

            // 确保转换成功
            guard let data = fileData else {
                print("⚠️ 警告：第 \(index + 1) 张图片转换失败，已跳过。")
//                continue // 跳过这张无法转换的图片
                throw ImageUploadPreProcessingError.conversionFailed(fileName: image.name)
            }
            
            print("图片大小为：",data.count)
            if data.count > maxSizeInBytes {
                let sizeInMB = Double(data.count) / 1024.0 / 1024.0
                // 图片过大，立即抛出错误并停止
                throw ImageUploadPreProcessingError.tooLarge(
                    fileName: image.name,
                    sizeInMB: sizeInMB,
                    maxSizeInMB: 10
                )
            }

            filesToUpload.append(
                UploadData(
                    data: data,
                    fileName: fileName,
                    mimeType: mimeType,
                    fieldName: "files" // 后端接收文件的字段名
                )
            )
        }

        // 如果没有一张图片成功转换，可以直接抛出错误或返回空数组
        guard !filesToUpload.isEmpty else {
            throw ImageConversionError.allImagesFailedToConvert
        }

        // 调用底层的上传方法
        let uploadedFiles: [UploadImagesRes] = try await Http.shared.UPLOAD(
            FilesApi.uploadImages(threadId: threadId).path,
            files: filesToUpload
        )

        return uploadedFiles
    }

    func uploadImageToPixverse(
        images: [(imageInfo: UIImage, name: String)],
        format: ImageFormat = .jpeg(compressionQuality: 0.8)
    ) async throws -> UploadImageToPixverseRes {
        var filesToUpload: [UploadData] = []

        for (index, image) in images.enumerated() {
            let fileData: Data?
            let fileName: String
            let mimeType: String

            // 根据选择的格式进行转换
            switch format {
            case .jpeg(let quality):
                fileData = image.imageInfo.jpegData(compressionQuality: quality)
                fileName = image.name
                mimeType = "image/jpeg"

            case .png:
                fileData = image.imageInfo.pngData()
                fileName = image.name
                mimeType = "image/png"
            }

            // 确保转换成功
            guard let data = fileData else {
                print("⚠️ 警告：第 \(index + 1) 张图片转换失败，已跳过。")
                continue // 跳过这张无法转换的图片
            }

            filesToUpload.append(
                UploadData(
                    data: data,
                    fileName: fileName,
                    mimeType: mimeType,
                    fieldName: "file" // 后端接收文件的字段名
                )
            )
        }

        // 如果没有一张图片成功转换，可以直接抛出错误或返回空数组
        guard !filesToUpload.isEmpty else {
            throw ImageConversionError.allImagesFailedToConvert
        }

        // 调用底层的上传方法
        let uploadedFiles: UploadImageToPixverseRes = try await Http.shared.UPLOAD(
            FilesApi.uploadImageToPixverse.path,
            files: filesToUpload
        )

        return uploadedFiles
    }

    /// 上传知识库文件
    /// - Parameters:
    ///   - categoryId: 知识库分类ID
    ///   - files: 要上传的文件数组
    /// - Returns: 上传结果数组
    func uploadKnowledgeFiles(
        categoryId: Int,
        files: [UploadData]
    ) async throws -> KnowledgeFileUploadResponse {
        // 使用UPLOAD_SIMPLE方法，因为知识库文件上传API直接返回数组，不包装在Res中
        let uploadResults: KnowledgeFileUploadResponse = try await Http.shared.UPLOAD_SIMPLE(
            FilesApi.uploadKnowledgeFiles(categoryId: categoryId).path,
            files: files,
            params: ["category_id": String(categoryId)]
        )

        return uploadResults
    }

    func getKnowledgeFiles(
        categoryId: Int? = nil,
        page: Int? = nil,
        size: Int? = nil,
        type: String? = nil,
        orderBy: String? = nil,
        title: String? = nil
    ) async throws -> KnowledgeFilePageData {
        let res: KnowledgeFilePageData = try await Http.shared.GETPAGE(
            FilesApi.getKnowledgeFiles(
                categoryId: categoryId,
                page: page,
                size: size,
                type: type,
                orderBy: orderBy,
                title: title
            ).path
        )
        return res
    }

    /// 获取知识库分类列表
    /// - Returns: 知识库分类列表
    func getKnowledgeCategories() async throws -> [KnowledgeCategory] {
        let categories: [KnowledgeCategory] = try await Http.shared.GET(
            FilesApi.getKnowledgeCategories.path
        )
        return categories
    }

    /// 删除知识库文件
    /// - Parameter ids: 文件ID数组
    func deleteKnowledgeFiles(ids: [Int]) async throws {
        let body = ["ids": ids.map { String($0) }]

        do {
            let response: OnlyCodeRes = try await Http.shared.POST_SIMPLE(
                FilesApi.deleteKnowledgeFiles.path,
                body: body
            )

            if response.code != 200 {
                throw BusinessError(code: response.code, message: response.msg)
            }

        } catch {
            throw error
        }
    }

    /// 删除知识库分类
    /// - Parameter ids: 分类ID数组
    func deleteKnowledgeCategories(ids: [Int]) async throws {
        let body = ["ids": ids.map { String($0) }]

        do {
            let response: OnlyCodeRes = try await Http.shared.POST_SIMPLE(
                FilesApi.deleteKnowledgeCategories.path,
                body: body
            )

            if response.code != 200 {
                throw BusinessError(code: response.code, message: response.msg)
            }

        } catch {
            throw error
        }
    }

    /// 更新知识库分类
    /// - Parameters:
    ///   - id: 分类ID
    ///   - title: 分类标题
    ///   - favicon: 分类图标（可选）
    ///   - content: 分类内容/简介（可选）
    func updateKnowledgeCategory(
        id: Int,
        title: String,
        favicon: String? = nil,
        content: String? = nil
    ) async throws {
        var body: [String: Any] = [
            "id": id,
            "title": title,
        ]

        if let favicon {
            body["favicon"] = favicon
        }

        if let content {
            body["content"] = content
        }

        do {
            struct UpdateResponse: Codable {
                let code: Int
                let msg: String
            }

            let response: UpdateResponse = try await Http.shared.POST_SIMPLE(
                FilesApi.updateKnowledgeCategory.path,
                body: body
            )

            if response.code != 200 {
                throw BusinessError(code: response.code, message: response.msg)
            }

        } catch {
            throw error
        }
    }

    /// 创建知识库分类
    /// - Parameters:
    ///   - title: 分类标题
    ///   - favicon: 分类图标（可选）
    ///   - content: 分类内容/简介（可选）
    func createKnowledgeCategory(
        title: String,
        favicon: String? = nil,
        content: String? = nil
    ) async throws {
        var body: [String: Any] = [
            "title": title,
        ]

        if let favicon {
            body["favicon"] = favicon
        }

        if let content {
            body["content"] = content
        }

        do {
            struct CreateResponse: Codable {
                let code: Int
                let msg: String
            }

            let response: CreateResponse = try await Http.shared.POST_SIMPLE(
                FilesApi.createKnowledgeCategory.path,
                body: body
            )

            if response.code != 200 {
                throw BusinessError(code: response.code, message: response.msg)
            }

        } catch {
            throw error
        }
    }

    /// 更新知识库文件标签
    /// - Parameters:
    ///   - fileId: 文件ID
    ///   - tags: 标签数组
    func updateKnowledgeFileTags(
        fileId: Int,
        tags: [String]
    ) async throws {
        let body: [String: Any] = [
            "id": fileId,
            "tags": tags,
        ]

        do {
            struct UpdateTagsResponse: Codable {
                let code: Int
                let msg: String
            }

            let response: UpdateTagsResponse = try await Http.shared.POST_SIMPLE(
                FilesApi.updateKnowledgeFileTags.path,
                body: body
            )

            if response.code != 200 {
                throw BusinessError(code: response.code, message: response.msg)
            }

        } catch {
            throw error
        }
    }

    /// 移动知识库文件到指定分类
    /// - Parameters:
    ///   - fileId: 要移动的文件ID
    ///   - targetCategoryId: 目标知识库分类ID
    func moveKnowledgeFile(
        fileId: Int,
        targetCategoryId: Int
    ) async throws {
        let body: [String: Any] = [
            "id": fileId,
            "targetCatId": targetCategoryId,
        ]

        do {
            struct MoveFileResponse: Codable {
                let code: Int
                let msg: String
            }

            let response: MoveFileResponse = try await Http.shared.POST_SIMPLE(
                FilesApi.moveKnowledgeFile.path,
                body: body
            )

            if response.code != 200 {
                throw BusinessError(code: response.code, message: response.msg)
            }

        } catch {
            throw error
        }
    }
}

enum ImageFormat {
    case jpeg(compressionQuality: CGFloat)
    case png
}

// 自定义一个错误类型，方便调试
enum ImageConversionError: Error, LocalizedError {
    case allImagesFailedToConvert
    var errorDescription: String? {
        "所有图片都无法转换为可上传的数据格式。"
    }
}
