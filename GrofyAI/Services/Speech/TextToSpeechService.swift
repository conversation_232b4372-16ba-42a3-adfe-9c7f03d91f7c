import AVFoundation
import Combine
import Foundation
import MicrosoftCognitiveServicesSpeech

// MARK: - 文字转语音服务

/// 专门用于TTS的服务，支持预加载和批处理
@MainActor
final class TextToSpeechService: NSObject, ObservableObject {
    private let connectionPool = SpeechSDKConnectionPool()
    private let audioSessionManager = AudioSessionManager()
    private let ttsCache = TTSCache()

    private var synthesizerPool: [SPXSpeechSynthesizer] = []
    private let maxPoolSize = 3

    @Published private(set) var currentPlayer: AVAudioPlayer?
    @Published private(set) var currentMessageId: String?

    private var currentTask: Task<Void, Error>?
    private var currentTokenTask: URLSessionDataTask?

    /// 预热服务（应用启动时调用）
    func warmup() async {
        do {
            _ = try await AudioTokenService.shared.getAzureToken()

            try await audioSessionManager.configureForMode(.playback)

        } catch {
            print("TTS warmup failed: \(error)")
        }
    }

    /// 合成并播放
    func synthesizeAndPlay(
        messageId: String,
        text: String,
        voice: SpeechVoice,
        onStateChange: @escaping (TTSDisplayState) -> Void
    ) async throws {
        // 取消当前任务
        currentTask?.cancel()

        // 停止当前正在播放的音频
        if let currentId = currentMessageId, currentId != messageId {
            await stopPlayback(messageId: currentId)
        }

        // 更新当前消息ID
        currentMessageId = messageId

        // 创建新任务
        let task = Task { @MainActor in
            do {
                // 检查缓存
                if let cachedData = ttsCache.getCachedAudio(forText: text, voice: voice) {
                    try Task.checkCancellation()
                    onStateChange(.preparingCached)
                    try await playFromCache(messageId: messageId, audioData: cachedData, onStateChange: onStateChange)
                    return
                }

                // 准备合成
                onStateChange(.preparing)

                // 检查是否被取消
                try Task.checkCancellation()

                // 获取token和配置
                let token = try await AudioTokenService.shared.getAzureToken(cancelHandler: { dataTask in
                    self.currentTokenTask = dataTask
                })

                // 再次检查是否被取消（token请求后）
                try Task.checkCancellation()
                guard currentMessageId == messageId else { return }

                let config = try connectionPool.getConfiguration(token: token, region: AzureSpeechConfig.serviceRegion)

                // 设置语音
                config.speechSynthesisVoiceName = voice.rawValue

                // 获取或创建合成器
                let synthesizer = try getOrCreateSynthesizer(config: config)

                // 开始合成
                onStateChange(.synthesizing(progress: 0.0))

                // 使用后台队列进行合成，避免阻塞UI
                let audioData = try await Task.detached(priority: .userInitiated) {
                    // 检查取消状态
                    try Task.checkCancellation()

                    let result = try synthesizer.speakText(text)

                    if result.reason == .synthesizingAudioCompleted,
                       let audioData = result.audioData
                    {
                        return audioData
                    } else {
                        throw AzureSpeechSDKError.synthesisError("Synthesis failed: \(result.reason)")
                    }
                }.value

                // 最终检查
                try Task.checkCancellation()
                guard currentMessageId == messageId else { return }

                // 缓存音频
                ttsCache.cacheAudio(audioData, forText: text, voice: voice)

                // 播放音频
                try await playAudioData(
                    messageId: messageId,
                    audioData: audioData,
                    onStateChange: onStateChange
                )
            } catch is CancellationError {
                // 任务被取消，静默处理
                onStateChange(.idle)
            } catch {
                throw error
            }
        }

        currentTask = task
        try await task.value
    }

    /// 停止播放
    func stopPlayback(messageId: String) async {
        guard currentMessageId == messageId else { return }

        // 取消当前任务
        currentTask?.cancel()

        // 取消token请求
        currentTokenTask?.cancel()
        currentTokenTask = nil

        currentPlayer?.stop()
        currentPlayer = nil
        currentMessageId = nil

        // 延迟停用音频会话
        audioSessionManager.deactivateAfterDelay()
    }

    func cleanup() async {
        currentTask?.cancel()
        currentTask = nil

        currentTokenTask?.cancel()
        currentTokenTask = nil

        if let player = currentPlayer {
            player.stop()
            currentPlayer = nil
        }

        currentMessageId = nil

        playbackContinuation?.resume()
        playbackContinuation = nil

        synthesizerPool.removeAll()

        audioSessionManager.deactivateAfterDelay()
    }

    // MARK: - 私有方法

    private func getOrCreateSynthesizer(config: SPXSpeechConfiguration) throws -> SPXSpeechSynthesizer {
        if let synthesizer = synthesizerPool.popLast() {
            return synthesizer
        }

        return try SPXSpeechSynthesizer(speechConfiguration: config, audioConfiguration: nil)
    }

    private func returnSynthesizerToPool(_ synthesizer: SPXSpeechSynthesizer) {
        guard synthesizerPool.count < maxPoolSize else { return }
        synthesizerPool.append(synthesizer)
    }

    private func playFromCache(
        messageId: String,
        audioData: Data,
        onStateChange: @escaping (TTSDisplayState) -> Void
    ) async throws {
        await Task.detached(priority: .userInitiated) {
            for _ in stride(from: 0.0, through: 1.0, by: 0.2) {
                await MainActor.run {
                    onStateChange(.preparingCached)
                }
                try? await Task.sleep(nanoseconds: 20_000_000) // 20ms
            }
        }.value

        try await playAudioData(messageId: messageId, audioData: audioData, onStateChange: onStateChange)
    }

    private func playAudioData(
        messageId: String,
        audioData: Data,
        onStateChange: @escaping (TTSDisplayState) -> Void
    ) async throws {
        // 配置音频会话
        try await audioSessionManager.configureForMode(.playback)

        // 创建播放器
        let player = try AVAudioPlayer(data: audioData)
        player.delegate = self
        player.prepareToPlay()

        // 更新状态
        currentPlayer = player
        currentMessageId = messageId
        onStateChange(.playing)

        // 开始播放
        guard player.play() else {
            throw AzureSpeechSDKError.synthesisError("Failed to start playback")
        }

        // 等待播放完成或被中断
        await withCheckedContinuation { continuation in
            self.playbackContinuation = continuation
        }

        // 播放完成后清理
        if currentMessageId == messageId {
            onStateChange(.idle)
        }
    }

    private var playbackContinuation: CheckedContinuation<Void, Never>?
}

// MARK: - AVAudioPlayerDelegate

extension TextToSpeechService: AVAudioPlayerDelegate {
    nonisolated func audioPlayerDidFinishPlaying(_ player: AVAudioPlayer, successfully flag: Bool) {
        Task { @MainActor in
            self.playbackContinuation?.resume()
            self.playbackContinuation = nil

            if flag {
                print("[TTS] ✅ Audio playback finished successfully")
            } else {
                print("[TTS] ⚠️ Audio playback finished with error")
            }
        }
    }

    nonisolated func audioPlayerDecodeErrorDidOccur(_ player: AVAudioPlayer, error: Error?) {
        Task { @MainActor in
            print("[TTS] ❌ Audio decode error: \(error?.localizedDescription ?? "unknown")")
            self.playbackContinuation?.resume()
            self.playbackContinuation = nil
        }
    }
}
