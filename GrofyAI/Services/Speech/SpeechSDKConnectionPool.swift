import AVFoundation
import Foundation
import MicrosoftCognitiveServicesSpeech

// MARK: - Speech SDK连接池管理器

/// 管理Speech SDK配置和连接的复用
@MainActor
final class SpeechSDKConnectionPool {
    private var configurationCache: SPXSpeechConfiguration?
    private var configurationExpiresAt: Date?
    private let configurationLifetime: TimeInterval = 3600 // 1小时

    /// 获取或创建Speech配置（带缓存）
    func getConfiguration(token: String, region: String) throws -> SPXSpeechConfiguration {
        // 检查缓存是否有效
        if let config = configurationCache,
           let expiresAt = configurationExpiresAt,
           expiresAt > Date()
        {
            return config
        }

        // 创建新配置
        let config = try SPXSpeechConfiguration(authorizationToken: token, region: region)

        // 设置语言
        config.speechRecognitionLanguage = "zh-CN"

        // 缓存配置
        configurationCache = config
        configurationExpiresAt = Date().addingTimeInterval(configurationLifetime)

        return config
    }

    func cleanup() {
        configurationCache = nil
        configurationExpiresAt = nil
    }
}

// MARK: - 音频会话管理器

@MainActor
final class AudioSessionManager {
    private var currentMode: AudioSessionMode = .idle
    private var isConfigured = false
    private let sessionQueue = DispatchQueue(label: "com.grofyai.audiosession", qos: .userInitiated)

    /// 配置音频会话（带智能缓存）
    func configureForMode(_ mode: AudioSessionMode) async throws {
        // 如果模式相同且已配置，直接返回
        guard currentMode != mode || !isConfigured else { return }

        try await withCheckedThrowingContinuation { (continuation: CheckedContinuation<Void, Error>) in
            sessionQueue.async { [weak self] in
                do {
                    let session = AVAudioSession.sharedInstance()

                    switch mode {
                    case .recording:
                        try session.setCategory(
                            .playAndRecord,
                            mode: .measurement,
                            options: [.defaultToSpeaker, .allowBluetooth]
                        )
                    case .playback:
                        try session.setCategory(
                            .playback,
                            mode: .spokenAudio,
                            options: [.mixWithOthers, .duckOthers]
                        )
                    case .idle:
                        Task { @MainActor in
                            self?.currentMode = mode
                            self?.isConfigured = true
                        }
                        continuation.resume()
                        return
                    }

                    try session.setActive(true, options: .notifyOthersOnDeactivation)

                    Task { @MainActor in
                        self?.currentMode = mode
                        self?.isConfigured = true
                    }
                    continuation.resume()

                } catch {
                    continuation.resume(throwing: error)
                }
            }
        }
    }

    /// 延迟停用音频会话
    func deactivateAfterDelay() {
        sessionQueue.asyncAfter(deadline: .now() + 2.0) { [weak self] in
            Task { @MainActor [weak self] in
                guard self?.currentMode == .idle else { return }

                do {
                    try AVAudioSession.sharedInstance().setActive(false, options: .notifyOthersOnDeactivation)
                    self?.isConfigured = false
                } catch {
                    print("Failed to deactivate audio session: \(error)")
                }
            }
        }
    }
}
