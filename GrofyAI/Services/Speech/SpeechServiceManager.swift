import Combine
import SwiftUI

// MARK: - 全局语音服务管理器

@MainActor
class SpeechServiceManager: ObservableObject {
    static let shared = SpeechServiceManager()

    private let azureSpeechService = AzureSpeechSDKService()

    // MARK: - TTS设置和状态

    @AppStorage("tts_enabled") var isTTSEnabled = true
    @AppStorage("tts_auto_play") var autoPlayEnabled = false
    private let defaultVoice: SpeechVoice = .xiaoxiaoMultilingual

    // 发布的TTS状态
    @Published var currentPlayingText = ""
    @Published var isPlaying = false
    @Published var isSynthesizing = false
    @Published var ttsError: String?

    private var cancellables = Set<AnyCancellable>()

    private init() {
        setupBindings()
        configureInitialSettings()
    }

    private func setupBindings() {
        azureSpeechService.$isPlaying
            .assign(to: \.isPlaying, on: self)
            .store(in: &cancellables)

        azureSpeechService.$isSynthesizing
            .assign(to: \.isSynthesizing, on: self)
            .store(in: &cancellables)

        azureSpeechService.$currentPlayingText
            .assign(to: \.currentPlayingText, on: self)
            .store(in: &cancellables)

        azureSpeechService.$state
            .map { state in
                if case .error(let message) = state {
                    return message
                }
                return nil
            }
            .assign(to: \.ttsError, on: self)
            .store(in: &cancellables)
    }

    private func configureInitialSettings() {
        azureSpeechService.updateSynthesisVoice(defaultVoice)
    }

    /// 获取语音服务实例（用于STT和TTS）
    func getSpeechService() -> AzureSpeechSDKService {
        return azureSpeechService
    }

    /// 播放文本
    func playTTS(_ text: String) async throws {
        guard isTTSEnabled else {
            return
        }

        guard !text.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            throw AzureSpeechSDKError.synthesisError("文本内容为空")
        }

        try await azureSpeechService.synthesizeAndPlay(text, voice: defaultVoice)
    }

    /// 检查是否正在播放指定文本
    func isPlayingText(_ text: String) -> Bool {
        return azureSpeechService.isPlayingText(text)
    }

    // MARK: - 状态查询

    var ttsDisplayState: TTSDisplayState {
        azureSpeechService.ttsDisplayState
    }

    /// 清理所有语音相关资源和状态
    func cleanupAllVoiceResources() {
        Task {
            // 停止所有语音操作
            await azureSpeechService.stopContinuousRecognition()
            await azureSpeechService.stopPlayback()

            await MainActor.run {
                // 重置状态
                currentPlayingText = ""
                isPlaying = false
                isSynthesizing = false
                ttsError = nil
            }
        }
    }
}
