import Foundation

@MainActor
final class AudioTokenService {
    static let shared = AudioTokenService()

    private init() {}

    /// 获取Azure JWT Token
    func getAzureToken(cancelHandler: ((URLSessionDataTask) -> Void)? = nil) async throws -> String {
        let maxRetries = 3
        let baseDelay: TimeInterval = 1.0

        for attempt in 1...maxRetries {
            do {
                let rawToken = try await requestTokenFromAPI(cancelHandler: cancelHandler)
                let cleanToken = cleanTokenString(rawToken)
                return cleanToken

            } catch {
                if attempt == maxRetries {
                    print("❌ JWT Token获取失败: \(error.localizedDescription)")
                    throw AudioTokenError.tokenRequestFailed(error.localizedDescription)
                }

                let delay = baseDelay * Double(attempt)
                try await Task.sleep(nanoseconds: UInt64(delay * 1_000_000_000))
            }
        }

        throw AudioTokenError.tokenRequestFailed("重试次数已达上限")
    }

    /// 请求token API
    private func requestTokenFromAPI(cancelHandler: ((URLSessionDataTask) -> Void)? = nil) async throws -> String {
        guard let baseURL = getBaseURL(),
              let url = URL(string: "\(baseURL)/kapi/audio/get_azure_token")
        else {
            throw AudioTokenError.invalidURL
        }

        var request = URLRequest(url: url)
        request.httpMethod = "GET"

        if let token = getAuthToken() {
            request.setValue("Bearer \(token)", forHTTPHeaderField: "Authorization")
        }

        request.setValue("application/json", forHTTPHeaderField: "Content-Type")

        return try await withCheckedThrowingContinuation { continuation in
            let dataTask = URLSession.shared.dataTask(with: request) { data, response, error in
                if let error {
                    continuation.resume(throwing: error)
                    return
                }

                guard let httpResponse = response as? HTTPURLResponse else {
                    continuation.resume(throwing: AudioTokenError.invalidResponse)
                    return
                }

                guard httpResponse.statusCode == 200 else {
                    continuation.resume(throwing: AudioTokenError.httpError(httpResponse.statusCode))
                    return
                }

                guard let data,
                      let tokenString = String(data: data, encoding: .utf8)
                else {
                    continuation.resume(throwing: AudioTokenError.invalidResponse)
                    return
                }

                continuation.resume(returning: tokenString)
            }

            cancelHandler?(dataTask)

            dataTask.resume()
        }
    }

    private func cleanTokenString(_ rawToken: String) -> String {
        var cleaned = rawToken.trimmingCharacters(in: .whitespacesAndNewlines)

        // 去除外层双引号
        if cleaned.hasPrefix("\""), cleaned.hasSuffix("\""), cleaned.count > 1 {
            cleaned = String(cleaned.dropFirst().dropLast())
        }

        return cleaned.trimmingCharacters(in: .whitespacesAndNewlines)
    }

    private func getBaseURL() -> String? {
        return AppConfig.API.baseURL
    }

    private func getAuthToken() -> String? {
        return AuthStore.shared.getAccessToken()
    }
}

// MARK: - JWT Token错误类型

enum AudioTokenError: LocalizedError {
    case tokenRequestFailed(String)
    case invalidURL
    case invalidResponse
    case httpError(Int)

    var errorDescription: String? {
        switch self {
        case .tokenRequestFailed(let message):
            return "JWT Token获取失败: \(message)"
        case .invalidURL:
            return "无效的URL"
        case .invalidResponse:
            return "无效的响应"
        case .httpError(let code):
            return "HTTP错误: \(code)"
        }
    }
}
