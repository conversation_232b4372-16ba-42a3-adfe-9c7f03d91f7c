import Foundation

final class VideoAiService {
    
    //MARK: 万象 Wanx
    func wanx_t2vVideo(req:Wanx_t2v_VideoReq) async throws -> Int {
        let res: Int = try await Http.shared.POST(VideoAiApi.wanx_t2v.api_path, body: req.toDictionary())
        return res
    }
    
    func wanx_i2vVideo(req:Wanx_i2v_VideoReq) async throws -> Int {
        let res: Int = try await Http.shared.POST(VideoAiApi.wanx_i2v.api_path, body: req.toDictionary())
        return res
    }
    
    //MARK: 可灵 Kling
    func kling_t2vVideo(req:Kling_t2v_VideoReq) async throws -> Int {
        let res: Int = try await Http.shared.POST(VideoAiApi.kling_t2v.api_path, body: req.toDictionary())
        return res
    }
    
    func kling_i2vVideo(req: Kling_i2v_VideoReq) async throws -> Int {
        let res: Int = try await Http.shared.POST(VideoAiApi.kling_i2v.api_path, body: req.toDictionary())
        return res
    }
    
    //MARK: 海螺 Minimax
    func minimax_t2vVideo(req:Minimax_t2v_VideoReq) async throws -> Int {
        let res: Int = try await Http.shared.POST(VideoAiApi.minimax_t2v.api_path, body: req.toDictionary())
        return res
    }
    
    func minimax_i2vVideo(req:Minimax_i2v_VideoReq) async throws -> Int {
        let res: Int = try await Http.shared.POST(VideoAiApi.minimax_i2v.api_path, body: req.toDictionary())
        return res
    }
    
    //MARK: Pixverse
    func pixverse_t2vVideo(req:Pixverse_t2v_VideoReq) async throws -> Int {
        let res: Int = try await Http.shared.POST(VideoAiApi.pixverse_t2v.api_path, body: req.toDictionary())
        return res
    }
    
    func pixverse_i2vVideo(req:Pixverse_i2v_VideoReq) async throws -> Int {
        let res: Int = try await Http.shared.POST(VideoAiApi.pixverse_i2v.api_path, body: req.toDictionary())
        return res
    }
    
    //MARK: Runway
    func runway_i2vVideo(req:Runway_i2v_VideoReq) async throws -> Int {
        let res: Int = try await Http.shared.POST(VideoAiApi.runway_i2v.api_path, body: req.toDictionary())
        return res
    }
    
    //MARK: 特效视频 Effect_video
    func effectVideo(req:Effect_video_VideoReq) async throws -> Int {
        let res: Int = try await Http.shared.POST(VideoAiApi.effect_video.api_path, body: req.toDictionary())
        return res
    }
}
