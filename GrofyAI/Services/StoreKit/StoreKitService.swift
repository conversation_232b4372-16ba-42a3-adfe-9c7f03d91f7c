import Alamofire
import Foundation
import StoreKit
import SwiftUI

// MARK: - 调试日志级别

enum StoreKitLogLevel {
    case verbose // 完整的原始数据
    case info // 重要信息和摘要
    case error // 错误信息
    case request // 请求相关
    case response // 响应相关
    case transaction // 交易相关
    case product // 产品相关
    case purchase // 购买相关
    case verification // 验证相关
}

// MARK: - 调试日志分类

enum StoreKitLogCategory {
    case initialization // 初始化相关
    case productLoading // 产品加载
    case purchase // 购买流程
    case verification // 验证流程
    case transaction // 交易监听
    case backend // 后端API交互
    case entitlement // 权益状态
    case restoration // 恢复购买
}

// MARK: - 调试日志管理器

class StoreKitLogger {
    static let shared = StoreKitLogger()

    private let logLevel: StoreKitLogLevel = .verbose
    private let dateFormatter: DateFormatter
    private let timeFormatter: DateFormatter

    private init() {
        dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd HH:mm:ss.SSS"

        timeFormatter = DateFormatter()
        timeFormatter.dateFormat = "HH:mm:ss.SSS"
    }

    // MARK: - 主要日志方法

    func logRequest(_ category: StoreKitLogCategory, title: String, details: String = "", data: Any? = nil) {
        logWithFormat(
            icon: "📤",
            prefix: "[REQUEST]",
            category: category,
            title: title,
            details: details,
            data: data
        )
    }

    func logResponse(_ category: StoreKitLogCategory, title: String, details: String = "", data: Any? = nil) {
        logWithFormat(
            icon: "📥",
            prefix: "[RESPONSE]",
            category: category,
            title: title,
            details: details,
            data: data
        )
    }

    func logInfo(_ category: StoreKitLogCategory, title: String, details: String = "", data: Any? = nil) {
        logWithFormat(
            icon: "ℹ️",
            prefix: "[INFO]",
            category: category,
            title: title,
            details: details,
            data: data
        )
    }

    func logSuccess(_ category: StoreKitLogCategory, title: String, details: String = "", data: Any? = nil) {
        logWithFormat(
            icon: "✅",
            prefix: "[SUCCESS]",
            category: category,
            title: title,
            details: details,
            data: data
        )
    }

    func logError(_ category: StoreKitLogCategory, title: String, error: Error? = nil, details: String = "") {
        var errorDetails = details
        if let error {
            errorDetails += "\n错误信息: \(error.localizedDescription)"
            if let nsError = error as NSError? {
                errorDetails += "\n错误代码: \(nsError.code)"
                errorDetails += "\n错误域: \(nsError.domain)"
                if !nsError.userInfo.isEmpty {
                    errorDetails += "\n用户信息: \(nsError.userInfo)"
                }
            }
        }

        logWithFormat(
            icon: "❌",
            prefix: "[ERROR]",
            category: category,
            title: title,
            details: errorDetails,
            data: error
        )
    }

    func logTransaction(_ title: String, transaction: StoreKit.Transaction? = nil, details: String = "") {
        var transactionDetails = details
        if let transaction {
            transactionDetails += "\n交易详情:"
            transactionDetails += "\n├─ 交易ID: \(transaction.id)"
            transactionDetails += "\n├─ 原始交易ID: \(transaction.originalID)"
            transactionDetails += "\n├─ 产品ID: \(transaction.productID)"
            transactionDetails += "\n├─ 购买日期: \(dateFormatter.string(from: transaction.purchaseDate))"
            transactionDetails += "\n├─ 交易状态: \(transaction.revocationDate == nil ? "有效" : "已撤销")"
            if let appAccountToken = transaction.appAccountToken {
                transactionDetails += "\n├─ AppAccountToken: \(appAccountToken.uuidString)"
            }
            if let expirationDate = transaction.expirationDate {
                transactionDetails += "\n└─ 到期日期: \(dateFormatter.string(from: expirationDate))"
            } else {
                transactionDetails += "\n└─ 永久有效"
            }
        }

        logWithFormat(
            icon: "💳",
            prefix: "[TRANSACTION]",
            category: .transaction,
            title: title,
            details: transactionDetails,
            data: transaction
        )
    }

    // MARK: - 格式化日志输出

    private func logWithFormat(
        icon: String,
        prefix: String,
        category: StoreKitLogCategory,
        title: String,
        details: String = "",
        data: Any? = nil
    ) {
        let timestamp = timeFormatter.string(from: Date())
        let categoryName = categoryName(category)

        print("\n" + String(repeating: "=", count: 100))
        print("\(icon) \(prefix) [\(timestamp)] \(categoryName) - \(title)")
        print(String(repeating: "=", count: 100))

        if !details.isEmpty {
            print("📝 详细信息:")
            print(details)
        }

        if let data {
            print("\n📋 原始数据:")
            print(String(repeating: "-", count: 50))
            print(data)
            print(String(repeating: "-", count: 50))
        }

        print(String(repeating: "=", count: 100) + "\n")
    }

    // MARK: - 工具方法

    private func categoryName(_ category: StoreKitLogCategory) -> String {
        switch category {
        case .initialization: return "初始化"
        case .productLoading: return "产品加载"
        case .purchase: return "购买流程"
        case .verification: return "验证流程"
        case .transaction: return "交易监听"
        case .backend: return "后端API"
        case .entitlement: return "权益状态"
        case .restoration: return "恢复购买"
        }
    }

    // MARK: - 产品分析工具

    func logProductAnalysis(_ products: [Product]) {
        guard !products.isEmpty else {
            logInfo(.productLoading, title: "产品分析", details: "没有找到任何产品")
            return
        }

        let totalProducts = products.count
        let groupedProducts = Dictionary(grouping: products) { product in
            GrofyProduct(rawValue: product.id)?.membershipType ?? "未知"
        }

        var analysisDetails = "产品统计分析:"
        analysisDetails += "\n├─ 产品总数: \(totalProducts)"

        for (type, typeProducts) in groupedProducts.sorted(by: { $0.key < $1.key }) {
            analysisDetails += "\n├─ \(type) 类型: \(typeProducts.count)个产品"
            for product in typeProducts {
                if let grofyProduct = GrofyProduct(rawValue: product.id) {
                    let period = grofyProduct.isYearly ? "年度" : "月度"
                    analysisDetails += "\n│  ├─ \(period): \(product.displayPrice)"
                    analysisDetails += "\n│  │  ├─ 名称: \(product.displayName)"
                    analysisDetails += "\n│  │  ├─ ID: \(product.id)"
                    analysisDetails += "\n│  │  └─ 描述: \(product.description)"
                }
            }
        }

        logSuccess(.productLoading, title: "产品分析完成", details: analysisDetails, data: products)
    }
}

// MARK: - 内购产品标识符

enum GrofyProduct: CaseIterable {
    // Pro 会员
    case proMonthly
    case proYearly

    // Plus 会员
    case plusMonthly
    case plusYearly

    // Ultra 会员
    case ultraMonthly
    case ultraYearly

    var rawValue: String {
        switch self {
        case .proMonthly: return AppConfig.InAppPurchase.ProductIdentifier.proMonthly
        case .proYearly: return AppConfig.InAppPurchase.ProductIdentifier.proYearly
        case .plusMonthly: return AppConfig.InAppPurchase.ProductIdentifier.plusMonthly
        case .plusYearly: return AppConfig.InAppPurchase.ProductIdentifier.plusYearly
        case .ultraMonthly: return AppConfig.InAppPurchase.ProductIdentifier.ultraMonthly
        case .ultraYearly: return AppConfig.InAppPurchase.ProductIdentifier.ultraYearly
        }
    }

    // 从产品标识符创建枚举
    init?(rawValue: String) {
        switch rawValue {
        case AppConfig.InAppPurchase.ProductIdentifier.proMonthly:
            self = .proMonthly
        case AppConfig.InAppPurchase.ProductIdentifier.proYearly:
            self = .proYearly
        case AppConfig.InAppPurchase.ProductIdentifier.plusMonthly:
            self = .plusMonthly
        case AppConfig.InAppPurchase.ProductIdentifier.plusYearly:
            self = .plusYearly
        case AppConfig.InAppPurchase.ProductIdentifier.ultraMonthly:
            self = .ultraMonthly
        case AppConfig.InAppPurchase.ProductIdentifier.ultraYearly:
            self = .ultraYearly
        default:
            return nil
        }
    }

    var displayName: String {
        switch self {
        case .proMonthly: return "Pro 月度会员"
        case .proYearly: return "Pro 年度会员"
        case .plusMonthly: return "Plus 月度会员"
        case .plusYearly: return "Plus 年度会员"
        case .ultraMonthly: return "Ultra 月度会员"
        case .ultraYearly: return "Ultra 年度会员"
        }
    }

    var membershipType: String {
        switch self {
        case .proMonthly, .proYearly: return "PRO"
        case .plusMonthly, .plusYearly: return "PLUS"
        case .ultraMonthly, .ultraYearly: return "ULTRA"
        }
    }

    var isYearly: Bool {
        switch self {
        case .plusYearly, .proYearly, .ultraYearly: return true
        default: return false
        }
    }

    var isSubscription: Bool {
        return true // 所有产品都是订阅
    }
}

// MARK: - StoreKit 2 Manager

@MainActor
class StoreKitService: ObservableObject {
    static let shared = StoreKitService()

    @Published var products: [Product] = []
    @Published var purchasedProducts: Set<String> = []
    @Published var isLoading = false
    @Published var error: Error?

    private var updateListenerTask: Task<Void, Error>?
    private var hasLoadedProducts = false
    private var productLoadingTask: Task<Void, Never>?

    private init() {
        // 延迟启动交易监听
        Task {
            try? await Task.sleep(nanoseconds: 2_000_000_000) // 2秒延迟
            updateListenerTask = listenForTransactions()
        }

        // 延迟加载产品
        Task {
            try? await Task.sleep(nanoseconds: 3_000_000_000) // 3秒延迟
            await loadProductsInBackground()
        }
    }

    deinit {
        updateListenerTask?.cancel()
        productLoadingTask?.cancel()
    }

    // MARK: - 后台加载产品

    private func loadProductsInBackground() async {
        guard !hasLoadedProducts else { return }

        productLoadingTask = Task {
            // 在后台优先级执行
            await Task.detached(priority: .background) {
                await self.loadProducts()
            }.value
        }
    }

    // MARK: - 加载产品

    func loadProducts() async {
        guard !hasLoadedProducts else {
            StoreKitLogger.shared.logInfo(.productLoading, title: "跳过重复加载", details: "产品已加载，跳过本次请求")
            return
        }

        // 不设置isLoading，避免UI阻塞
        defer { hasLoadedProducts = true }

        // 获取所有产品ID
        let productIds = GrofyProduct.allCases.map(\.rawValue)

        let requestDetails = "产品加载请求参数:"
            + "\n├─ 请求产品数量: \(productIds.count)"
            + "\n├─ 产品ID列表:"

        var detailsWithProducts = requestDetails
        for (index, id) in productIds.enumerated() {
            let prefix = index == productIds.count - 1 ? "└─" : "├─"
            detailsWithProducts += "\n│  \(prefix) \(id)"
        }

        StoreKitLogger.shared.logRequest(
            .productLoading,
            title: "请求产品列表",
            details: detailsWithProducts,
            data: productIds
        )

        do {
            StoreKitLogger.shared.logInfo(
                .productLoading,
                title: "调用 Product.products(for:)",
                details: "正在从 App Store 加载产品信息..."
            )

            products = try await Product.products(for: productIds)
            hasLoadedProducts = true

            // 按照产品类型排序：Ultra -> Plus -> Pro，每个类型内月度在前
            products.sort { first, second in
                guard let firstProduct = GrofyProduct(rawValue: first.id),
                      let secondProduct = GrofyProduct(rawValue: second.id)
                else {
                    return false
                }

                // 先按会员类型排序
                if firstProduct.membershipType != secondProduct.membershipType {
                    let typeOrder = ["ULTRA": 0, "PLUS": 1, "PRO": 2]
                    return (typeOrder[firstProduct.membershipType] ?? 999) <
                        (typeOrder[secondProduct.membershipType] ?? 999)
                }

                // 同类型内，月度在前
                return !firstProduct.isYearly && secondProduct.isYearly
            }

            StoreKitLogger.shared.logInfo(
                .productLoading,
                title: "产品排序完成",
                details: "已按照 ULTRA->PLUS->PRO 的顺序排列，每类型内月度优先"
            )

            StoreKitLogger.shared.logProductAnalysis(products)

            await updateCustomerProductStatus()

        } catch {
            await MainActor.run {
                self.error = error
                StoreKitLogger.shared.logError(
                    .productLoading,
                    title: "产品加载失败",
                    error: error,
                    details: "无法从 App Store 加载产品信息"
                )
            }
        }
    }

    // MARK: - 购买产品

    func purchase(_ product: Product) async throws -> StoreKit.Transaction? {
        // 确保产品已加载
        if !hasLoadedProducts {
            await loadProducts()
        }

        isLoading = true
        defer { isLoading = false }

        guard let unionId = AuthStore.shared.user.unionId,
              let appAccountToken = UUID(uuidString: unionId)
        else {
            StoreKitLogger.shared.logError(
                .purchase,
                title: "用户未登录",
                details: "无法获取有效的用户 unionId，购买需要用户先登录"
            )
            throw StoreError.userNotAuthenticated
        }

        StoreKitLogger.shared.logRequest(
            .purchase,
            title: "开始购买流程",
            details: "用户启动产品购买:"
                + "\n├─ 产品ID: \(product.id)"
                + "\n├─ 产品名称: \(product.displayName)"
                + "\n├─ 产品价格: \(product.displayPrice)"
                + "\n├─ 产品描述: \(product.description)"
                + "\n├─ 用户ID: \(unionId)"
                + "\n└─ AppAccountToken: \(appAccountToken.uuidString)",
            data: product
        )

        do {
            // 创建购买选项
            let purchaseOption = Product.PurchaseOption.appAccountToken(appAccountToken)

            // 发起购买
            StoreKitLogger.shared.logInfo(
                .purchase,
                title: "调用 product.purchase()",
                details: "正在向 App Store 发起购买请求（包含 appAccountToken）..."
            )
            let result = try await product.purchase(options: [purchaseOption])

            StoreKitLogger.shared.logResponse(.purchase, title: "购买请求响应", details: "收到 App Store 购买结果", data: result)

            switch result {
            case .success(let verification):
                StoreKitLogger.shared.logSuccess(.purchase, title: "购买成功", details: "用户完成付款，开始验证交易")

                // 验证交易
                StoreKitLogger.shared.logRequest(
                    .verification,
                    title: "开始交易验证",
                    details: "正在验证交易的有效性...",
                    data: verification
                )
                let transaction = try checkVerified(verification)
                StoreKitLogger.shared.logSuccess(.verification, title: "交易验证成功", details: "交易签名验证通过")

                // 记录交易信息
                StoreKitLogger.shared.logTransaction("验证成功的交易", transaction: transaction)

                // 发送收据到后端验证
                await sendReceiptToBackend(transaction: transaction)

                // 完成交易
                StoreKitLogger.shared.logInfo(.transaction, title: "完成交易", details: "正在向 App Store 确认交易完成...")
                await transaction.finish()
                StoreKitLogger.shared.logSuccess(.transaction, title: "交易已完成", details: "交易已向 App Store 确认完成")

                // 更新购买状态
                await updateCustomerProductStatus()

                return transaction

            case .userCancelled:
                StoreKitLogger.shared.logInfo(.purchase, title: "用户取消购买", details: "用户主动取消了购买流程")
                return nil

            case .pending:
                StoreKitLogger.shared.logInfo(.purchase, title: "交易待处理", details: "交易需要进一步处理（可能需要家长批准等）")
                return nil

            @unknown default:
                StoreKitLogger.shared.logError(.purchase, title: "未知购买结果", details: "收到未知的购买结果类型")
                return nil
            }
        } catch {
            StoreKitLogger.shared.logError(.purchase, title: "购买流程失败", error: error, details: "购买过程中发生错误")
            throw error
        }
    }

    // MARK: - 恢复购买

    func restorePurchases() async {
        isLoading = true
        defer { isLoading = false }

        StoreKitLogger.shared.logRequest(.restoration, title: "开始恢复购买", details: "用户请求恢复之前的购买记录")

        do {
            // 同步所有交易
            StoreKitLogger.shared.logInfo(.restoration, title: "同步 App Store 交易", details: "正在从 App Store 同步最新的交易信息...")
            try await AppStore.sync()
            StoreKitLogger.shared.logSuccess(.restoration, title: "App Store 同步完成", details: "已成功同步 App Store 交易状态")

            // 获取最新的交易进行恢复
            var latestTransaction: StoreKit.Transaction?
            var transactionCount = 0

            StoreKitLogger.shared.logInfo(.restoration, title: "扫描用户权益", details: "正在扫描当前用户的所有有效权益...")

            for await result in StoreKit.Transaction.currentEntitlements {
                if case .verified(let transaction) = result {
                    transactionCount += 1
                    StoreKitLogger.shared.logInfo(
                        .restoration,
                        title: "发现有效交易 #\(transactionCount)",
                        details: "交易ID: \(transaction.id), 产品ID: \(transaction.productID)"
                    )

                    // 找到最新的交易
                    if latestTransaction == nil || transaction.purchaseDate > latestTransaction!.purchaseDate {
                        latestTransaction = transaction
                        StoreKitLogger.shared.logInfo(
                            .restoration,
                            title: "更新最新交易",
                            details: "将交易 \(transaction.id) 设为最新交易"
                        )
                    }
                }
            }

            let scanDetails = "权益扫描结果:"
                + "\n├─ 发现交易数量: \(transactionCount)"
                + "\n└─ 最新交易: \(latestTransaction?.id.description ?? "无")"

            StoreKitLogger.shared.logResponse(.restoration, title: "权益扫描完成", details: scanDetails)

            // 如果找到交易，发送恢复购买请求
            if let transaction = latestTransaction {
                StoreKitLogger.shared.logTransaction("准备恢复的交易", transaction: transaction)
                await sendRestorePurchaseRequest(transaction: transaction)
            } else {
                StoreKitLogger.shared.logInfo(.restoration, title: "没有找到可恢复的交易", details: "用户没有任何有效的购买记录")
            }

            // 更新购买状态
            await updateCustomerProductStatus()

        } catch {
            StoreKitLogger.shared.logError(.restoration, title: "恢复购买失败", error: error, details: "恢复购买过程中发生错误")
            self.error = error
        }
    }

    // MARK: - 发送恢复购买请求

    private func sendRestorePurchaseRequest(transaction: StoreKit.Transaction) async {
        let params: [String: Any] = [
            "transactionId": String(transaction.id),
            "originalTransactionId": String(transaction.originalID),
        ]

        let requestDetails = "恢复购买请求参数:"
            + "\n├─ 交易ID: \(transaction.id)"
            + "\n├─ 原始交易ID: \(transaction.originalID)"
            + "\n├─ 产品ID: \(transaction.productID)"
            + "\n└─ API端点: \(PaymentApi.restorePurchase.path)"

        StoreKitLogger.shared.logRequest(.backend, title: "发送恢复购买请求", details: requestDetails, data: params)

        do {
            let response: AuthResp = try await Http.shared.POST(
                PaymentApi.restorePurchase.path,
                body: params
            )

            let responseDetails = "恢复购买响应:"
                + "\n├─ 会员等级: \(response.memberLevel ?? "无")"
                + "\n├─ 普通积分: \(response.credits ?? 0)"
                + "\n├─ 高级积分: \(response.premiumCredits ?? 0)"
                + "\n└─ 到期时间: \(response.expireDate?.description ?? "无")"

            StoreKitLogger.shared.logSuccess(.backend, title: "恢复购买成功", details: responseDetails, data: response)
            await updateLocalUserStatus(response)

        } catch {
            StoreKitLogger.shared.logError(.backend, title: "恢复购买请求失败", error: error, details: "后端恢复购买API调用失败")
        }
    }

    // MARK: - 监听交易更新

    private func listenForTransactions() -> Task<Void, Error> {
        StoreKitLogger.shared.logInfo(.transaction, title: "初始化交易监听器", details: "正在设置实时交易更新监听器...")

        return Task.detached {
            StoreKitLogger.shared.logSuccess(
                .transaction,
                title: "交易监听器已启动",
                details: "开始监听 StoreKit.Transaction.updates 流"
            )

            var transactionIndex = 0

            // 监听交易更新
            for await result in StoreKit.Transaction.updates {
                transactionIndex += 1

                StoreKitLogger.shared.logInfo(
                    .transaction,
                    title: "收到交易更新 #\(transactionIndex)",
                    details: "交易监听器收到新的交易事件",
                    data: result
                )

                do {
                    let transaction = try await self.checkVerified(result)

                    StoreKitLogger.shared.logTransaction("监听器验证成功的交易", transaction: transaction)

                    // 发送到后端验证
                    await self.sendReceiptToBackend(transaction: transaction)

                    // 更新购买状态
                    await self.updateCustomerProductStatus()

                    // 完成交易
                    StoreKitLogger.shared.logInfo(.transaction, title: "监听器完成交易", details: "正在向 App Store 确认交易完成...")
                    await transaction.finish()
                    StoreKitLogger.shared.logSuccess(
                        .transaction,
                        title: "监听器交易已完成",
                        details: "交易 \(transaction.id) 已成功完成"
                    )

                } catch {
                    StoreKitLogger.shared.logError(
                        .transaction,
                        title: "监听器交易处理失败",
                        error: error,
                        details: "处理交易更新时出现错误"
                    )
                }
            }
        }
    }

    // MARK: - 更新客户产品状态

    private func updateCustomerProductStatus() async {
        StoreKitLogger.shared.logRequest(.entitlement, title: "开始扫描用户权益", details: "正在检查用户当前的所有有效订阅...")

        var purchased: Set<String> = []
        var purchaseDetails: [(productId: String, transaction: StoreKit.Transaction)] = []
        var entitlementCount = 0

        // 检查所有当前权益
        for await result in StoreKit.Transaction.currentEntitlements {
            if case .verified(let transaction) = result {
                entitlementCount += 1
                purchased.insert(transaction.productID)
                purchaseDetails.append((transaction.productID, transaction))

                StoreKitLogger.shared.logInfo(
                    .entitlement,
                    title: "发现有效权益 #\(entitlementCount)",
                    details: "产品ID: \(transaction.productID), 交易ID: \(transaction.id)"
                )
            }
        }

        purchasedProducts = purchased

        let statusSummary = "权益扫描结果:"
            + "\n├─ 总权益数量: \(entitlementCount)"
            + "\n├─ 有效产品数: \(purchased.count)"
            + "\n└─ 产品ID列表: \(Array(purchased).joined(separator: ", "))"

        StoreKitLogger.shared.logResponse(.entitlement, title: "权益扫描完成", details: statusSummary)

        // 打印详细购买状态
        if !purchaseDetails.isEmpty {
            var detailsLog = "详细购买信息:"

            // 按产品类型分组
            let groupedPurchases = purchaseDetails.sorted { first, second in
                guard let firstProduct = GrofyProduct(rawValue: first.productId),
                      let secondProduct = GrofyProduct(rawValue: second.productId)
                else {
                    return false
                }

                if firstProduct.membershipType != secondProduct.membershipType {
                    let typeOrder = ["ULTRA": 0, "PLUS": 1, "PRO": 2]
                    return (typeOrder[firstProduct.membershipType] ?? 999) <
                        (typeOrder[secondProduct.membershipType] ?? 999)
                }

                return !firstProduct.isYearly && secondProduct.isYearly
            }

            for (index, purchase) in groupedPurchases.enumerated() {
                let isLast = index == groupedPurchases.count - 1
                let prefix = isLast ? "└─" : "├─"

                if let product = products.first(where: { $0.id == purchase.productId }),
                   let grofyProduct = GrofyProduct(rawValue: purchase.productId)
                {
                    detailsLog += "\n\(prefix) \(product.displayName)"
                    let detailPrefix = isLast ? "   " : "│  "
                    detailsLog +=
                        "\n\(detailPrefix)├─ 类型: \(grofyProduct.membershipType) (\(grofyProduct.isYearly ? "年度" : "月度"))"
                    detailsLog += "\n\(detailPrefix)├─ 产品ID: \(purchase.productId)"
                    detailsLog += "\n\(detailPrefix)├─ 交易ID: \(purchase.transaction.id)"
                    detailsLog += "\n\(detailPrefix)├─ 购买日期: \(formatDate(purchase.transaction.purchaseDate))"

                    if let expirationDate = purchase.transaction.expirationDate {
                        let daysRemaining = Calendar.current.dateComponents([.day], from: Date(), to: expirationDate)
                            .day ?? 0
                        detailsLog += "\n\(detailPrefix)├─ 到期日期: \(formatDate(expirationDate))"
                        detailsLog += "\n\(detailPrefix)└─ 剩余天数: \(daysRemaining)天"
                    } else {
                        detailsLog += "\n\(detailPrefix)└─ 永久有效"
                    }
                }
            }

            StoreKitLogger.shared.logSuccess(.entitlement, title: "用户购买状态更新完成", details: detailsLog)
        } else {
            StoreKitLogger.shared.logInfo(.entitlement, title: "用户暂无购买记录", details: "用户当前没有任何有效的订阅或购买")
        }
    }

    // MARK: - 验证交易

    private func checkVerified<T>(_ result: VerificationResult<T>) throws -> T {
        switch result {
        case .unverified:
            throw StoreError.failedVerification
        case .verified(let safe):
            return safe
        }
    }

    // MARK: - 发送收据到后端

    private func sendReceiptToBackend(transaction: StoreKit.Transaction) async {
        // 构建请求参数 - 根据接口文档
        let params: [String: Any] = [
            "transactionId": String(transaction.id),
            "originalTransactionId": String(transaction.originalID),
            "productId": transaction.productID,
        ]

        let requestDetails = "订阅验证请求参数:"
            + "\n├─ 交易ID: \(transaction.id)"
            + "\n├─ 原始交易ID: \(transaction.originalID)"
            + "\n├─ 产品ID: \(transaction.productID)"
            + "\n├─ 购买日期: \(transaction.purchaseDate)"
            +
            (transaction
                .appAccountToken != nil ? "\n├─ AppAccountToken: \(transaction.appAccountToken!.uuidString)" : ""
            )
            + "\n└─ API端点: \(PaymentApi.verifySubscription.path)"

        StoreKitLogger.shared.logRequest(.backend, title: "发送订阅验证请求", details: requestDetails, data: params)

        // 使用项目中的 Http.shared 发送请求
        do {
            StoreKitLogger.shared.logInfo(.backend, title: "调用后端 API", details: "正在向后端验证订阅交易...")

            let response: AuthResp = try await Http.shared.POST(
                PaymentApi.verifySubscription.path,
                body: params
            )

            let responseDetails = "订阅验证响应:"
                + "\n├─ 会员等级: \(response.memberLevel ?? "无")"
                + "\n├─ 用户ID: \(response.unionId ?? "无")"
                + "\n├─ 普通积分: \(response.credits ?? 0)"
                + "\n├─ 高级积分: \(response.premiumCredits ?? 0)"
                + "\n├─ 到期时间: \(response.expireDate?.description ?? "无")"
                + "\n└─ 昵称: \(response.nickName ?? "无")"

            StoreKitLogger.shared.logSuccess(.backend, title: "订阅验证成功", details: responseDetails, data: response)

            // 更新本地用户状态
            await updateLocalUserStatus(response)

        } catch {
            StoreKitLogger.shared.logError(.backend, title: "订阅验证失败", error: error, details: "后端验证API调用失败")
        }
    }

    // MARK: - 工具方法

    private func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        formatter.locale = Locale(identifier: "zh_CN")
        return formatter.string(from: date)
    }

    // MARK: - 更新本地用户状态

    private func updateLocalUserStatus(_ authResp: AuthResp) async {
        let statusDetails = "用户状态更新:"
            + "\n├─ 会员等级: \(authResp.memberLevel ?? "未变更")"
            + "\n├─ 普通积分: \(authResp.credits ?? 0)"
            + "\n├─ 高级积分: \(authResp.premiumCredits ?? 0)"
            + "\n└─ 到期时间: \(authResp.expireDate?.description ?? "无")"

        StoreKitLogger.shared.logRequest(.entitlement, title: "更新本地用户状态", details: statusDetails, data: authResp)

        // 更新 AuthStore 中的用户状态
        await MainActor.run {
            AuthStore.shared.updateMemberStatus(
                memberLevel: authResp.memberLevel,
                credits: authResp.credits,
                premiumCredits: authResp.premiumCredits,
                expireDate: authResp.expireDate
            )

            StoreKitLogger.shared.logSuccess(
                .entitlement,
                title: "本地用户状态已更新",
                details: "用户状态已同步到 AuthStore，界面将自动更新"
            )
        }
    }

    // MARK: - 获取产品价格

    func getProductPrice(for productId: GrofyProduct) -> String? {
        guard let product = products.first(where: { $0.id == productId.rawValue }) else {
            return nil
        }
        return product.displayPrice
    }

    // MARK: - 检查产品是否已购买

    func isPurchased(_ productId: GrofyProduct) -> Bool {
        return purchasedProducts.contains(productId.rawValue)
    }

    // MARK: - 调试信息

    func printDebugInfo() {
        StoreKitLogger.shared.logInfo(
            .initialization,
            title: "StoreKit 管理器调试信息",
            details: "用户手动请求打印调试信息"
        )

        // 状态信息
        let statusDetails = "管理器状态:"
            + "\n├─ 产品已加载: \(hasLoadedProducts)"
            + "\n├─ 正在加载: \(isLoading)"
            + "\n└─ 错误状态: \(error != nil ? error!.localizedDescription : "无")"

        StoreKitLogger.shared.logInfo(.initialization, title: "管理器状态", details: statusDetails)

        // 使用新的产品分析方法
        StoreKitLogger.shared.logProductAnalysis(products)

        // 购买状态
        let purchaseStatusDetails = "购买状态统计:"
            + "\n├─ 已购买数量: \(purchasedProducts.count)"
            + "\n└─ 已购买产品: \(purchasedProducts.isEmpty ? "无" : Array(purchasedProducts).joined(separator: ", "))"

        StoreKitLogger.shared.logInfo(.entitlement, title: "购买状态统计", details: purchaseStatusDetails)

        // 配置信息
        let configDetails = "配置信息:"
            + "\n├─ 产品标识符数量: \(GrofyProduct.allCases.count)"
            + "\n└─ 支持的会员类型: ULTRA, PLUS, PRO"

        StoreKitLogger.shared.logInfo(.initialization, title: "配置信息", details: configDetails)
    }
}

// MARK: - 错误定义

enum StoreError: Error {
    case failedVerification
    case productNotFound
    case purchaseFailed
    case userNotAuthenticated
}

// MARK: - 响应模型

struct AuthResp: Codable {
    let accessToken: String?
    let memberLevel: String?
    let unionId: String?
    let expireDate: Int?
    let nickName: String?
    let headImage: String?
    let credits: Int?
    let premiumCredits: Int?
    let giftedCredits: Int?
    let giftedPremiumCredits: Int?
}
