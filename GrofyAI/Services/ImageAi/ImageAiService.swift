//
//  ImageAiService.swift
//  GrofyAI
//
//  Created by kissy on 2025/4/27.
//

import Foundation

class ImageAiService {
    //MARK: dalleImage请求
    func openAiImage(req:OpenAiImageReq) async throws -> Int {
        let res:Int = try await Http.shared.POST(ImageAiApi.openAi.api_path, body: req.toDictionary())
        return res
    }
    //MARK: fluxImage请求
    func fluxImage(req:FluxImageReq) async throws -> Int {
        let res: Int = try await Http.shared.POST(ImageAiApi.flux.api_path, body: req.toDictionary())
        return res
    }
    
    //MARK: ideogramImage请求
    func ideogramImage(req:IdeogramImageReq) async throws -> Int {
        let res: Int = try await Http.shared.POST(ImageAiApi.ideogram.api_path, body: req.toDictionary())
        return res
    }
    
    //MARK: midjourneyImage请求
    func midjourneyImage(req:MidjourneyImageReq) async throws -> Int {
        let res: Int = try await Http.shared.POST(ImageAiApi.midjourney.api_path, body: req.toDictionary())
        return res
    }
    
    //MARK: stableDiffusion_3_5Image请求
    func stableDiffusion_3_5Image(req:StableDiffusion3_5ImageReq) async throws -> Int {
        let res: Int = try await Http.shared.POST(ImageAiApi.stable_diffusion_3_5.api_path, body: req.toDictionary())
        return res
    }
}
