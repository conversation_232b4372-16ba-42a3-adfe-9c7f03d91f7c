import Foundation

class UserService {
    func userArtWork(req: UserArtWorkReq) async throws -> UserArtWorkRes {
        let res: UserArtWorkRes = try await Http.shared.GET(UserApi.artWork.path, params: req.toDictionary())
        return res
    }

    func userArtWorksPage(req: UserArtWorksPageReq) async throws -> UserArtWorksPageRes {
        let res: UserArtWorksPageRes = try await Http.shared.GETPAGE(
            UserApi.artWorksPage.path,
            params: req.toDictionary()
        )
        return res
    }

    func userDeleteArtWorks(ids: Set<ArtWorksCell.ID>) async throws -> OnlyCodeRes {
        let deleteReq = UserDeleteArtWorks(ids: Array(ids))
        let res: OnlyCodeRes = try await Http.shared.POST_SIMPLE(
            UserApi.deleteArtWorks.path,
            body: deleteReq.toDictionary()
        )

        if res.code != 200 {
            throw BusinessError(code: res.code, message: res.msg)
        }
        return res
    }

    func userRecommendedCategoriesList(req: RecommendedCategoriesListReq) async throws
        -> [RecommendedCategoriesListRes]
    {
        let res: [RecommendedCategoriesListRes] = try await Http.shared.GET(
            UserApi.recommendedCategoriesList.path,
            params: req.toDictionary()
        )
        return res
    }

    func userRecommendedCategoriesDetail(req: RecommendedCategoriesDetailReq) async throws -> [ArtWorksCell] {
        let res: [ArtWorksCell] = try await Http.shared.GETPAGE(
            UserApi.recommendedCategoriesDetail.path,
            params: req.toDictionary()
        )
        return res
    }
}
