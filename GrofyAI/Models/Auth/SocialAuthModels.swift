import Foundation

struct AppleAuthRequest: Codable {
    let terminal: String
    let locale: String
    let code: String
    let idToken: String
    let state: String?
    let user: AppleUserInfo?

    enum CodingKeys: String, CodingKey {
        case terminal
        case locale
        case code
        case idToken = "id_token"
        case state
        case user
    }
}

struct AppleUserInfo: Codable {
    let name: AppleUserName?
    let email: String?
}

struct AppleUserName: Codable {
    let firstName: String?
    let lastName: String?

    enum CodingKeys: String, CodingKey {
        case firstName = "firstName"
        case lastName = "lastName"
    }
}

struct GoogleAuthRequest: Codable {
    let token: String
    let terminal: String
    let locale: String
}

struct SocialAuthResponse: Codable {
    let accessToken: String?
    let refreshToken: String?
    let memberLevel: String?
    let unionId: String?
    let expireDate: Int?

    enum CodingKeys: String, CodingKey {
        case accessToken = "access_token"
        case refreshToken = "refresh_token"
        case memberLevel = "member_level"
        case unionId = "union_id"
        case expireDate = "expire_date"
    }
}

enum SocialAuthError: Error, LocalizedError {
    case appleSignInFailed(String)
    case googleSignInFailed(String)
    case authorizationCancelled
    case networkError(String)
    case invalidResponse
    case missingCredentials

    var errorDescription: String? {
        switch self {
        case .appleSignInFailed(let message):
            return "Apple登录失败: \(message)"
        case .googleSignInFailed(let message):
            return "Google登录失败: \(message)"
        case .authorizationCancelled:
            return "用户取消了授权"
        case .networkError(let message):
            return "网络错误: \(message)"
        case .invalidResponse:
            return "服务器响应无效"
        case .missingCredentials:
            return "缺少必要的认证信息"
        }
    }
}
