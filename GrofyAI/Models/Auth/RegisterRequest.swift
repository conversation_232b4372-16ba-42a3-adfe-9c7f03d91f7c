import Foundation

struct RegisterRequest: Codable {
    var email: String
    var password: String
    var confirmPassword: String
    var verificationCode: String // 验证码

    init(email: String = "", password: String = "", confirmPassword: String = "", verificationCode: String = "") {
        self.email = email
        self.password = password
        self.confirmPassword = confirmPassword
        self.verificationCode = verificationCode
    }
}
