import Foundation

enum AuthenticationState {
    case notAuthenticated // 未认证
    case loading // 加载中
    case authenticated // 已认证
    case error(AuthError) // 错误状态
}

enum AuthenticationFlow {
    case login // 登录流程
    case register // 注册流程
}

enum AuthError: Error, LocalizedError, Equatable {
    // MARK: - 表单验证错误

    case invalidEmail // 无效邮箱
    case invalidPassword // 无效密码
    case passwordTooShort // 密码太短
    case agreementNotAccepted // 未同意用户协议

    // MARK: - 认证错误

    case invalidCredentials // 无效凭据
    case emailAlreadyExists // 邮箱已存在
    case emailVerificationRequired // 需要邮箱验证
    case invalidVerificationCode // 无效验证码
    case notAuthenticated // 未认证（用于业务逻辑检查）

    // MARK: - 系统错误

    case networkError // 网络错误
    case serverError(String) // 服务器错误
    case socialAuthError(String) // 社交认证错误
    case unknown // 未知错误

    var errorDescription: String? {
        switch self {
        // 表单验证错误
        case .invalidEmail:
            return "请输入有效的邮箱地址"
        case .invalidPassword:
            return "密码格式不正确"
        case .passwordTooShort:
            return "密码至少需要8个字符"
        case .agreementNotAccepted:
            return "请先阅读并同意用户协议和隐私协议"
        // 认证错误
        case .invalidCredentials:
            return "邮箱或密码错误"
        case .emailAlreadyExists:
            return "该邮箱已被注册"
        case .emailVerificationRequired:
            return "请先验证邮箱"
        case .invalidVerificationCode:
            return "验证码错误或已过期"
        case .notAuthenticated:
            return "请先登录以访问此功能"
        // 系统错误
        case .networkError:
            return "网络连接失败，请检查网络设置"
        case .serverError(let message):
            return message
        case .socialAuthError(let message):
            return "社交登录失败: \(message)"
        case .unknown:
            return "未知错误，请重试"
        }
    }
}

enum SocialAuthProvider {
    case google
    case apple

    var title: String {
        switch self {
        case .google:
            return "Google"
        case .apple:
            return "Apple"
        }
    }

    var iconName: String {
        switch self {
        case .google:
            return "IconAuthGoogle"
        case .apple:
            return "apple.logo"
        }
    }

    var isSystemIcon: Bool {
        switch self {
        case .google:
            return false
        case .apple:
            return true
        }
    }
}
