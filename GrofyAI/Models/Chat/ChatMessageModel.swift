import Foundation

// MARK: - 聊天消息模型

struct ChatMessageModel: Identifiable, Hashable {
    let id: UUID
    let content: String
    let isUser: Bool
    let type: MessageType
    let modelId: Int?

    let searchResults: [SearchResult]?
    let files: [MessageFile]?
    var images: [MessageImage]?
    let reasoningContent: String?

    let toolCallStatus: String?
    let isToolActive: Bool

    // 消息变体支持
    let variants: [MessageVariantModel]?
    let currentVariantIndex: Int

    let chatMode: ChatMode?

    let fileAnalysisResult: FileAnalysisResult?
    let selectedAnalysisTab: FileAnalysisTab?

    enum MessageType {
        case text
        case reasoning
        case searchResult
        case event
        case error
        case fileAnalysis
        case trialEnded
    }

    init(
        id: UUID = UUID(),
        content: String,
        isUser: Bool,
        type: MessageType = .text,
        modelId: Int? = nil,
        searchResults: [SearchResult]? = nil,
        files: [MessageFile]? = nil,
        images: [MessageImage]? = nil,
        reasoningContent: String? = nil,
        toolCallStatus: String? = nil,
        isToolActive: Bool = false,
        variants: [MessageVariantModel]? = nil,
        currentVariantIndex: Int = 0,
        chatMode: ChatMode? = nil,
        fileAnalysisResult: FileAnalysisResult? = nil,
        selectedAnalysisTab: FileAnalysisTab? = nil
    ) {
        self.id = id
        self.content = content
        self.isUser = isUser
        self.type = type
        self.modelId = modelId
        self.searchResults = searchResults
        self.files = files
        self.images = images
        self.reasoningContent = reasoningContent
        self.toolCallStatus = toolCallStatus
        self.isToolActive = isToolActive
        self.variants = variants
        self.currentVariantIndex = currentVariantIndex
        self.chatMode = chatMode
        self.fileAnalysisResult = fileAnalysisResult
        self.selectedAnalysisTab = selectedAnalysisTab
    }

    /// 获取所有附件（合并files和images）
    var allAttachments: [MessageFile] {
        var attachments: [MessageFile] = []

        if let files {
            attachments.append(contentsOf: files)
        }

        if let images {
            attachments.append(contentsOf: images.map { $0.toMessageFile() })
        }

        return attachments
    }

    /// 获取非图片类型的附件（用于文件附件展示）
    var nonImageAttachments: [MessageFile] {
        return files ?? []
    }

    /// 获取图片附件（用于图片展示）
    var imageAttachments: [MessageImage] {
        return images ?? []
    }

    /// 消息的展示模式
    enum DisplayMode {
        case text // 纯文本/Markdown内容
        case media // 纯媒体（图片）内容
        case mixed // 混合内容（文本+附件）
        case status // 状态消息（工具调用、系统提示等）
        case special // 特殊消息（文件分析、试用结束等）
    }

    /// 获取消息的展示模式
    var displayMode: DisplayMode {
        // 特殊消息类型
        if type == .fileAnalysis || type == .trialEnded {
            return .special
        }

        // 状态消息
        if type == .event || type == .error {
            return .status
        }

        // 纯图片消息
        if content.isEmpty &&
            reasoningContent?.isEmpty != false &&
            toolCallStatus?.isEmpty != false &&
            searchResults?.isEmpty != false &&
            nonImageAttachments.isEmpty &&
            !imageAttachments.isEmpty
        {
            return .media
        }

        // 有内容的消息
        if !content.isEmpty ||
            reasoningContent != nil ||
            searchResults != nil ||
            !nonImageAttachments.isEmpty
        {
            return imageAttachments.isEmpty ? .text : .mixed
        }

        return .text
    }

    /// 是否为纯媒体消息（不需要气泡背景）
    var isPureMediaMessage: Bool {
        return displayMode == .media
    }

    /// 是否有附加内容（思考内容、搜索结果、工具调用等）
    var hasSupplementaryContent: Bool {
        return reasoningContent != nil ||
            searchResults != nil ||
            toolCallStatus != nil ||
            !nonImageAttachments.isEmpty
    }
}
