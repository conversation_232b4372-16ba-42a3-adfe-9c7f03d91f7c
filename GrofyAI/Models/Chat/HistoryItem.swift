import Foundation

// MARK: - 历史记录项模型

struct HistoryItem: Identifiable, Codable, Equatable {
    let id: Int
    let ownerId: Int?
    let threadId: String
    let categoryId: Int?
    let knowledgeId: Int?
    let modelId: Int?
    let modelName: String?
    let modelIcon: String?
    let title: String?
    let graphName: String?
    let type: String?
    let terminalType: String?
    let createDate: String?
    let creationDay: String?

    enum CodingKeys: String, CodingKey {
        case id
        case ownerId
        case threadId
        case categoryId
        case knowledgeId
        case modelId
        case modelName
        case modelIcon
        case title
        case graphName
        case type
        case terminalType
        case createDate
        case creationDay
    }

    var safeModelName: String {
        return modelName ?? "未知模型"
    }

    var safeTitle: String {
        return title ?? "无标题对话"
    }

    var safeType: String {
        return type ?? "CHAT"
    }

    var creationDayString: String {
        if let creationDay, !creationDay.isEmpty {
            return creationDay
        }

        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd"
        return formatter.string(from: Date())
    }

    var formattedCreateTime: String {
        guard let createDate else { return "" }

        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd HH:mm:ss"

        if let date = formatter.date(from: createDate) {
            let displayFormatter = DateFormatter()
            displayFormatter.dateFormat = "HH:mm"
            return displayFormatter.string(from: date)
        }

        return ""
    }

    /// 格式化的日期显示
    var formattedDate: String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd"

        if let date = formatter.date(from: creationDayString) {
            let displayFormatter = DateFormatter()
            displayFormatter.dateFormat = "MM月dd日"

            if Calendar.current.isDateInToday(date) {
                return "今天"
            } else if Calendar.current.isDateInYesterday(date) {
                return "昨天"
            } else {
                return displayFormatter.string(from: date)
            }
        }

        return creationDayString
    }



    /// 安全的知识库ID
    var safeKnowledgeId: Int {
        return knowledgeId ?? 0
    }

    /// 安全的分类ID
    var safeCategoryId: Int {
        return categoryId ?? 0
    }

    /// 是否为RAG对话
    var isRagChat: Bool {
        return graphName == "ragbot_graph"
    }

    /// 是否为知识库对话
    var isKnowledgeChat: Bool {
        return graphName == "knowledge_graph"
    }

    /// 是否为文件对话（包括RAG和知识库对话）
    var isFileChat: Bool {
        return isRagChat || isKnowledgeChat
    }
}

// MARK: - 历史记录分页数据

struct HistoryPageData: Codable {
    let items: [String: [HistoryItem]]

    /// 将字典数据转换为按日期分组的数组
    var groupedItems: [(date: String, items: [HistoryItem])] {
        return items.map { (date: $0.key, items: $0.value) }
            .sorted { first, second in
                // 按日期倒序排列（最新的在前）
                first.date > second.date
            }
    }

    /// 获取所有历史记录项的平铺列表
    var allItems: [HistoryItem] {
        return items.values.flatMap(\.self)
            .sorted { $0.id > $1.id } // 按ID倒序排列
    }

    init(from decoder: Decoder) throws {
        let container = try decoder.singleValueContainer()

        // 尝试安全解码，如果失败则使用空字典
        do {
            items = try container.decode([String: [HistoryItem]].self)
        } catch {
            print("HistoryPageData 解码失败: \(error)")
            items = [:]
        }
    }

    func encode(to encoder: Encoder) throws {
        var container = encoder.singleValueContainer()
        try container.encode(items)
    }

    init() {
        items = [:]
    }

    init(items: [String: [HistoryItem]]?) {
        self.items = items ?? [:]
    }
}

// MARK: - 历史记录分页响应

typealias HistoryPageResponse = PageRes<HistoryPageData>
