import Foundation
import SwiftUI

// MARK: - 图片生成状态枚举

/// 图片生成状态
enum ImageGenerationState: Equatable, Hashable {
    case placeholder
    case partialImage(current: Int, total: Int)
    case finalizing
    case completed
}

// MARK: - 消息图片附件模型

struct MessageImage: Codable, Identifiable, Hashable {
    let fileId: String
    let fileName: String
    let fileUrl: String
    let localImage: UIImage?
    var generationState: ImageGenerationState?
    var id: String { fileId }
    var name: String { fileName }
    var url: String { fileUrl }
    var type: String { "image" }

    enum CodingKeys: String, CodingKey {
        case fileId = "file_id"
        case fileName = "file_name"
        case fileUrl = "file_url"
    }

    init(
        fileId: String,
        fileName: String,
        fileUrl: String,
        localImage: UIImage? = nil,
        generationState: ImageGenerationState? = nil
    ) {
        self.fileId = fileId
        self.fileName = fileName
        self.fileUrl = fileUrl
        self.localImage = localImage
        self.generationState = generationState
    }

    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        fileId = try container.decode(String.self, forKey: .fileId)
        fileName = try container.decode(String.self, forKey: .fileName)
        fileUrl = try container.decode(String.self, forKey: .fileUrl)

        localImage = nil
        generationState = nil
    }

    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(fileId, forKey: .fileId)
        try container.encode(fileName, forKey: .fileName)
        try container.encode(fileUrl, forKey: .fileUrl)
    }
}

// MARK: - 文件类型扩展

extension MessageImage {
    /// 获取文件图标名称
    var iconName: String {
        return "photo"
    }

    /// 获取文件类型的中文显示名称
    var displayName: String {
        return "图片"
    }

    /// 判断是否为占位符状态
    var isPlaceholder: Bool {
        return generationState == .placeholder
    }

    /// 判断是否为部分图片状态
    var isPartialImage: Bool {
        if case .partialImage = generationState {
            return true
        }
        return false
    }

    /// 获取进度信息
    var progressInfo: (current: Int, total: Int)? {
        if case .partialImage(let current, let total) = generationState {
            return (current, total)
        }
        return nil
    }

    /// 判断是否为最终化状态
    var isFinalizing: Bool {
        return generationState == .finalizing
    }

    /// 判断是否为完成状态
    var isCompleted: Bool {
        return generationState == .completed
    }

    /// 判断是否为生成相关状态
    var isGenerationRelated: Bool {
        return generationState != nil
    }

    /// 转换为MessageFile格式（用于UI兼容）
    func toMessageFile() -> MessageFile {
        return MessageFile(id: fileId, name: fileName, type: "image", url: fileUrl, localImage: localImage)
    }
}
