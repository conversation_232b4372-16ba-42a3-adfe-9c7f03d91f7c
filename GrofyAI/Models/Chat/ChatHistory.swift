import Foundation

// MARK: - 历史记录详情模型

/// 历史消息基础协议
protocol HistoryMessage: Codable {
    var id: String { get }
    var role: String { get }
    var content: String { get }
}

/// 历史记录中的用户消息
struct HistoryHumanMessage: HistoryMessage, Identifiable, Equatable {
    let id: String
    let role: String // "human"
    let content: String
    let files: [MessageFile]?

    init(id: String, content: String, files: [MessageFile]? = nil) {
        self.id = id
        role = "human"
        self.content = content
        self.files = files
    }
}

/// 历史记录中的AI消息
struct HistoryAIMessage: HistoryMessage, Identifiable, Equatable {
    let id: String
    let role: String // "ai"
    let content: String
    let modelId: Int

    private enum CodingKeys: String, CodingKey {
        case id
        case role
        case content
        case modelId = "model_id"
    }

    init(id: String, content: String, modelId: Int) {
        self.id = id
        role = "ai"
        self.content = content
        self.modelId = modelId
    }
}

/// 历史记录详情容器
enum HistoryMessageContainer: Codable {
    case human(HistoryHumanMessage)
    case aiGroup([HistoryAIMessage])

    init(from decoder: Decoder) throws {
        let container = try decoder.singleValueContainer()

        // 尝试解码为单个human消息
        if let humanMessage = try? container.decode(HistoryHumanMessage.self) {
            self = .human(humanMessage)
            return
        }

        // 尝试解码为AI消息数组
        if let aiMessages = try? container.decode([HistoryAIMessage].self) {
            self = .aiGroup(aiMessages)
            return
        }

        throw DecodingError.dataCorrupted(
            DecodingError.Context(
                codingPath: decoder.codingPath,
                debugDescription: "无法解码历史消息容器"
            )
        )
    }

    func encode(to encoder: Encoder) throws {
        var container = encoder.singleValueContainer()

        switch self {
        case .human(let message):
            try container.encode(message)
        case .aiGroup(let messages):
            try container.encode(messages)
        }
    }
}

// MARK: - 历史记录详情响应

/// 历史记录详情响应类型
typealias ChatHistoryDetailResponse = [HistoryMessageContainer]

extension HistoryMessageContainer {
    /// 获取消息ID（用于列表显示）
    var messageId: String {
        switch self {
        case .human(let message):
            return message.id
        case .aiGroup(let messages):
            return messages.first?.id ?? UUID().uuidString
        }
    }

    /// 是否为用户消息
    var isUserMessage: Bool {
        switch self {
        case .human:
            return true
        case .aiGroup:
            return false
        }
    }
}
