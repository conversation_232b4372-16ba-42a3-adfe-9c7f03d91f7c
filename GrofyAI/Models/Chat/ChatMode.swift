import Foundation

// MARK: - 聊天模式枚举

enum ChatMode: <PERSON>hable {
    case agent // AI对话
    case image // 图片生成对话
    case rag // RAG文档对话
    case vision // 图片识别对话

    var graphId: String {
        switch self {
        case .agent:
            return "chatbot_graph"
        case .image:
            return "chatimg_graph"
        case .rag:
            return "ragbot_graph"
        case .vision:
            return "vision_graph"
        }
    }
}
