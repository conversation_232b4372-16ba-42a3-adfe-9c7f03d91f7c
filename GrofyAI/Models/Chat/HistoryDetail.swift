import Foundation

// MARK: - 历史对话详情模型

/// 历史对话详情项（支持不规则数组结构）
/// 可以是单个用户消息对象，也可以是AI消息数组
enum HistoryDetailItem: Codable {
    case humanMessage(HistoryDetailHumanMessage)
    case aiMessageGroup([HistoryDetailAIGroupMember])

    init(from decoder: Decoder) throws {
        let container = try decoder.singleValueContainer()

        // 尝试解析为AI消息组数组（包含AI消息和工具消息）
        if let aiMessageGroup = try? container.decode([HistoryDetailAIGroupMember].self) {
            self = .aiMessageGroup(aiMessageGroup)
            return
        }

        // 尝试解析为用户消息对象
        if let humanMessage = try? container.decode(HistoryDetailHumanMessage.self) {
            self = .humanMessage(humanMessage)
            return
        }

        throw DecodingError.typeMismatch(
            HistoryDetailItem.self,
            DecodingError.Context(
                codingPath: decoder.codingPath,
                debugDescription: "无法解析为用户消息或AI消息组"
            )
        )
    }

    func encode(to encoder: Encoder) throws {
        var container = encoder.singleValueContainer()

        switch self {
        case .humanMessage(let message):
            try container.encode(message)
        case .aiMessageGroup(let messages):
            try container.encode(messages)
        }
    }
}

// MARK: - 历史详情中的用户消息

struct HistoryDetailHumanMessage: Codable, Identifiable, Equatable {
    let id: String
    let role: String // "human"
    let content: String
    let images: [MessageImage]?

    init(id: String, content: String, images: [MessageImage]? = nil) {
        self.id = id
        role = "human"
        self.content = content
        self.images = images
    }
}

// MARK: - 历史详情中的AI消息组成员（支持AI和工具消息）

enum HistoryDetailAIGroupMember: Codable, Identifiable, Equatable {
    case ai(HistoryDetailAIMessage)
    case tool(HistoryDetailToolMessage)

    var id: String {
        switch self {
        case .ai(let message):
            return message.id
        case .tool(let message):
            return message.id
        }
    }

    init(from decoder: Decoder) throws {
        let container = try decoder.singleValueContainer()

        // 先尝试解析为工具消息
        if let toolMessage = try? container.decode(HistoryDetailToolMessage.self) {
            self = .tool(toolMessage)
            return
        }

        // 再尝试解析为AI消息
        if let aiMessage = try? container.decode(HistoryDetailAIMessage.self) {
            self = .ai(aiMessage)
            return
        }

        throw DecodingError.typeMismatch(
            HistoryDetailAIGroupMember.self,
            DecodingError.Context(
                codingPath: decoder.codingPath,
                debugDescription: "无法解析为AI消息或工具消息"
            )
        )
    }

    func encode(to encoder: Encoder) throws {
        var container = encoder.singleValueContainer()

        switch self {
        case .ai(let message):
            try container.encode(message)
        case .tool(let message):
            try container.encode(message)
        }
    }
}

// MARK: - 历史详情中的AI消息

struct HistoryDetailAIMessage: Codable, Identifiable, Equatable {
    let id: String
    let modelId: Int
    let role: String // "ai"
    let content: String
    let thinkingContent: String?
    let imageUrl: String? // 图像生成的URL

    private enum CodingKeys: String, CodingKey {
        case id
        case role
        case content
        case modelId = "model_id"
        case thinkingContent = "thinking_content"
        case imageUrl = "image_url"
    }

    init(id: String, modelId: Int, content: String, thinkingContent: String? = nil, imageUrl: String? = nil) {
        self.id = id
        self.modelId = modelId
        role = "ai"
        self.content = content
        self.thinkingContent = thinkingContent
        self.imageUrl = imageUrl
    }
}

// MARK: - 历史详情中的工具消息

struct HistoryDetailToolMessage: Codable, Identifiable, Equatable {
    let id: String
    let role: String // "tool"
    let name: String // 工具名称，如 "deepsearch"
    let content: [SearchResult]

    private enum CodingKeys: String, CodingKey {
        case id
        case role
        case name
        case content
    }

    init(id: String, name: String, content: [SearchResult]) {
        self.id = id
        role = "tool"
        self.name = name
        self.content = content
    }
}

// MARK: - 便利扩展

extension HistoryDetailItem {
    /// 获取消息ID（用于列表显示）
    var messageId: String {
        switch self {
        case .humanMessage(let message):
            return message.id
        case .aiMessageGroup(let messages):
            // 找到第一个AI消息的ID
            for member in messages {
                if case .ai(let aiMessage) = member {
                    return aiMessage.id
                }
            }
            return UUID().uuidString
        }
    }

    /// 是否为用户消息
    var isUserMessage: Bool {
        switch self {
        case .humanMessage:
            return true
        case .aiMessageGroup:
            return false
        }
    }

    /// 获取消息内容（用于预览）
    var previewContent: String {
        switch self {
        case .humanMessage(let message):
            return message.content
        case .aiMessageGroup(let messages):
            // 获取第一个AI消息的内容
            for member in messages {
                if case .ai(let aiMessage) = member {
                    return aiMessage.content
                }
            }
            return ""
        }
    }
}

// MARK: - 转换方法

extension HistoryDetailItem {
    private func convertToUUID(_ idString: String) -> UUID {
        if let uuid = UUID(uuidString: idString) {
            return uuid
        }

        let hash = idString.hashValue
        let uuidString = String(
            format: "%08X-0000-0000-0000-%012X",
            abs(hash) & 0xFFFFFFFF,
            abs(hash) & 0xFFFFFFFFFFFF
        )
        return UUID(uuidString: uuidString) ?? UUID()
    }

    func toChatMessages(chatMode: ChatMode? = nil) -> [ChatMessageModel] {
        switch self {
        case .humanMessage(let humanMessage):
            return [ChatMessageModel(
                id: convertToUUID(humanMessage.id),
                content: humanMessage.content,
                isUser: true,
                images: humanMessage.images,
                chatMode: chatMode
            )]

        case .aiMessageGroup(let aiGroupMembers):
            guard !aiGroupMembers.isEmpty else { return [] }

            var messages: [ChatMessageModel] = []

            // 先提取搜索结果
            var searchResults: [SearchResult]?
            for member in aiGroupMembers {
                if case .tool(let toolMessage) = member,
                   toolMessage.name == "deepsearch"
                {
                    searchResults = toolMessage.content
                    break
                }
            }

            // 再处理AI消息
            let aiMessages = aiGroupMembers.compactMap { member -> HistoryDetailAIMessage? in
                if case .ai(let aiMessage) = member {
                    return aiMessage
                }
                return nil
            }

            guard let lastAI = aiMessages.last else { return [] }

            if chatMode == .image, lastAI.imageUrl != nil {
                let variants: [MessageVariantModel]? = aiMessages.count > 1 ? aiMessages.map { aiMessage in
                    MessageVariantModel(
                        id: aiMessage.id,
                        content: aiMessage.content,
                        modelId: aiMessage.modelId,
                        thinkingContent: aiMessage.thinkingContent,
                        searchResults: searchResults,
                        images: nil
                    )
                } : nil

                // 计算正确的变体索引：找到lastAI在aiMessages中的位置
                let currentVariantIndex = aiMessages.firstIndex(where: { $0.id == lastAI.id }) ?? 0

                // 先添加文字消息
                messages.append(ChatMessageModel(
                    id: convertToUUID(lastAI.id),
                    content: lastAI.content,
                    isUser: false,
                    modelId: lastAI.modelId,
                    searchResults: searchResults,
                    images: nil,
                    reasoningContent: lastAI.thinkingContent,
                    variants: variants,
                    currentVariantIndex: currentVariantIndex,
                    chatMode: chatMode
                ))

                let imageUrls = parseImageUrls(lastAI.imageUrl!)

                for (index, imageUrl) in imageUrls.enumerated() {
                    let messageImage = MessageImage(
                        fileId: UUID().uuidString,
                        fileName: "generated_image_\(index + 1).png",
                        fileUrl: imageUrl
                    )

                    let imageMessageId = convertToUUID("\(lastAI.id)-image-\(index)")

                    messages.append(ChatMessageModel(
                        id: imageMessageId,
                        content: "",
                        isUser: false,
                        modelId: lastAI.modelId,
                        images: [messageImage],
                        chatMode: chatMode
                    ))
                }
            } else {
                let variants: [MessageVariantModel]? = aiMessages.count > 1 ? aiMessages.map { aiMessage in
                    let variantImages: [MessageImage]? = aiMessage.imageUrl != nil ? [
                        MessageImage(
                            fileId: aiMessage.id,
                            fileName: "generated_image.png",
                            fileUrl: aiMessage.imageUrl!
                        )
                    ] : nil

                    return MessageVariantModel(
                        id: aiMessage.id,
                        content: aiMessage.content,
                        modelId: aiMessage.modelId,
                        thinkingContent: aiMessage.thinkingContent,
                        searchResults: searchResults,
                        images: variantImages
                    )
                } : nil

                // 计算正确的变体索引：找到lastAI在aiMessages中的位置
                let currentVariantIndex = aiMessages.firstIndex(where: { $0.id == lastAI.id }) ?? 0

                let images: [MessageImage]? = lastAI.imageUrl != nil ? [
                    MessageImage(
                        fileId: lastAI.id,
                        fileName: "generated_image.png",
                        fileUrl: lastAI.imageUrl!
                    )
                ] : nil

                messages.append(ChatMessageModel(
                    id: convertToUUID(lastAI.id),
                    content: lastAI.content,
                    isUser: false,
                    modelId: lastAI.modelId,
                    searchResults: searchResults,
                    images: images,
                    reasoningContent: lastAI.thinkingContent,
                    variants: variants,
                    currentVariantIndex: currentVariantIndex,
                    chatMode: chatMode
                ))
            }

            return messages
        }
    }

    private func parseImageUrls(_ imageUrl: String) -> [String] {
        if imageUrl.contains(",") {
            return imageUrl.components(separatedBy: ",")
                .map { $0.trimmingCharacters(in: .whitespacesAndNewlines) }
                .filter { !$0.isEmpty }
        } else {
            return [imageUrl]
        }
    }

    /// 转换为文件聊天消息格式（支持文件分析类型）
    func toFileChatMessage() -> ChatMessageModel? {
        switch self {
        case .humanMessage(let humanMessage):
            return ChatMessageModel(
                content: humanMessage.content,
                isUser: true,
                images: humanMessage.images,
                chatMode: .rag
            )

        case .aiMessageGroup(let aiGroupMembers):
            guard !aiGroupMembers.isEmpty else { return nil }

            // 先提取搜索结果
            var searchResults: [SearchResult]?
            for member in aiGroupMembers {
                if case .tool(let toolMessage) = member,
                   toolMessage.name == "deepsearch"
                {
                    searchResults = toolMessage.content
                    break
                }
            }

            // 再处理AI消息
            let aiMessages = aiGroupMembers.compactMap { member -> HistoryDetailAIMessage? in
                if case .ai(let aiMessage) = member {
                    return aiMessage
                }
                return nil
            }

            guard let firstAI = aiMessages.first else { return nil }

            if let analysisResult = parseFileAnalysisResult(from: firstAI.content) {
                return ChatMessageModel(
                    content: "",
                    isUser: false,
                    type: .fileAnalysis,
                    chatMode: .rag,
                    fileAnalysisResult: analysisResult,
                    selectedAnalysisTab: .summary
                )
            }

            let variants: [MessageVariantModel]? = aiMessages.count > 1 ? aiMessages.map { aiMessage in
                let variantImages: [MessageImage]? = aiMessage.imageUrl != nil ? [
                    MessageImage(
                        fileId: aiMessage.id,
                        fileName: "generated_image.png",
                        fileUrl: aiMessage.imageUrl!
                    )
                ] : nil

                return MessageVariantModel(
                    id: aiMessage.id,
                    content: aiMessage.content,
                    modelId: aiMessage.modelId,
                    thinkingContent: aiMessage.thinkingContent,
                    searchResults: searchResults,
                    images: variantImages
                )
            } : nil

            // 计算正确的变体索引：找到firstAI在aiMessages中的位置
            let currentVariantIndex = aiMessages.firstIndex(where: { $0.id == firstAI.id }) ?? 0

            let images: [MessageImage]? = firstAI.imageUrl != nil ? [
                MessageImage(
                    fileId: firstAI.id,
                    fileName: "generated_image.png",
                    fileUrl: firstAI.imageUrl!
                )
            ] : nil

            return ChatMessageModel(
                content: firstAI.content,
                isUser: false,
                modelId: firstAI.modelId,
                searchResults: searchResults,
                images: images,
                reasoningContent: firstAI.thinkingContent,
                variants: variants,
                currentVariantIndex: currentVariantIndex,
                chatMode: .rag
            )
        }
    }

    // MARK: - 文件分析关键字管理

    private static let analysisKeywords: [FileAnalysisTab: [String]] = [
        .summary: ["summary"],
        .keyPoints: ["key_points"],
        .outline: ["outline"],
        .preview: ["file_url"],
    ]

    /// 获取所有分析关键字
    private static var allAnalysisKeywords: [String] {
        return analysisKeywords.values.flatMap(\.self)
    }

    /// 获取指定分析类型的关键字
    private static func getKeywords(for tab: FileAnalysisTab) -> [String] {
        return analysisKeywords[tab] ?? []
    }

    /// 解析文件分析结果
    private func parseFileAnalysisResult(from content: String) -> FileAnalysisResult? {
        let trimmedContent = content.trimmingCharacters(in: .whitespacesAndNewlines)
        guard !trimmedContent.isEmpty else { return nil }

        if let jsonResult = tryParseAsJson(trimmedContent) {
            return jsonResult
        }

        if let textResult = tryParseAsText(trimmedContent) {
            return textResult
        }

        return nil
    }

    /// 尝试解析JSON格式的分析结果
    private func tryParseAsJson(_ content: String) -> FileAnalysisResult? {
        guard let data = content.data(using: .utf8) else { return nil }

        do {
            let analysisResult = try JSONDecoder().decode(FileAnalysisResult.self, from: data)
            return analysisResult.hasContent ? analysisResult : nil
        } catch {
            return nil
        }
    }

    /// 尝试解析文本格式的分析结果
    private func tryParseAsText(_ content: String) -> FileAnalysisResult? {
        let hasAnalysisKeywords = Self.allAnalysisKeywords.contains { keyword in
            content.localizedCaseInsensitiveContains(keyword)
        }

        guard hasAnalysisKeywords else { return nil }

        // 提取各个分析部分的内容
        let result = FileAnalysisResult(
            summary: extractSection(from: content, keywords: Self.getKeywords(for: .summary)),
            keyPoints: extractSection(from: content, keywords: Self.getKeywords(for: .keyPoints)),
            outline: extractSection(from: content, keywords: Self.getKeywords(for: .outline)),
            fileUrl: extractSection(from: content, keywords: Self.getKeywords(for: .preview))
        )

        return result.hasContent ? result : nil
    }

    /// 从内容中提取指定关键词的部分
    private func extractSection(from content: String, keywords: [String]) -> String? {
        for keyword in keywords {
            if let range = content.range(of: keyword, options: .caseInsensitive) {
                let startIndex = range.upperBound
                let remainingContent = String(content[startIndex...])

                let lines = remainingContent.components(separatedBy: .newlines)
                var extractedLines: [String] = []

                for line in lines {
                    let trimmedLine = line.trimmingCharacters(in: .whitespacesAndNewlines)
                    if trimmedLine.isEmpty { continue }

                    let hasOtherKeyword = Self.allAnalysisKeywords.contains { otherKeyword in
                        otherKeyword != keyword && trimmedLine.localizedCaseInsensitiveContains(otherKeyword)
                    }

                    if hasOtherKeyword { break }

                    extractedLines.append(trimmedLine)

                    if extractedLines.count >= 10 { break }
                }

                let result = extractedLines.joined(separator: "\n").trimmingCharacters(in: .whitespacesAndNewlines)
                return result.isEmpty ? nil : result
            }
        }
        return nil
    }

    func handleMessage() -> ChatMessageModel? {
        switch self {
        case .humanMessage(let humanMessage):
            return ChatMessageModel(
                content: humanMessage.content,
                isUser: true,
                images: humanMessage.images
            )

        case .aiMessageGroup(let aiGroupMembers):
            guard !aiGroupMembers.isEmpty else { return nil }

            // 先提取搜索结果
            var searchResults: [SearchResult]?
            for member in aiGroupMembers {
                if case .tool(let toolMessage) = member,
                   toolMessage.name == "deepsearch"
                {
                    searchResults = toolMessage.content
                    break
                }
            }

            // 再处理AI消息
            let aiMessages = aiGroupMembers.compactMap { member -> HistoryDetailAIMessage? in
                if case .ai(let aiMessage) = member {
                    return aiMessage
                }
                return nil
            }

            guard let firstAI = aiMessages.first else { return nil }

            // 如果有多个AI消息，转换为MessageVariant数组
            let variants: [MessageVariantModel]? = aiMessages.count > 1 ? aiMessages.map { aiMessage in
                let variantImages: [MessageImage]? = aiMessage.imageUrl != nil ? [
                    MessageImage(
                        fileId: aiMessage.id,
                        fileName: "generated_image.png",
                        fileUrl: aiMessage.imageUrl!
                    )
                ] : nil

                return MessageVariantModel(
                    id: aiMessage.id,
                    content: aiMessage.content,
                    modelId: aiMessage.modelId,
                    thinkingContent: aiMessage.thinkingContent,
                    searchResults: searchResults,
                    images: variantImages
                )
            } : nil

            // 计算正确的变体索引：找到firstAI在aiMessages中的位置
            let currentVariantIndex = aiMessages.firstIndex(where: { $0.id == firstAI.id }) ?? 0

            // 如果AI消息包含图片URL，转换为MessageImage数组
            let images: [MessageImage]? = firstAI.imageUrl != nil ? [
                MessageImage(
                    fileId: firstAI.id,
                    fileName: "generated_image.png",
                    fileUrl: firstAI.imageUrl!
                )
            ] : nil

            return ChatMessageModel(
                content: firstAI.content,
                isUser: false,
                modelId: firstAI.modelId,
                searchResults: searchResults,
                images: images,
                reasoningContent: firstAI.thinkingContent,
                variants: variants,
                currentVariantIndex: currentVariantIndex
            )
        }
    }
}
