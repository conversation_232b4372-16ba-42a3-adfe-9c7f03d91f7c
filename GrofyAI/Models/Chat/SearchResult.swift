import Foundation

// MARK: - 搜索结果模型

/// 深度搜索结果项
struct SearchResult: Codable, Identifiable, Hashable {
    let id: UUID
    let title: String
    let url: String
    let content: String?
    let score: Double?

    private enum CodingKeys: String, CodingKey {
        case title
        case url
        case content
        case score
    }

    init(id: UUID = UUID(), title: String, url: String, content: String? = nil, score: Double? = nil) {
        self.id = id
        self.title = title
        self.url = url
        self.content = content
        self.score = score
    }

    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        id = UUID()
        title = try container.decode(String.self, forKey: .title)
        url = try container.decode(String.self, forKey: .url)
        content = try container.decodeIfPresent(String.self, forKey: .content)
        score = try container.decodeIfPresent(Double.self, forKey: .score)
    }

    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(title, forKey: .title)
        try container.encode(url, forKey: .url)
        try container.encodeIfPresent(content, forKey: .content)
        try container.encodeIfPresent(score, forKey: .score)
    }
}
