import Foundation

// MARK: - 消息变体模型

struct MessageVariantModel: Identifiable, Hashable {
    let id: String
    let content: String
    let modelId: Int?
    let thinkingContent: String?
    let searchResults: [SearchResult]?
    let files: [MessageFile]?
    let images: [MessageImage]?

    init(
        id: String,
        content: String,
        modelId: Int? = nil,
        thinkingContent: String? = nil,
        searchResults: [SearchResult]? = nil,
        files: [MessageFile]? = nil,
        images: [MessageImage]? = nil
    ) {
        self.id = id
        self.content = content
        self.modelId = modelId
        self.thinkingContent = thinkingContent
        self.searchResults = searchResults
        self.files = files
        self.images = images
    }

    /// 获取所有附件（合并files和images）
    var allAttachments: [MessageFile] {
        var attachments: [MessageFile] = []

        if let files {
            attachments.append(contentsOf: files)
        }

        if let images {
            attachments.append(contentsOf: images.map { $0.toMessageFile() })
        }

        return attachments
    }

    /// 获取非图片类型的附件（用于文件附件展示）
    var nonImageAttachments: [MessageFile] {
        return files ?? []
    }

    /// 获取图片附件（用于图片展示）
    var imageAttachments: [MessageImage] {
        return images ?? []
    }
}
