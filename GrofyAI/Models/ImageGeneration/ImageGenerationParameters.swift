import Foundation

// MARK: - 图片生成参数模型

/// 图片生成参数配置
struct ImageGenerationParameters: Codable, Equatable {
    var size: ImageSize
    var quality: ImageQuality
    var format: ImageGenerationFormat
    var background: ImageBackground

    init(
        size: ImageSize = .default,
        quality: ImageQuality = .default,
        format: ImageGenerationFormat = .default,
        background: ImageBackground = .default
    ) {
        self.size = size
        self.quality = quality
        self.format = format
        self.background = background
    }
}

// MARK: - 图片尺寸

enum ImageSize: String, CaseIterable, Codable {
    case square = "1024x1024"
    case landscape = "1536x1024"
    case portrait = "1024x1536"

    static let `default` = ImageSize.square

    var displayName: String {
        switch self {
        case .square:
            return "1:1"
        case .landscape:
            return "16:9"
        case .portrait:
            return "9:16"
        }
    }

    var description: String {
        switch self {
        case .square:
            return "正方形"
        case .landscape:
            return "横向"
        case .portrait:
            return "纵向"
        }
    }
}

// MARK: - 图片质量

enum ImageQuality: String, CaseIterable, Codable {
    case low = "low"
    case medium = "medium"
    case high = "high"

    static let `default` = ImageQuality.medium

    var displayName: String {
        switch self {
        case .low:
            return "低"
        case .medium:
            return "中"
        case .high:
            return "高"
        }
    }

    var description: String {
        switch self {
        case .low:
            return "快速生成"
        case .medium:
            return "平衡质量"
        case .high:
            return "高质量"
        }
    }
}

// MARK: - 图片格式

enum ImageGenerationFormat: String, CaseIterable, Codable {
    case png = "png"
    case jpeg = "jpeg"
    case webp = "webp"

    static let `default` = ImageGenerationFormat.png

    var displayName: String {
        return rawValue.uppercased()
    }

    var description: String {
        switch self {
        case .png:
            return "无损压缩"
        case .jpeg:
            return "有损压缩"
        case .webp:
            return "现代格式"
        }
    }

    /// 获取对应的MIME类型
    var mimeType: String {
        switch self {
        case .png:
            return "image/png"
        case .jpeg:
            return "image/jpeg"
        case .webp:
            return "image/webp"
        }
    }
}

// MARK: - 图片背景

enum ImageBackground: String, CaseIterable, Codable {
    case opaque = "opaque"
    case transparent = "transparent"

    static let `default` = ImageBackground.opaque

    var displayName: String {
        switch self {
        case .opaque:
            return "不透明"
        case .transparent:
            return "透明"
        }
    }

    var description: String {
        switch self {
        case .opaque:
            return "实体背景"
        case .transparent:
            return "透明背景"
        }
    }
}

// MARK: - API请求参数

/// 图片生成API请求参数
struct ImageGenerationRequest: Codable {
    let threadId: String
    let uploadedImages: [UploadedImageReq]
    let parentId: String?
    let message: String
    let parameters: ImageGenerationParameters
    let isFirst: Bool

    init(
        threadId: String,
        uploadedImages: [UploadedImageReq] = [],
        parentId: String? = nil,
        message: String,
        parameters: ImageGenerationParameters = ImageGenerationParameters(),
        isFirst: Bool
    ) {
        self.threadId = threadId
        self.uploadedImages = uploadedImages
        self.parentId = parentId
        self.message = message
        self.parameters = parameters
        self.isFirst = isFirst
    }

    /// 转换为API参数字典
    func toAPIParameters() throws -> [String: Any] {
        var apiParameters: [String: Any] = [
            "thread_id": threadId,
            "messages": message,
            "is_first": isFirst,
            "size": parameters.size.rawValue,
            "quality": parameters.quality.rawValue,
            "format": parameters.format.rawValue,
            "background": parameters.background.rawValue,
        ]

        if !uploadedImages.isEmpty {
            apiParameters["uploaded_images"] = uploadedImages.map { image in
                [
                    "file_id": image.file_id,
                    "file_name": image.file_name,
                    "file_url": image.file_url,
                ]
            }
        }

        if let parentId {
            apiParameters["parent_id"] = parentId
        }

        return apiParameters
    }
}
