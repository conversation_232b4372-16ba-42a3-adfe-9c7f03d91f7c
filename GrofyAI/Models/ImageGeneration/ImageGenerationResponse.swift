import Foundation

// MARK: - 图片生成响应模型

/// 图片生成响应数据
enum ImageGenerationResponse: Codable {
    case status(ImageGenerationStatusResponse)
    case imageGeneration(ImageGenerationContentResponse)

    enum CodingKeys: String, CodingKey {
        case code
        case type
        case event
        case node
        case content
    }

    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        let type = try container.decode(String.self, forKey: .type)

        switch type {
        case "status":
            let statusResponse = try ImageGenerationStatusResponse(from: decoder)
            self = .status(statusResponse)
        case "tool_image_generation":
            let contentResponse = try ImageGenerationContentResponse(from: decoder)
            self = .imageGeneration(contentResponse)
        default:
            throw DecodingError.dataCorrupted(
                DecodingError.Context(
                    codingPath: decoder.codingPath,
                    debugDescription: "未知的响应类型: \(type)"
                )
            )
        }
    }

    func encode(to encoder: Encoder) throws {
        switch self {
        case .status(let response):
            try response.encode(to: encoder)
        case .imageGeneration(let response):
            try response.encode(to: encoder)
        }
    }
}

// MARK: - 状态响应

/// 图片生成状态响应
struct ImageGenerationStatusResponse: Codable {
    let code: Int
    let type: String
    let event: String
    let node: String
}

// MARK: - 图片生成内容响应

/// 图片生成内容响应
struct ImageGenerationContentResponse: Codable {
    let code: Int
    let type: String
    let content: ImageGenerationContent
}

/// 图片生成内容
struct ImageGenerationContent: Codable {
    let status: String
    let partialImageIndex: Int?
    let imageB64: String?
    let text: String?
    let imageUrl: String?

    enum CodingKeys: String, CodingKey {
        case status
        case partialImageIndex = "partial_image_index"
        case imageB64 = "image_b64"
        case text
        case imageUrl = "image_url"
    }

    /// 是否为部分图片数据
    var isPartialImage: Bool {
        status == "image_generation" && partialImageIndex != nil && imageB64 != nil
    }

    /// 是否为完成状态
    var isCompleted: Bool {
        status == "completed"
    }
}
