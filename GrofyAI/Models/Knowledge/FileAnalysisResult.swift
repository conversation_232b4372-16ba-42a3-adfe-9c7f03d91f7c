import Foundation

// MARK: - 文件分析结果数据模型

/// 文件分析结果，包含总结、重点、大纲和预览四个部分
struct FileAnalysisResult: Codable, Hashable {
    let summary: String?
    let keyPoints: String?
    let outline: String?
    let fileUrl: String?
    let tags: [String]?
    let language: String?

    private enum CodingKeys: String, CodingKey {
        case summary
        case keyPoints = "key_points"
        case outline
        case fileUrl = "file_url"
        case tags
        case language
    }

    init(
        summary: String? = nil,
        keyPoints: String? = nil,
        outline: String? = nil,
        fileUrl: String? = nil,
        tags: [String]? = nil,
        language: String? = nil
    ) {
        self.summary = summary
        self.keyPoints = keyPoints
        self.outline = outline
        self.fileUrl = fileUrl
        self.tags = tags
        self.language = language
    }

    /// 检查是否有任何核心分析内容
    /// 只检查前4个核心字段：summary, keyPoints, outline, fileUrl
    var hasContent: Bool {
        return hasValidContent(summary) ||
            hasValidContent(keyPoints) ||
            hasValidContent(outline) ||
            hasValidContent(fileUrl)
    }

    /// 检查是否完整（所有四个核心字段都有内容）
    /// 不包括 tags 和 language 字段的检查
    var isComplete: Bool {
        return hasValidContent(summary) &&
            hasValidContent(keyPoints) &&
            hasValidContent(outline) &&
            hasValidContent(fileUrl)
    }

    /// 获取有内容的核心字段数量
    var contentFieldCount: Int {
        var count = 0
        if hasValidContent(summary) { count += 1 }
        if hasValidContent(keyPoints) { count += 1 }
        if hasValidContent(outline) { count += 1 }
        if hasValidContent(fileUrl) { count += 1 }
        return count
    }

    /// 检查字符串是否有有效内容（非空且非纯空白）
    private func hasValidContent(_ content: String?) -> Bool {
        guard let content else { return false }
        return !content.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
    }

    /// 检查 tags 数组是否有有效内容
    var hasValidTags: Bool {
        guard let tags else { return false }
        return tags.contains { !$0.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty }
    }

    /// 获取有效的 tags（过滤掉空字符串）
    var validTags: [String] {
        return tags?.compactMap { tag in
            let trimmed = tag.trimmingCharacters(in: .whitespacesAndNewlines)
            return trimmed.isEmpty ? nil : trimmed
        } ?? []
    }
}

// MARK: - 文件分析标签页类型

/// 文件分析的标签页类型
enum FileAnalysisTab: String, CaseIterable, Identifiable, Hashable {
    case summary = "summary"
    case keyPoints = "key_points"
    case outline = "outline"
    case preview = "file_url"

    var id: String { rawValue }

    /// 本地化显示名称
    var displayName: String {
        switch self {
        case .summary:
            return NSLocalizedString(
                "file_analysis.tab.summary.title",
                value: "总结",
                comment: "文件分析标签页：内容总结"
            )
        case .keyPoints:
            return NSLocalizedString(
                "file_analysis.tab.key_points.title",
                value: "重点",
                comment: "文件分析标签页：关键重点"
            )
        case .outline:
            return NSLocalizedString(
                "file_analysis.tab.outline.title",
                value: "大纲",
                comment: "文件分析标签页：文档结构大纲"
            )
        case .preview:
            return NSLocalizedString(
                "file_analysis.tab.preview.title",
                value: "预览",
                comment: "文件分析标签页：文件预览"
            )
        }
    }

    func getContent(from result: FileAnalysisResult) -> String? {
        switch self {
        case .summary:
            return result.summary
        case .keyPoints:
            return result.keyPoints
        case .outline:
            return result.outline
        case .preview:
            return result.fileUrl
        }
    }
}
