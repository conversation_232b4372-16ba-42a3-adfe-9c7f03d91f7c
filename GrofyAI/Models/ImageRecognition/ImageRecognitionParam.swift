//
//  ImageRecognitionParam.swift
//  GrofyAI
//
//  Created by kissy on 2025/7/8.
//

import Foundation

enum VisionTypeParentType:CaseIterable {
    
    case recognition
    case search
    case searchQuestions
    
    var description: String {
        switch self {
        case .search: return "拍照搜索"
        case .recognition: return "拍照识别"
        case .searchQuestions: return "拍照搜题"
        }
    }
}

enum VisionType: String, CaseIterable, Codable, Identifiable {
    var id: String { self.rawValue }
    
    case biology = "BIOLOGY"
    case chemistry = "CHEMISTRY"
    case physics = "PHYSICS"
    case mathematics = "MATHEMATICS"
    case food_calorie = "FOOD_CALORIE"
    case food_comment = "FOOD_COMMENT"
    case plant = "PLANT"
    case vision = "VISION"
    
    

    /// 用于在UI上显示的描述文本
    var description: String {
        switch self {
        case .biology: return "生物"
        case .chemistry: return "化学"
        case .physics: return "物理"
        case .mathematics: return "数学"
        case .food_calorie: return "食物热量"
        case .food_comment: return "美食点评"
        case .plant: return "植物识别"
        case .vision: return "通用识别"
        }
    }
    
    var parentType: VisionTypeParentType {
        switch self {
        case .vision:
            return .recognition
        case .biology, .chemistry, .physics, .mathematics:
            return .searchQuestions
        case .food_calorie, .food_comment, .plant:
            return .search
        }
    }
    
    var iconName: String {
            switch self {
            case .vision: return "ImagePlaceholderDog" // 主摄像头图标
            case .food_comment: return "ImagePlaceholderDog"
            case .food_calorie: return "ImagePlaceholderDog"
            case .plant: return "ImagePlaceholderDog" // 假设是朋友圈那个图标
            case .biology, .chemistry, .physics, .mathematics:
                return "ImagePlaceholderDog" // 暂时用一个图标代替，你可以换成具体的
            }
        }
}

/// 图片识别API请求参数模型
struct ImageRecognitionReq: Codable {
    let thread_id: String
    let uploaded_images: [UploadedImageInfo]?
    let vision_type: VisionType?
    let messages: String
    let is_first: Bool
    // for retry
//    let parentId: String?
//    let isFirst: Bool
}


struct UploadedImageInfo: Codable {
    let file_id: String
    let file_name: String
    let file_url:String
    // 如果还有其他字段，也一并添加进来
}
