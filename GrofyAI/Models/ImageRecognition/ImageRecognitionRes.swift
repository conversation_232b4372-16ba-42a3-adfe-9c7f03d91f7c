//
//  ImageRecognitionRes.swift
//  GrofyAI
//
//  Created by kissy on 2025/7/8.
//

//状态消息
struct ImageRecognitionStreamStatusRes: Codable {
    let code: Int
    let type: String // "status"
    let event: String? // "on_chain_start", "on_chain_end"
    let node: String? // "vision_agent"
}

//文本内容消息
struct ImageRecognitionStreamTextRes: Codable {
    let code: Int
    let type: String // "text"
    let content: String? // "元素: ** 一瓶白"
}

enum ImageRecognitionRes: Codable {
    case status(ImageRecognitionStreamStatusRes)
    case text(ImageRecognitionStreamTextRes)
    // 如果还有其他 type，可以继续在这里添加 case
    
    // 自定义解码逻辑是关键
    init(from decoder: Decoder) throws {
        // 先获取一个无键的容器来解码，并尝试读取 "type" 字段
        let container = try decoder.container(keyedBy: CodingKeys.self)
        let type = try container.decode(String.self, forKey: .type)
        
        // 根据 "type" 的值，决定如何完整地解码整个 JSON
        switch type {
        case "status":
            let response = try ImageRecognitionStreamStatusRes(from: decoder)
            self = .status(response)
        case "text":
            let response = try ImageRecognitionStreamTextRes(from: decoder)
            self = .text(response)
        default:
            throw DecodingError.dataCorruptedError(
                forKey: .type,
                in: container,
                debugDescription: "无法识别的事件类型: \(type)"
            )
        }
    }
    
    // 我们只需要解码，所以编码部分可以留空或按需实现
    func encode(to encoder: Encoder) throws {
        // ... 如果你需要将这个 enum 转回 JSON，在这里实现
    }
    
    // 定义一个 CodingKeys 来帮助我们预先读取 "type" 字段
    private enum CodingKeys: String, CodingKey {
        case type
    }
}


/// 处理图片识别响应数据
func convertSSEResponseToImageRecognition(_ sseResponse: SSEResponse) -> ImageRecognitionRes? {
    let code = sseResponse.code
    //    let { code } = sseResponse
    
    switch sseResponse.type {
        
    case "text":
        // 1. 确保 sseResponse.content 不是 nil
        guard let anyCodableContent = sseResponse.content else { return nil }
        
        // 2. 从 AnyCodable 的 value 中提取 String
        guard let stringContent = anyCodableContent.value as? String else { return nil }
        
        return .text(ImageRecognitionStreamTextRes(
            code: code,
            type: sseResponse.type,
            content: stringContent
        ))
        
    case "status":
        return .status(ImageRecognitionStreamStatusRes(
            code: code,
            type: sseResponse.type,
            event: sseResponse.event,
            node: sseResponse.node
        ))
        
    default:
        print("未知的SSE响应类型: \(sseResponse.type)")
        return nil
    }
}
