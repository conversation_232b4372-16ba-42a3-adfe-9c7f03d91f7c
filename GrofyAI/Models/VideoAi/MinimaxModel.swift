import SwiftUI

enum MinimaxVideoAi {
    
    //MARK: - MinimaxVideoAi.Mode
    enum Mode: String,GenericSelectorOptionsProtocol {
        var id: Self { self }
        
        case t2v = "Minimax_TextToVideo"
        case i2v = "Minimax_ImageToVide"
        
        var description: String {
            switch self {
            case .i2v: return "图生视频"
            case .t2v: return "文生视频"
            }
        }
    }
    
    enum T2V {
        // T2V_01_Director,T2V_01
        enum Model: String, Encodable, CaseIterable {
            case minimax_t2v_01_director = "MINI_MAX_T2V_01_DIRECTOR"
            case minimax_t2v_01 = "MINI_MAX_T2V_01"
        }

        case model(Model)
    }

    enum I2V {
        //I2V_01_DIRECTOR,I2V_01_LIVE,I2V_01
        enum Model: String, Encodable, CaseIterable {
            case minimax_i2v_01_director = "MINI_MAX_I2V_01_DIRECTOR"
            case minimax_i2v_01_live = "MINI_MAX_I2V_01_LIVE"
            case minimax_i2v_01 = "MINI_MAX_I2V_01"
        }

        case model(Model)
    }

    case t2v(T2V)
    case i2v(I2V)
}


//MARK: - RunwayVideoAi.Model
extension MinimaxVideoAi.T2V.Model: Identifiable {
    
    // 1. 遵循 Identifiable 协议
    // 我们可以直接使用它的 rawValue 作为唯一标识符。
    var id: String { self.rawValue }
    
    // 2. 添加一个用于展示的名称 (计算属性)
    var displayName: String {
        switch self {
        case .minimax_t2v_01:
            return "V1.0"
        case .minimax_t2v_01_director:
            return "V1.0 Director"
        }
    }
    
    // 3. 添加一个对应的图标 (计算属性)
    var icon: String {
        switch self {
//        case .minimax_t2v_01:
//            return "star.fill"   // 代表专业版
//        case .minimax_t2v_01_director:
//            return "crown.fill"  // 代表顶级、至尊版
        default:
            return "IconMinimax"
        }
    }
    
    var icon_darkTheme:String {
        switch self {
        default:
            return "IconMinimax"
        }
    }
    
    var describe: String {
        switch self {
        case .minimax_t2v_01:
            return "海螺文生视频-标准版"
        case .minimax_t2v_01_director:
            return "海螺文生视频-导演版，更高质量"
        }
    }
}


extension MinimaxVideoAi.I2V.Model: Identifiable {
    
    // 1. 遵循 Identifiable 协议
    // 我们可以直接使用它的 rawValue 作为唯一标识符。
    var id: String { self.rawValue }
    
    // 2. 添加一个用于展示的名称 (计算属性)
    var displayName: String {
        switch self {
        case .minimax_i2v_01:
            return "V1.0"
        case .minimax_i2v_01_director:
            return "V1.0 Director"
        case .minimax_i2v_01_live:
            return "V1.0 Live"
        }
    }
    
    // 3. 添加一个对应的图标 (计算属性)
    var icon: String {
        switch self {
//        case .minimax_i2v_01:
//            return "hammer.fill" // 代表开发、工具
//        case .minimax_i2v_01_director:
//            return "star.fill"   // 代表专业版
//        case .minimax_i2v_01_live:
//            return "crown.fill"  // 代表顶级、至尊版
        default:
            return "IconMinimax"
        }
    }
    
    var icon_darkTheme:String {
        switch self {
        default:
            return "IconMinimax"
        }
    }
    
    var describe: String {
        switch self {
        case .minimax_i2v_01:
            return "海螺图生视频-标准版"
        case .minimax_i2v_01_director:
            return "海螺图生视频-导演版，更高质量"
        case .minimax_i2v_01_live:
            return "海螺图生视频-实时版，速度快"
        }
    }
}
