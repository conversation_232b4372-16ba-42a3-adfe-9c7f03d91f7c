import SwiftUI


enum WanxVideoAi {
    
    //MARK: - WanxVideoAi.Mode 
    enum Mode: String, GenericSelectorOptionsProtocol {
        var id: Self { self }
        
        case t2v = "Wanx_TextToVideo"
        case i2v = "Wanx_ImageToVide"
        
        var description: String {
            switch self {
            case .i2v: return "图生视频"
            case .t2v: return "文生视频"
            }
        }
    }
    
    //MARK: - WanxVideoAi.T2v
    enum T2V { 
        enum Model: String, Encodable, CaseIterable {
            case wanx_t2v_model2_1_turbo = "WANX_T2V_MODEL2_1_TURBO"
            case wanx_t2v_model2_1_plus = "WANX_T2V_MODEL2_1_PLUS"
        }

        enum Resolution: String, Encodable, CaseIterable, AiDescription {
            case resolution_480p = "RESOLUTION_480P"
            case resolution_720p = "RESOLUTION_720P"

            var description: String {
                switch self {
                case .resolution_480p:
                    return "480P"
                case .resolution_720p:
                    return "720P"
                }
            }
            
            var supportedModels: [Model] {
                switch self {
                case .resolution_720p: return [.wanx_t2v_model2_1_plus, .wanx_t2v_model2_1_turbo]
                case .resolution_480p: return [.wanx_t2v_model2_1_turbo]
                    
                }
            }
        }

        enum Size: String, Encodable, CaseIterable, AiDescription {
            case size_480p_16_9 = "SIZE_480P_16_9"
            case size_480p_9_16 = "SIZE_480P_9_16"
            case size_480p_1_1 = "SIZE_480P_1_1"
            case size_720p_16_9 = "SIZE_720P_16_9"
            case size_720p_9_16 = "SIZE_720P_9_16"
            case size_720p_1_1 = "SIZE_720P_1_1"
            case size_720p_3_4 = "SIZE_720P_3_4"
            case size_720p_4_3 = "SIZE_720P_4_3"

            var description: String { 
                switch self {
                case .size_480p_16_9:
                    return "16:9"
                case .size_480p_9_16:
                    return "9:16"
                case .size_480p_1_1:
                    return "1:1"
                    
                case .size_720p_16_9:
                    return "16:9"
                case .size_720p_9_16:
                    return "9:16"
                case .size_720p_1_1:
                    return "1:1"
                case .size_720p_3_4:
                    return "3:4"
                case .size_720p_4_3:
                    return "4:3"
                }
            }
                        
            var supportedResolution: Resolution {
                switch self {
                case .size_720p_16_9, .size_720p_9_16, .size_720p_4_3, .size_720p_3_4, .size_720p_1_1:
                    return .resolution_720p
                    
                    // 其他所有 case (即 480p)，只返回 turbo 模型
                case .size_480p_1_1, .size_480p_16_9, .size_480p_9_16:
                    return .resolution_480p
                }
                
            }
        }
        
        enum Duration: Int, Encodable, CaseIterable, AiDescription  {
            case five = 5
            
            var description: String {
                switch self {
                case .five: return "5s"
                }
            }
        }

    }

    //MARK: - WanxVideoAi.I2V
    enum I2V {
        enum Model: String, Encodable, CaseIterable {
            case wanx_i2v_model2_1_turbo = "WANX_I2V_MODEL2_1_TURBO"
            case wanx_i2v_model2_1_plus = "WANX_I2V_MODEL2_1_PLUS"
        }

        enum Resolution: String, Encodable, CaseIterable,AiDescription {
            case resolution_480p = "RESOLUTION_480P"
            case resolution_720p = "RESOLUTION_720P"

            var description: String {
                switch self {
                case .resolution_480p:
                    return "480P"
                case .resolution_720p:
                    return "720P"
                }
            }
            
            var supportedModels: [Model] {
                switch self {
                case .resolution_720p: return [.wanx_i2v_model2_1_plus, .wanx_i2v_model2_1_turbo]
                case .resolution_480p: return [.wanx_i2v_model2_1_turbo]
                    
                }
            }
        }
        
        enum Duration: Int, Encodable, CaseIterable, AiDescription  {
            case three = 3
            case four = 4
            case five = 5
            
            var description: String {
                switch self {
                case .three: return "3s"
                case .four: return "4s"
                case .five: return "5s"
                }
            }
            
            var supportedModels: [Model] {
                switch self {
                case .five: return [.wanx_i2v_model2_1_plus, .wanx_i2v_model2_1_turbo]
                case .three, .four: return [.wanx_i2v_model2_1_turbo]
                    
                }
            }
            
        }

//        case model(Model)
//        case resolution(Resolution)
    }

    case t2v(T2V)
    case i2v(I2V)
}


//MARK: - WanxVideoAi.T2V.Model
extension WanxVideoAi.T2V.Model: Identifiable {
    
    // 1. 遵循 Identifiable 协议
    // 我们可以直接使用它的 rawValue 作为唯一标识符。
    var id: String { self.rawValue }
    
    // 2. 添加一个用于展示的名称 (计算属性)
    var displayName: String {
        switch self {
        case .wanx_t2v_model2_1_plus:
            return "V2.1 Plus"
        case .wanx_t2v_model2_1_turbo:
            return "V2.1 Turbo"
        }
    }
    
    // 3. 添加一个对应的图标 (计算属性)
    var icon: String {
        switch self {
//        case .wanx_t2v_model2_1_plus:
//            return "star.fill"   // 代表专业版
//        case .wanx_t2v_model2_1_turbo:
//            return "crown.fill"  // 代表顶级、至尊版
        default:
            return "IconQwen"
        }
    }
    
    var icon_darkTheme:String {
        switch self {
        default:
            return "IconQwen__customWhite"
        }
    }
    
    var describe: String {
        switch self {
        case .wanx_t2v_model2_1_plus:
            return "万相文字生成视频-增强版"
        case .wanx_t2v_model2_1_turbo:
            return "万相文字生成视频-极速版"
        }
    }
}


//MARK: - WanxVideoAi.T2V.Size
extension WanxVideoAi.T2V.Size: AspectRatioRepresentable {
    
    // 1. 满足 Identifiable 协议 (enum case 本身就是唯一的)
    var id: Self { self }
    
    // 2. 满足 AspectRatioRepresentable 协议的要求
    var ratioText: String {
        return self.description // 直接复用你已有的 description
    }
    
    var secondaryDescription: String? {
        switch self {
        case .size_480p_1_1 : return  nil
        case .size_480p_16_9: return  nil
        case .size_480p_9_16 : return  nil
        case .size_720p_1_1 : return  nil
        case .size_720p_3_4 : return  nil
        case .size_720p_4_3 : return  nil
        case .size_720p_16_9 : return  nil
        case .size_720p_9_16 : return  nil
        }
    }
    
    var widthRatio: CGFloat {
        switch self {
        case .size_480p_1_1 : return  1
        case .size_480p_16_9: return  16
        case .size_480p_9_16 : return  9
        case .size_720p_1_1 : return  1
        case .size_720p_3_4 : return  3
        case .size_720p_4_3 : return  4
        case .size_720p_16_9 : return  16
        case .size_720p_9_16 : return  9
        }
    }
    
    var heightRatio: CGFloat {
        switch self {
        case .size_480p_1_1 : return  1
        case .size_480p_16_9: return  9
        case .size_480p_9_16 : return  16
        case .size_720p_1_1 : return  1
        case .size_720p_3_4 : return  4
        case .size_720p_4_3 : return  3
        case .size_720p_16_9 : return  9
        case .size_720p_9_16 : return  16
        }
    }
}

//MARK: - WanxVideoAi.I2V.Model
extension WanxVideoAi.I2V.Model: Identifiable {
    
    // 1. 遵循 Identifiable 协议
    // 我们可以直接使用它的 rawValue 作为唯一标识符。
    var id: String { self.rawValue }
    
    // 2. 添加一个用于展示的名称 (计算属性)
    var displayName: String {
        switch self {
        case .wanx_i2v_model2_1_plus:
            return "V2.1 Plus"
        case .wanx_i2v_model2_1_turbo:
            return "V2.1 Turbo"
        }
    }
    
    // 3. 添加一个对应的图标 (计算属性)
    var icon: String {
        switch self {
//        case .wanx_i2v_model2_1_plus:
//            return "star.fill"   // 代表专业版
//        case .wanx_i2v_model2_1_turbo:
//            return "crown.fill"  // 代表顶级、至尊版
        default:
            return "IconQwen"
        }
    }
    
    var icon_darkTheme:String {
        switch self {
        default:
            return "IconQwen__customWhite"
        }
    }
    
    var describe: String {
        switch self {
        case .wanx_i2v_model2_1_plus:
            return "万相图片生成视频-增强版"
        case .wanx_i2v_model2_1_turbo:
            return "万相图片生成视频-极速版"
        }
    }
}
