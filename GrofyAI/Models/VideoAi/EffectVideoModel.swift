enum EffectVideoAi {
    
    enum ContentType: String, CaseIterable,Identifiable {
        var id: Self { self }

        case single = "单张图片"
        case multiple = "多张图片"
    }
    
    enum EffectType: String, Encodable, CaseIterable,Identifiable,AiDescription {
        var id: Self { self }

        case fuzzy = "FUZZY"
        case squish = "SQUISH"
        case expansion = "EXPANSION"
        case hug = "HUG"
        case kiss = "KISS"
        case heartGesture = "HEART_GESTURE"
        case venom = "VENOM"
        case shoot = "SHOOT"
        case batman = "BATMAN"
        case ironman = "IRONMAN"
        case joker = "JOKER"
        case squidGame = "SQUID_GAME"
        case squidGameEye = "SQUID_GAME_EYE"
        case babyFace = "BABY_FACE"
        case muscleMan = "MUSCLE_MAN"
        case longHairMagic = "LONG_HAIR_MAGIC"
//        case hotHarleyQuinn = "HOT_HARLEY_QUINN"
        case catCatWoman = "CAT_CAT_WOMAN"
        case wonderWoman = "WONDER_WOMAN"
        case sheepCurls = "SHEEP_CURLS"
        case christmasGift = "CHRISTMAS_GIFT"
        case christmasElf = "CHRISTMAS_ELF"
        case christmasOOTD = "CHRISTMAS_OOTD"
        case wuKong = "WU_KONG"
        case sailorMoon = "SAILOR_MOON"
        
        
        var hot:String {
            switch self {
            case .fuzzy,.squish,.expansion,.hug,.kiss,.wonderWoman:
                return "HOT"
            default:
                return ""
            }
            
        }
        
        
        var requiredContentType: ContentType {
                switch self {
                case .hug, .kiss, .heartGesture:
                    return .multiple // 这三个是多图场景
                default:
                    return .single // 其他所有都是单图场景
                }
            }

        var description: String {
            switch self {
            case .fuzzy:
                return "模糊"
            case .squish:
                return "挤压"
            case .expansion:
                return "扩展"
            case .hug:
                return "拥抱"
            case .kiss:
                return "亲吻"
            case .heartGesture:
                return "心形手势"
            case .venom:
                return "毒液"
            case .shoot:
                return "射击"
            case .batman:
                return "蝙蝠侠"
            case .ironman:
                return "钢铁侠"
            case .joker:
                return "小丑"
            case .squidGame:
                return "鱿鱼游戏"
            case .squidGameEye:
                return "鱿鱼游戏眼睛"
            case .babyFace:
                return "婴儿脸"
            case .muscleMan:
                return "肌肉男"
            case .longHairMagic:
                return "长发魔法"
//            case .hotHarleyQuinn:
//                return "性感哈莉·奎茵"
            case .catCatWoman:
                return "猫女猫猫"
            case .wonderWoman:
                return "神奇女侠"
            case .sheepCurls:
                return "羊卷发"
            case .christmasGift:
                return "圣诞礼物"
            case .christmasElf:
                return "圣诞精灵"
            case .christmasOOTD:
                return "圣诞穿搭"
            case .wuKong:
                return "悟空变身"
            case .sailorMoon:
                return "水手月亮"

            }
        }
        
        var imageName: String {
            switch self {
            case .fuzzy:
                return "effect_fuzzy"
            case .squish:
                return "effect_squish"
            case .expansion:
                return "effect_expansion"
            case .hug:
                return "effect_hug"
            case .kiss:
                return "effect_kiss"
            case .heartGesture:
                return "effect_heart_gesture"
            case .venom:
                return "effect_venom"
            case .shoot:
                return "effect_shoot"
            case .batman:
                return "effect_batman"
            case .ironman:
                return "effect_ironman"
            case .joker:
                return "effect_joker"
            case .squidGame:
                return "effect_squid_game"
            case .squidGameEye:
                return "effect_squid_game_eye"
            case .babyFace:
                return "effect_baby_face"
            case .muscleMan:
                return "effect_muscle_man"
            case .longHairMagic:
                return "effect_long_hair_magic"
//            case .hotHarleyQuinn:
//                return "effect_hot_harly_quinn"
            case .catCatWoman:
                return "effect_cat_cat_woman"
            case .wonderWoman:
                return "effect_wonder_woman"
            case .sheepCurls:
                return "effect_sheep_curls"
            case .christmasGift:
                return "effect_christmas_gift"
            case .christmasElf:
                return "effect_christmas_elf"
            case .christmasOOTD:
                return "effect_christmas_ootd"
            case .wuKong:
                return "effect_wu_kong"
            case .sailorMoon:
                return "effect_sailor_moon"
            }
        }
        

        
    }

    case effectType(EffectType)
}
