//
//  VideoModels.swift
//  GrofyAI
//
//  Created by kissy on 2025/7/25.
//

enum VideoModelsIcon:String, CaseIterable {
    case google     = "IconGoogle"
    case midjourney = "IconMidjourney"
    case qwen       = "IconQwen"
    case kling      = "IconKling"
    case luma       = "IconLuma"
    case hailu      = "IconHailuo" // "Hailuo" 对应 "hailu" 比较符合 Swift 命名习惯
    case ideogram   = "IconIdeogram"
    case minimax    = "IconMinimax"
    case pixverse   = "IconPixverse"
    case pika       = "IconPika"
    
    var darkTheme: String {
        switch self {
        case .midjourney, .qwen, .ideogram, .pika:
            return self.rawValue + "__customWhite"
        default:
            return self.rawValue
        }
    }
}
