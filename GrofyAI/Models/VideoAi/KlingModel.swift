
import SwiftUI

enum CameraConfigOption :String, Encodable, CaseIterable, Identifiable {
    var id: String { self.rawValue }
    
    case horizontal
    case vertical
    case pan
    case tilt
    case roll
    case zoom
    case none
    
    var description: String {
        switch self {
        case .horizontal:
            return "水平运镜"
        case .vertical:
            return "垂直运镜"
        case .pan:
            return "旋转"
        case .tilt:
            return "垂直翻转"
        case .roll:
            return "水平翻转"
        case .zoom:
            return "放大｜缩小"
        case .none:
            return "无"
        }
    }
}

enum KlingVideoAi {
    
    //MARK: - KlingVideoAi.Mode
    enum Mode: String,GenericSelectorOptionsProtocol {
        var id: Self { self }
        
        case t2v = "Kling_TextToVideo"
        case i2v = "Kling_ImageToVide"
        
        var description: String {
            switch self {
            case .i2v: return "图生视频"
            case .t2v: return "文生视频"
            }
        }
    }
    
    enum T2V {
        //KLING_V1,KLING_V1_6
        enum Model: String, Encodable, CaseIterable {
            case kling_v1 = "KLING_V1"
            case kling_v1_6 = "KLING_V1_6"
        }

        enum Mode: String, Encodable, CaseIterable, AiDescription {
            case std = "STD"
            case pro = "PRO"

            var description: String {
                switch self {
                case .std:
                    return "基础模式"
                case .pro:
                    return "专业模式"
                }
            }
            
            var supportedModel: [Model] {
                switch self {
                case .std:
                    return [.kling_v1, .kling_v1_6]
                case .pro:
                    return [.kling_v1]
                }
            }
        }
        
        enum Duration: Int, Encodable, CaseIterable, AiDescription  {
            case five = 5
            case ten = 10
            
            var description: String {
                switch self {
                case .five: return "5s"
                case .ten: return "10s"
                
                }
            }
        }

        enum CameraMovementType: String, Encodable, CaseIterable, Identifiable {
            var id: String { self.rawValue }
            
            case simple = "SIMPLE"
            case down_back = "DOWN_BACK"
            case forward_up = "FORWARD_UP"
            case right_turn_forward = "RIGHT_TURN_FORWARD"
            case left_turn_forward = "LEFT_TURN_FORWARD"

            var description: String {
                switch self {
                case .simple:
                    return "简单"
                case .down_back:
                    return "向下后退"
                case .forward_up:
                    return "向前上升"
                case .right_turn_forward:
                    return "右转前进"
                case .left_turn_forward:
                    return "左转前进"
                }
            }
        }

        enum AspectRatio: String, Encodable, CaseIterable,AiDescription {
            case ratio_16_9 = "RATIO_16_9"
            case ratio_9_16 = "RATIO_9_16"
            case ratio_1_1 = "RATIO_1_1"

            var description: String {
                switch self {
                case .ratio_16_9:
                    return "16:9"
                case .ratio_9_16:
                    return "9:16"
                case .ratio_1_1:
                    return "1:1"
                }
            }
        }

    }

    enum I2V {
        enum Model: String, Encodable, CaseIterable {
            case kling_v1 = "KLING_V1"
            case kling_v1_6 = "KLING_V1_6"
        }

        enum Mode: String, Encodable, CaseIterable,AiDescription {
            case std = "STD"
            case pro = "PRO"

            var description: String {
                switch self {
                case .std:
                    return "基础模式"
                case .pro:
                    return "专业模式"
                }
            }
        }
        
        enum CameraMovementType: String, Encodable, CaseIterable, Identifiable {
            var id: String { self.rawValue }
            
            case simple = "SIMPLE"
            case down_back = "DOWN_BACK"
            case forward_up = "FORWARD_UP"
            case right_turn_forward = "RIGHT_TURN_FORWARD"
            case left_turn_forward = "LEFT_TURN_FORWARD"

            var description: String {
                switch self {
                case .simple:
                    return "简单"
                case .down_back:
                    return "向下后退"
                case .forward_up:
                    return "向前上升"
                case .right_turn_forward:
                    return "右转前进"
                case .left_turn_forward:
                    return "左转前进"
                }
            }
        }

        enum AspectRatio: String, Encodable, CaseIterable {
            case ratio_16_9 = "RATIO_16_9"
            case ratio_9_16 = "RATIO_9_16"
            case ratio_1_1 = "RATIO_1_1"

            var description: String {
                switch self {
                case .ratio_16_9:
                    return "16:9"
                case .ratio_9_16:
                    return "9:16"
                case .ratio_1_1:
                    return "1:1"
                }
            }
        }

        enum Duration: Int, Encodable, CaseIterable, AiDescription  {
            case five = 5
            case ten = 10
            
            var description: String {
                switch self {
                case .five: return "5s"
                case .ten: return "10s"
                
                }
            }
        }
    }
}


//MARK: - KlingVideoAi.T2V.Model
extension KlingVideoAi.T2V.Model: Identifiable {
    
    // 1. 遵循 Identifiable 协议
    // 我们可以直接使用它的 rawValue 作为唯一标识符。
    var id: String { self.rawValue }
    
    // 2. 添加一个用于展示的名称 (计算属性)
    var displayName: String {
        switch self {
        case .kling_v1:
            return "V1.0"
        case .kling_v1_6:
            return "V1.6"
        }
    }
    
    // 3. 添加一个对应的图标 (计算属性)
    var icon: String {
        switch self {
//        case .kling_v1:
//            return "star.fill"   // 代表专业版
//        case .kling_v1_6:
//            return "crown.fill"  // 代表顶级、至尊版
        default:
            return "IconKling"
        }
    }
    
    var icon_darkTheme:String {
        switch self {
        default:
            return "IconKling"
        }
    }
    
    var describe: String {
        switch self {
        case .kling_v1:
            return "快手 Kling V1 视频模型"
        case .kling_v1_6:
            return "快手 Kling V1.6 视频模型"
        }
    }
}


//MARK: - KlingVideoAi.T2V.AspectRatio
extension KlingVideoAi.T2V.AspectRatio: AspectRatioRepresentable {
    
    // 1. 满足 Identifiable 协议 (enum case 本身就是唯一的)
    var id: Self { self }
    
    // 2. 满足 AspectRatioRepresentable 协议的要求
    var ratioText: String {
        return self.description // 直接复用你已有的 description
    }
    
    var secondaryDescription: String? {
        switch self {
        case .ratio_16_9: return nil
        case .ratio_9_16: return nil
        case .ratio_1_1: return nil
        }
    }
    
    var widthRatio: CGFloat {
        switch self {
        case .ratio_16_9: return 16
        case .ratio_9_16: return 9
        case .ratio_1_1: return 1
        }
    }
    
    var heightRatio: CGFloat {
        switch self {
        case .ratio_16_9: return 9
        case .ratio_9_16: return 16
        case .ratio_1_1: return 1
        }
    }
}


//MARK: - KlingVideoAi.I2V.Model
extension KlingVideoAi.I2V.Model: Identifiable {
    
    // 1. 遵循 Identifiable 协议
    // 我们可以直接使用它的 rawValue 作为唯一标识符。
    var id: String { self.rawValue }
    
    // 2. 添加一个用于展示的名称 (计算属性)
    var displayName: String {
        switch self {
        case .kling_v1:
            return "V1.0"
        case .kling_v1_6:
            return "V1.6"
        }
    }
    
    // 3. 添加一个对应的图标 (计算属性)
    var icon: String {
        switch self {
//        case .kling_v1:
//            return "star.fill"   // 代表专业版
//        case .kling_v1_6:
//            return "crown.fill"  // 代表顶级、至尊版
        default:
            return "IconKling"
        }
    }
    
    var icon_darkTheme:String {
        switch self {
        default:
            return "IconKling"
        }
    }
    
    var describe: String {
        switch self {
        case .kling_v1:
            return "快手 Kling V1 视频模型"
        case .kling_v1_6:
            return "快手 Kling V1.6 视频模型"
        }
    }
}


//MARK: - KlingVideoAi.T2V.AspectRatio
extension KlingVideoAi.I2V.AspectRatio: AspectRatioRepresentable {
    
    // 1. 满足 Identifiable 协议 (enum case 本身就是唯一的)
    var id: Self { self }
    
    // 2. 满足 AspectRatioRepresentable 协议的要求
    var ratioText: String {
        return self.description // 直接复用你已有的 description
    }
    
    var secondaryDescription: String? {
        switch self {
        case .ratio_16_9: return nil
        case .ratio_9_16: return nil
        case .ratio_1_1: return nil
        }
    }
    
    var widthRatio: CGFloat {
        switch self {
        case .ratio_16_9: return 16
        case .ratio_9_16: return 9
        case .ratio_1_1: return 1
        }
    }
    
    var heightRatio: CGFloat {
        switch self {
        case .ratio_16_9: return 9
        case .ratio_9_16: return 16
        case .ratio_1_1: return 1
        }
    }
}
