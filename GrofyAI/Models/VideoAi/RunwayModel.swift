import SwiftUI

enum RunwayVideoAi {
    // GEN4_TURBO,GEN3_TURBO
    enum Model: String, Encodable, CaseIterable {
        case runway_gen4_turbo = "RUNWAY_GEN4_TURBO"
        case runway_gen3_turbo = "RUNWAY_GEN3_TURBO"
    }
    

    enum Duration: Int, Encodable, CaseIterable,AiDescription {
            case five  = 5
            case ten = 10

            var description: String {
                switch self {
                case .five:
                    return "5s"
                case .ten:
                    return "10s"
                }
            }
    }

    enum Ratio: String, Encodable, CaseIterable {
        case ratio_1280_720 = "RATIO_1280_720"
        case ratio_720_1280 = "RATIO_720_1280"
        case ratio_1104_832 = "RATIO_1104_832"
        case ratio_832_1104 = "RATIO_832_1104"
        case ratio_960_960 = "RATIO_960_960"
        case ratio_1584_672 = "RATIO_1584_672"
        case ratio_1280_768 = "RATIO_1280_768"
        case ratio_768_1280 = "RATIO_768_1280"

        var description: String {
            switch self {
            case .ratio_1280_720:
                return "16:9"
            case .ratio_720_1280:
                return "9:16"
            case .ratio_1104_832:
                return "4:3"
            case .ratio_832_1104:
                return "3:4"
            case .ratio_960_960:
                return "1:1"
            case .ratio_1584_672:
                return "21:9"
            case .ratio_1280_768:
                return "5:3"
            case .ratio_768_1280:
                return "3:5"
                
            }
        }
        
        var supportedModel: Model {
            switch self {
            case .ratio_1280_720, .ratio_720_1280, .ratio_1104_832, .ratio_832_1104, .ratio_960_960, .ratio_1584_672:
                return .runway_gen4_turbo
                
            case .ratio_1280_768, .ratio_768_1280:
                return .runway_gen3_turbo
            }
        }
        
    }
//    case model(Model)
//    case duration(Duration)
//    case ratio(Ratio)

}


//MARK: - RunwayVideoAi.Model
extension RunwayVideoAi.Model: Identifiable {
    
    // 1. 遵循 Identifiable 协议
    // 我们可以直接使用它的 rawValue 作为唯一标识符。
    var id: String { self.rawValue }
    
    // 2. 添加一个用于展示的名称 (计算属性)
    var displayName: String {
        switch self {
        case .runway_gen3_turbo:
            return "Gen3 turbo"
        case .runway_gen4_turbo:
            return "Gen4 turbo"
        }
    }
    
    // 3. 添加一个对应的图标 (计算属性)
    var icon: String {
        switch self {
//        case .runway_gen3_turbo:
//            return "star.fill"   // 代表专业版
//        case .runway_gen4_turbo:
//            return "crown.fill"  // 代表顶级、至尊版
        default:
            return "IconRunway"
        }
    }
    
    var icon_darkTheme:String {
        switch self {
        default:
            return "IconRunway__customWhite"
        }
    }
    
    var describe: String {
        switch self {
        case .runway_gen3_turbo:
            return "Runway 第三代极速视频模型"
        case .runway_gen4_turbo:
            return "Runway 第四代旗舰极速模型o"
        }
    }
}


extension RunwayVideoAi.Ratio: AspectRatioRepresentable {
    
    // 1. 满足 Identifiable 协议 (enum case 本身就是唯一的)
    var id: Self { self }
    
    // 2. 满足 AspectRatioRepresentable 协议的要求
    var ratioText: String {
        return self.description // 直接复用你已有的 description
    }
    
    var secondaryDescription: String? {
        switch self {
            
        case .ratio_1104_832 : return  nil
        case .ratio_832_1104 : return  nil
            
        case .ratio_1280_720 : return  nil
        case .ratio_720_1280 : return  nil
            
        case .ratio_1584_672 : return  nil
        
        case .ratio_1280_768 : return  nil
        case .ratio_768_1280 : return  nil
        
        case .ratio_960_960 : return  nil
        }
    }
    
    var widthRatio: CGFloat {
        switch self {
            
        case .ratio_1104_832 : return  1104
        case .ratio_832_1104 : return  832
            
        case .ratio_1280_720 : return  1280
        case .ratio_720_1280 : return  720
            
        case .ratio_1584_672 : return  1584
        
        case .ratio_1280_768 : return  1280
        case .ratio_768_1280 : return  768
        
        case .ratio_960_960 : return  960
        }
    }
    
    var heightRatio: CGFloat {
        switch self {
            
        case .ratio_1104_832 : return  832
        case .ratio_832_1104 : return  1104
            
        case .ratio_1280_720 : return  720
        case .ratio_720_1280 : return  1280
            
        case .ratio_1584_672 : return  672
        
        case .ratio_1280_768 : return  768
        case .ratio_768_1280 : return  1280
        
        case .ratio_960_960 : return  960
        }
    }
}
