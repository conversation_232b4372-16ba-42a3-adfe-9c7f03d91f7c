import SwiftUI

enum PixverseVideoAi {
    //MARK: - WanxVideoAi.Mode
    enum Mode: String,GenericSelectorOptionsProtocol {
        var id: Self { self }
        
        case t2v = "Pixverse_TextToVideo"
        case i2v = "Pixverse_ImageToVide"
        
        var description: String {
            switch self {
            case .i2v: return "图生视频"
            case .t2v: return "文生视频"
            }
        }
    }
    
    
    
    enum T2V {
        //MARK: - PixverseVideoAi.T2V.AspectRatio
        enum AspectRatio : String, Encodable, CaseIterable, CustomStringConvertible {
            case ratio_16_9 = "RATIO_16_9"
            case ratio_9_16 = "RATIO_9_16"
            case ratio_4_3 = "RATIO_4_3"
            case ratio_3_4 = "RATIO_3_4"
            case ratio_1_1 = "RATIO_1_1"

            var description: String {
                switch self {
                case .ratio_16_9:
                    return "16:9"
                case .ratio_9_16:
                    return "9:16"
                case .ratio_4_3:
                    return "4:3"
                case .ratio_3_4:
                    return "3:4"
                case .ratio_1_1:
                    return "1:1"
                }
            }
        }
        
        //MARK: - PixverseVideoAi.T2V.Model
        enum Model: String, Encodable, CaseIterable {
            case pixverse_v3_5 = "PIXVERSE_V_3_5"
            case pixverse_v4_0 = "PIXVERSE_V_4"
            case pixverse_v4_5 = "PIXVERSE_V_4_5"
        }
        
        //MARK: - PixverseVideoAi.T2V.Duration
        enum Duration: Int, Encodable, CaseIterable, AiDescription {
            case five  = 5
            case eight = 8

            var description: String {
                switch self {
                case .five:
                    return "5s"
                case .eight:
                    return "8s"
                }
            }
            
            var supportedQuality: [Quality] {
                switch self {
                case .eight:
                    return [.quality_540p, .quality_720p]
                    
                case .five:
                    return [.quality_540p, .quality_720p, .quality_1080p]
                }
                
            }
            
        }
        //MARK: - PixverseVideoAi.T2V.MotionMode
        enum MotionMode: String, Encodable, CaseIterable,AiDescription {
            case normal = "NORMAL"
            case fast = "FAST"

            var description: String {
                switch self {
                case .normal:
                    return "正常"
                case .fast:
                    return "快速"
                }
            }
            
            var supportedDuration: [Duration] {
                switch self {
                case .normal:
                    return [.five, .eight]
                    
                case .fast:
                    return [.five]
                }
            }
            
            var supportedQuality: [Quality] {
                switch self {
                case .normal:
                    return [.quality_540p, .quality_720p,.quality_1080p]
                    
                case .fast:
                    return [.quality_540p, .quality_720p]
                }
            }
        }
        //MARK: - PixverseVideoAi.T2V.Quality
        enum Quality: String, Encodable, CaseIterable,AiDescription {
            case quality_540p = "QUALITY_540p"
            case quality_720p = "QUALITY_720p"
            case quality_1080p = "QUALITY_1080p"

            var description: String {
                switch self {
                case .quality_540p:
                    return "540P"
                case .quality_720p:
                    return "720P"
                case .quality_1080p:
                    return "1080P"
                }
            }   
        }
        //MARK: - PixverseVideoAi.T2V.Style
        enum Style: String, Encodable, CaseIterable ,AiDescription {
            case style_anime = "STYLE_ANIME"
            case style_animation = "STYLE_ANIMATION"
            case style_clay = "STYLE_CLAY"
            case style_comic = "STYLE_COMIC"
            case style_cyberpunk = "STYLE_CYBERPUNK"

            var description: String {
                switch self {
                case .style_anime:
                    return "动漫"
                case .style_animation:
                    return "动画"
                case .style_clay:
                    return "简约"
                case .style_comic:
                    return "漫画"
                case .style_cyberpunk:
                    return "赛博朋克"
                }
            }
            
            
            var ImageName: String {
                switch self {
                case .style_anime:
                    return "anime"
                case .style_animation:
                    return "pixelArt"
                case .style_clay:
                    return "analogFilm"
                case .style_comic:
                    return "manga"
                case .style_cyberpunk:
                    return "neonPunk"
                }
            }
        }
    }

    enum I2V {
        //MARK: - PixverseVideoAi.I2V.Model
        enum Model: String, Encodable, CaseIterable {
            case pixverse_v3_5 = "PIXVERSE_V_3_5"
            case pixverse_v4_0 = "PIXVERSE_V_4"
            case pixverse_v4_5 = "PIXVERSE_V_4_5"
        }
        //MARK: - PixverseVideoAi.I2V.Duration
        enum Duration: Int, Encodable, CaseIterable, AiDescription {
            case five  = 5
            case eight = 8

            var description: String {
                switch self {
                case .five:
                    return "5s"
                case .eight:
                    return "8s"
                }
            }
            
            var supportedQuality: [Quality] {
                switch self {
                case .eight:
                    return [.quality_540p, .quality_720p]
                    
                case .five:
                    return [.quality_540p, .quality_720p, .quality_1080p]
                }
                
            }
        }
        //MARK: - PixverseVideoAi.I2V.MotionMode
        enum MotionMode: String, Encodable, CaseIterable,AiDescription {
            case normal = "NORMAL"
            case fast = "FAST"

            var description: String {
                switch self {
                case .normal:
                    return "正常"
                case .fast:
                    return "快速"
                }
            }
            
            var supportedDuration: [Duration] {
                switch self {
                case .normal:
                    return [.five, .eight]
                    
                case .fast:
                    return [.five]
                }
            }
            
            var supportedQuality: [Quality] {
                switch self {
                case .normal:
                    return [.quality_540p, .quality_720p,.quality_1080p]
                    
                case .fast:
                    return [.quality_540p, .quality_720p]
                }
            }
        }
        //MARK: - PixverseVideoAi.I2V.Quality
        enum Quality: String, Encodable, CaseIterable,AiDescription {
            case quality_540p = "QUALITY_540p"
            case quality_720p = "QUALITY_720p"
            case quality_1080p = "QUALITY_1080p"

            var description: String {
                switch self {
                case .quality_540p:
                    return "540P"
                case .quality_720p:
                    return "720P"
                case .quality_1080p:
                    return "1080P"
                }
            }
        }
        //MARK: - PixverseVideoAi.I2V.Style
        enum Style: String, Encodable, CaseIterable,AiDescription {
            case style_anime = "STYLE_ANIME"
            case style_animation = "STYLE_ANIMATION"
            case style_clay = "STYLE_CLAY"
            case style_comic = "STYLE_COMIC"
            case style_cyberpunk = "STYLE_CYBERPUNK"

            var description: String {
                switch self {
                case .style_anime:
                    return "动漫"
                case .style_animation:
                    return "动画"
                case .style_clay:
                    return "简约"
                case .style_comic:
                    return "漫画"
                case .style_cyberpunk:
                    return "赛博朋克"
                }
            }
            
            var ImageName: String {
                switch self {
                case .style_anime:
                    return "anime"
                case .style_animation:
                    return "pixelArt"
                case .style_clay:
                    return "analogFilm"
                case .style_comic:
                    return "manga"
                case .style_cyberpunk:
                    return "neonPunk"
                }
            }
        }
    }
}



//MARK: - PixverseVideoAi.T2V.Model
extension PixverseVideoAi.T2V.Model: Identifiable {
    
    // 1. 遵循 Identifiable 协议
    // 我们可以直接使用它的 rawValue 作为唯一标识符。
    var id: String { self.rawValue }
    
    // 2. 添加一个用于展示的名称 (计算属性)
    var displayName: String {
        switch self {
        case .pixverse_v3_5:
            return "Pix v3.5"
        case .pixverse_v4_0:
            return "Pix v4.0"
        case .pixverse_v4_5:
            return "Pix v4.5"
        }
    }
    
    // 3. 添加一个对应的图标 (计算属性)
    var icon: String {
        switch self {
//        case .pixverse_v3_5:
//            return "hammer.fill" // 代表开发、工具
//        case .pixverse_v4_0:
//            return "star.fill"   // 代表专业版
//        case .pixverse_v4_5:
//            return "crown.fill"  // 代表顶级、至尊版
        default:
            return "IconPixverse"
        }
    }
    
    var icon_darkTheme:String {
        switch self {
        default:
            return "IconPixverse"
        }
    }
    
    var describe: String {
        switch self {
        case .pixverse_v3_5:
            return "PixVerse V3.5版，视频模型"
        case .pixverse_v4_0:
            return "PixVerse V4.0版，视频模型"
        case .pixverse_v4_5:
            return "PixVerse V4.5版，最新版本"
        }
    }
    
}

//MARK: - PixverseVideoAi.T2V.AspectRatio
extension PixverseVideoAi.T2V.AspectRatio: AspectRatioRepresentable {
    
    // 1. 满足 Identifiable 协议 (enum case 本身就是唯一的)
    var id: Self { self }
    
    // 2. 满足 AspectRatioRepresentable 协议的要求
    var ratioText: String {
        return self.description // 直接复用你已有的 description
    }
    
    var secondaryDescription: String? {
        switch self {
        case .ratio_1_1 : return  nil
        case .ratio_16_9: return  nil
        case .ratio_9_16 : return  nil
        case .ratio_3_4 : return  nil
        case .ratio_4_3 : return  nil
        }
    }
    
    var widthRatio: CGFloat {
        switch self {
        case .ratio_1_1 : return  1
        case .ratio_16_9: return  16
        case .ratio_9_16 : return  9
        case .ratio_3_4 : return  3
        case .ratio_4_3 : return  4
        }
    }
    
    var heightRatio: CGFloat {
        switch self {
        case .ratio_1_1 : return  1
        case .ratio_16_9: return  9
        case .ratio_9_16 : return  16
        case .ratio_3_4 : return  4
        case .ratio_4_3 : return  3
        }
    }
}


//MARK: - PixverseVideoAi.I2V.Model
extension PixverseVideoAi.I2V.Model: Identifiable {
    
    // 1. 遵循 Identifiable 协议
    // 我们可以直接使用它的 rawValue 作为唯一标识符。
    var id: String { self.rawValue }
    
    // 2. 添加一个用于展示的名称 (计算属性)
    var displayName: String {
        switch self {
        case .pixverse_v3_5:
            return "PixVerse V3.5版，视频模型"
        case .pixverse_v4_0:
            return "PixVerse V4.0版，视频模型"
        case .pixverse_v4_5:
            return "PixVerse V4.5版，最新版本"
        }
    }
    
    // 3. 添加一个对应的图标 (计算属性)
    var icon: String {
        switch self {
//        case .pixverse_v3_5:
//            return "hammer.fill" // 代表开发、工具
//        case .pixverse_v4_0:
//            return "star.fill"   // 代表专业版
//        case .pixverse_v4_5:
//            return "crown.fill"  // 代表顶级、至尊版
        default:
            return "IconPixverse"
        }
    }
    
    var icon_darkTheme:String {
        switch self {
        default:
            return "IconPixverse"
        }
    }
    
    var describe: String {
        switch self {
        case .pixverse_v3_5:
            return "pixverse_v3_5"
        case .pixverse_v4_0:
            return "pixverse_v4_0"
        case .pixverse_v4_5:
            return "pixverse_v4_5"
        }
    }
    
}
