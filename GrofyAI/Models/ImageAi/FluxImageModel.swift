//
//  FluxImageModel.swift
//  GrofyAI
//
//  Created by kissy on 2025/4/27.
//
import SwiftUI

//MARK: Flux
enum FluxImageAi {
    enum Model: String, Encodable, CaseIterable {
        case flux_dev = "FLUX_DEV"
        case flux_pro_1_1 = "FLUX_PRO_1_1"
        case flux_pro_1_1_ultra = "FLUX_PRO_1_1_ULTRA"
    }
    
    enum AspectRatio: String, AiModelProtocol {
        case ratio1_1 = "RATIO_1_1"
        case ratio3_2 = "RATIO_3_2"
        case ratio2_3 = "RATIO_2_3"
        case ratio5_4 = "RATIO_5_4"
        case ratio4_5 = "RATIO_4_5"
        case ratio16_9 = "RATIO_16_9"
        case ratio9_16 = "RATIO_9_16"
        case ratio21_9 = "RATIO_21_9"
        case ratio9_21 = "RATIO_9_21"
        
        var description: String {
            switch self {
            case .ratio1_1:   return "1:1"
            case .ratio3_2:   return "3:2"
            case .ratio2_3:   return "2:3"
            case .ratio5_4:   return "5:4"
            case .ratio4_5:   return "4:5"
            case .ratio16_9:   return "16:9"
            case .ratio9_16:   return "9:16"
            case .ratio21_9:   return "21:9"
            case .ratio9_21:   return "9:21"
            }
        }
    }
    
    enum OutputFormat: String, AiModelProtocol {
        case jpeg = "jpeg"
        case png = "png"
        
        var description: String {
            switch self {
            case .jpeg :return "JPEG"
            case .png: return "PNG"
            }
        }
    }
    
    case model(Model)
    case aspect_ratio(AspectRatio)
    case output_format(OutputFormat)
}

//MARK: FluxImageAi.Model
extension FluxImageAi.Model: Identifiable {
    
    // 1. 遵循 Identifiable 协议
    // 我们可以直接使用它的 rawValue 作为唯一标识符。
    var id: String { self.rawValue }
    
    // 2. 添加一个用于展示的名称 (计算属性)
    var displayName: String {
        switch self {
        case .flux_dev:
            return "Flux Dev"
        case .flux_pro_1_1:
            return "Flux Pro 1.1"
        case .flux_pro_1_1_ultra:
            return "Flux Pro 1.1 Ultra"
        }
    }
    
    // 3. 添加一个对应的图标 (计算属性)
    var icon: String {
        switch self {
//        case .flux_dev:
//            return "hammer.fill" // 代表开发、工具
//        case .flux_pro_1_1:
//            return "star.fill"   // 代表专业版
//        case .flux_pro_1_1_ultra:
//            return "crown.fill"  // 代表顶级、至尊版
        default:
            return "IconFlux"
        }
    }
    
    var icon_darkTheme:String {
        switch self {
        default:
            return "IconFlux__customWhite"
        }
    }
    
    var describe: String {
        switch self {
        case .flux_dev:
            return "FLUX 内部开发测试版"
        case .flux_pro_1_1:
            return "FLUX 主力版，平衡速度与画质"
        case .flux_pro_1_1_ultra:
            return "FLUX 旗舰版，追求极致图像质量"
        }
    }

}


// --- 使用 Extension 来遵循新协议 ---
extension FluxImageAi.AspectRatio: AspectRatioRepresentable {
    
    // 1. 满足 Identifiable 协议 (enum case 本身就是唯一的)
    var id: Self { self }
    
    // 2. 满足 AspectRatioRepresentable 协议的要求
    var ratioText: String {
        return self.description // 直接复用你已有的 description
    }
    
    var secondaryDescription: String? {
        switch self {
        case .ratio1_1: return  nil //  "头像图"
        case .ratio3_2: return  nil //"竖版"
        case .ratio2_3: return  nil //"横版"
        case .ratio4_5: return nil
        case .ratio5_4: return nil
        case .ratio16_9: return nil
        case .ratio9_16: return nil
        case .ratio21_9: return nil
        case .ratio9_21: return nil
        }
    }
    
    var widthRatio: CGFloat {
        switch self {
        case .ratio1_1: return  1 //  "头像图"
        case .ratio3_2: return  3 //"竖版"
        case .ratio2_3: return  2 //"横版"
        case .ratio4_5: return 4
        case .ratio5_4: return 5
        case .ratio16_9: return 16
        case .ratio9_16: return 9
        case .ratio21_9: return 21
        case .ratio9_21: return 9
        }
    }
    
    var heightRatio: CGFloat {
        switch self {
        case .ratio1_1: return  1 //  "头像图"
        case .ratio3_2: return  2 //"竖版"
        case .ratio2_3: return  3 //"横版"
        case .ratio4_5: return 5
        case .ratio5_4: return 4
        case .ratio16_9: return 9
        case .ratio9_16: return 16
        case .ratio21_9: return 9
        case .ratio9_21: return 21
        }
    }
}
