//
//  StableDiffusionImageModel.swift
//  GrofyAI
//
//  Created by kissy on 2025/4/28.
//
import SwiftUI

//MARK: StableDiffusion - ENUM
enum StableDiffusion3_5ImageAi {
    enum Mode: String, GenericSelectorOptionsProtocol {
        var id: Self {self}
        
        case text_to_image = "TEXT_TO_IMAGE"
        case image_to_image = "IMAGE_TO_IMAGE"
        
        var description: String {
            switch self {
            case .text_to_image: return "文生图"
            case .image_to_image: return "图生图"
            }
        }
    }
    
    enum AspectRatio: String, AiModelProtocol {
        case ratio1_1 = "RATIO_1_1"
        case ratio3_2 = "RATIO_3_2"
        case ratio2_3 = "RATIO_2_3"
        case ratio5_4 = "RATIO_5_4"
        case ratio4_5 = "RATIO_4_5"
        case ratio16_9 = "RATIO_16_9"
        case ratio9_16 = "RATIO_9_16"
        case ratio21_9 = "RATIO_21_9"
        case ratio9_21 = "RATIO_9_21"
        
        var description: String {
            switch self {
            case .ratio1_1:   return "1:1"
            case .ratio3_2:   return "3:2"
            case .ratio2_3:   return "2:3"
            case .ratio5_4:   return "5:4"
            case .ratio4_5:   return "4:5"
            case .ratio16_9:   return "16:9"
            case .ratio9_16:   return "9:16"
            case .ratio21_9:   return "21:9"
            case .ratio9_21:   return "9:21"
            }
        }
    }
    
    enum Model: String, Encodable, CaseIterable{
        case sd3_5_large = "SD3_5_LARGE"
        case sd3_5_large_turbo = "SD3_5_LARGE_TURBO"
        case sd3_5_medium = "SD3_5_MEDIUM"
    }
    
    enum OutputFormat: String, AiModelProtocol {
        case jpeg = "JPEG"
        case png = "PNG"
        
        var description: String {
            switch self {
            case .jpeg :return "JPEG"
            case .png: return "PNG"
            }
        }
    }
    
    enum StylePreset: String, AiModelProtocol {
        case style3DModel = "STYLE_3D_MODEL"
        case analogFilm = "STYLE_ANALOG_FILM"
        case anime = "STYLE_ANIME"
        case cinematic = "STYLE_CINEMATIC"
        case comicBook = "STYLE_COMIC_BOOK"
        case digitalArt = "STYLE_DIGITAL_ART"
        case enhance = "STYLE_ENHANCE"
        case fantasyArt = "STYLE_FANTASY_ART"
        case isometric = "STYLE_ISOMETRIC"
        case lineArt = "STYLE_LINE_ART"
        case lowPoly = "STYLE_LOW_POLY"
        case modelingCompound = "STYLE_MODELING_COMPOUND"
        case neonPunk = "STYLE_NEON_PUNK"
        case origami = "STYLE_ORIGAMI"
        case photographic = "STYLE_PHOTOGRAPHIC"
        case pixelArt = "STYLE_PIXEL_ART"
        case tileTexture = "STYLE_TILE_TEXTURE"
        
        
        var description: String {
            switch self {
            case .style3DModel:        return "3D模型"
            case .analogFilm:         return "胶片质感"
            case .anime:             return "日式动漫"
            case .cinematic:         return "电影镜头"
            case .comicBook:         return "美漫风格"
            case .digitalArt:        return "数字艺术"
            case .enhance:           return "增强模式"
            case .fantasyArt:        return "奇幻艺术"
            case .isometric:         return "等轴视角"
            case .lineArt:           return "线条艺术"
            case .lowPoly:           return "低多边形"
            case .modelingCompound:  return "建模合成"
            case .neonPunk:          return "霓虹朋克"
            case .origami:           return "折纸艺术"
            case .photographic:      return "摄影写实"
            case .pixelArt:          return "像素艺术"
            case .tileTexture:       return "瓷砖纹理"
            }
        }
        
        var ImageName:String{
            switch self {
            case .style3DModel : return  "3D_model"
            case .analogFilm:         return "analogFilm"
            case .anime:             return "anime"
            case .cinematic:         return "cinematic"
            case .comicBook:         return "comicBook"
            case .digitalArt:        return "digitalArt"
            case .enhance:           return "enhance"
            case .fantasyArt:        return "fantasyArt"
            case .isometric:         return "isometric"
            case .lineArt:           return "lineArt"
            case .lowPoly:           return "lowPoly"
            case .modelingCompound:  return "modelingCompound"
            case .neonPunk:          return "neonPunk"
            case .origami:           return "origami"
            case .photographic:      return "photographic"
            case .pixelArt:          return "pixelArt"
            case .tileTexture:       return "tileTexture"
                
            }
        }
    }
}

//MARK: StableDiffusion3_5ImageAi.Model
extension StableDiffusion3_5ImageAi.Model: Identifiable {
    
    // 1. 遵循 Identifiable 协议
    // 我们可以直接使用它的 rawValue 作为唯一标识符。
    var id: String { self.rawValue }
    
    // 2. 添加一个用于展示的名称 (计算属性)
    var displayName: String {
        switch self {
        case .sd3_5_large:
            return "SD 3.5 Large"
        case .sd3_5_large_turbo:
            return "SD 3.5 Large Turbo"
        case .sd3_5_medium:
            return "SD 3.5 Medium"
        }
    }
    
    // 3. 添加一个对应的图标 (计算属性)
    var icon: String {
        switch self {
//        case .sd3_5_large:
//            return "hammer.fill" // 代表开发、工具
//        case .sd3_5_large_turbo:
//            return "star.fill"   // 代表专业版
//        case .sd3_5_medium:
//            return "crown.fill"  // 代表顶级、至尊版
        default:
            return "IconStableDiffusion"
        }
    }
    
    var icon_darkTheme:String {
        switch self {
        default:
            return "IconStableDiffusion"
        }
    }
    
    
    var describe: String {
        switch self {
        case .sd3_5_large:
            return "SD 3.5 旗舰版，细节与文字效果最佳"
        case .sd3_5_large_turbo:
            return "SD 3.5 极速版，为实时反馈而生"
        case .sd3_5_medium:
            return "SD 3.5 均衡版，兼顾性能与效率"
        }
    }
}


//MARK: StableDiffusion3_5ImageAi.AspectRatio
extension StableDiffusion3_5ImageAi.AspectRatio: AspectRatioRepresentable {
    
    // 1. 满足 Identifiable 协议 (enum case 本身就是唯一的)
    var id: Self { self }
    
    // 2. 满足 AspectRatioRepresentable 协议的要求
    var ratioText: String {
        return self.description // 直接复用你已有的 description
    }
    
    var secondaryDescription: String? {
        switch self {
        case .ratio1_1: return  nil //  "头像图"
        case .ratio3_2: return  nil //"竖版"
        case .ratio2_3: return  nil //"横版"
        case .ratio4_5: return nil
        case .ratio5_4: return nil
        case .ratio16_9: return nil
        case .ratio9_16: return nil
        case .ratio21_9: return nil
        case .ratio9_21: return nil
        }
    }
    
    var widthRatio: CGFloat {
        switch self {
        case .ratio1_1: return  1 //  "头像图"
        case .ratio3_2: return  3 //"竖版"
        case .ratio2_3: return  2 //"横版"
        case .ratio4_5: return 4
        case .ratio5_4: return 5
        case .ratio16_9: return 16
        case .ratio9_16: return 9
        case .ratio21_9: return 21
        case .ratio9_21: return 9
        }
    }
    
    var heightRatio: CGFloat {
        switch self {
        case .ratio1_1: return  1 //  "头像图"
        case .ratio3_2: return  2 //"竖版"
        case .ratio2_3: return  3 //"横版"
        case .ratio4_5: return 5
        case .ratio5_4: return 4
        case .ratio16_9: return 9
        case .ratio9_16: return 16
        case .ratio21_9: return 9
        case .ratio9_21: return 21
        }
    }
}
