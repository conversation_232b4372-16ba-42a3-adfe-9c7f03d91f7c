//
//  MidjoutneyImageModel.swift
//  GrofyAI
//
//  Created by kissy on 2025/4/28.
//
import SwiftUI

//MARK: Midjourney - ENUM
enum MidjourneyImageAi {
    enum AspectRatio: String, AiModelProtocol {
        case ratio1_1 = "RATIO_1_1"
        case ratio3_2 = "RATIO_3_2"
        case ratio2_3 = "RATIO_2_3"
        case ratio5_4 = "RATIO_5_4"
        case ratio4_5 = "RATIO_4_5"
        case ratio16_9 = "RATIO_16_9"
        case ratio9_16 = "RATIO_9_16"
        case ratio21_9 = "RATIO_21_9"
        case ratio9_21 = "RATIO_9_21"
        
        var description: String {
            switch self {
            case .ratio1_1:     return "1:1"
            case .ratio3_2:     return "3:2"
            case .ratio2_3:     return "2:3"
            case .ratio5_4:     return "5:4"
            case .ratio4_5:     return "4:5"
            case .ratio16_9:    return "16:9"
            case .ratio9_16:    return "9:16"
            case .ratio21_9:    return "21:9"
            case .ratio9_21:    return "9:21"
            }
        }
    }

    enum Style: String, AiModelProtocol {
        case cyberpunk = "CYBERPUNK"
        case warframe = "WARFRAME"
        case acgn = "ACGN"
        case japaneseManga = "JAPANESE_MANGA"
        case inkWash = "INK_WASH"
        case original = "ORIGINAL"
        case landscape = "LANDSCAPE"
        case illustration = "ILLUSTRATION"
        case manga = "MANGA"
        case modernOrganic = "MODERN_ORGANIC"
        case genesis = "GENESIS"
        case posterStyle = "POSTER_STYLE"
        case surrealism = "SURREALISM"
        case sketch = "SKETCH"
        case realism = "REALISM"
        case watercolor = "WATERCOLOR"
        case cubism = "CUBISM"
        case blackAndWhite = "BLACK_AND_WHITE"
        case filmPhotography = "FILM_PHOTOGRAPHY"
        case cinematic = "CINEMATIC"
        
        var description: String {
            switch self {
            case .cyberpunk:       return "赛博朋克"
            case .warframe:       return "星际战甲风"
            case .acgn:           return "二次元文化"
            case .japaneseManga:  return "日本漫画"
            case .inkWash:        return "水墨风格"
            case .original:       return "原创设计"
            case .landscape:      return "自然风景"
            case .illustration:   return "插画艺术"
            case .manga:         return "日式漫画"
            case .modernOrganic:  return "现代有机"
            case .genesis:        return "创世纪"
            case .posterStyle:    return "海报风格"
            case .surrealism:     return "超现实主义"
            case .sketch:         return "手绘素描"
            case .realism:        return "写实主义"
            case .watercolor:     return "水彩画"
            case .cubism:         return "立体主义"
            case .blackAndWhite: return "黑白摄影"
            case .filmPhotography: return "胶片质感"
            case .cinematic:     return "电影镜头"
            }
        }
        
        var ImageName: String {
            switch self {
            case .cyberpunk:       return "neonPunk"
            case .warframe:       return "warframe"
            case .acgn:           return "anime"
            case .japaneseManga:  return "japaneseManga"
            case .inkWash:        return "inkWash"
            case .original:       return "original"
            case .landscape:      return "landscape"
            case .illustration:   return "illustration"
            case .manga:          return "manga"
            case .modernOrganic:  return "analogFilm"
            case .genesis:        return "genesis"
            case .posterStyle:    return "posterStyle"
            case .surrealism:     return "surrealism"
            case .sketch:         return "enhance"
            case .realism:        return "realism"
            case .watercolor:     return "watercolor"
            case .cubism:         return "cubism"
            case .blackAndWhite: return "lineArt"
            case .filmPhotography: return "filmPhotography"
            case .cinematic:     return "cinematic"
            }
        }
    }
    
    enum Mode: String, Encodable, CaseIterable {
        case relax = "RELAX"
        case fast = "FAST"
        case turbo = "TURBO"
    }

}


extension MidjourneyImageAi.Mode: Identifiable {
    
    // 1. 遵循 Identifiable 协议
    // 我们可以直接使用它的 rawValue 作为唯一标识符。
    var id: String { self.rawValue }
    
    // 2. 添加一个用于展示的名称 (计算属性)
    var displayName: String {
        switch self {
        case .relax: return "RELAX"
        case .fast: return "FAST"
        case .turbo: return "TURBO"
        }
    }
    // 3. 添加一个对应的图标 (计算属性)
    var icon: String {
        switch self {
//        case .relax:
//            return "hammer.fill" // 代表开发、工具
//        case .fast:
//            return "star.fill"   // 代表专业版
//        case .turbo:
//            return "crown.fill"
        default:
            return "IconMidjourney"
        }
    }
    
    var icon_darkTheme:String {
        switch self {
        default:
            return "IconMidjourney__customWhite"
        }
    }
    
    var describe: String {
        switch self {
        case .relax: return "悠闲模式，不耗额度，排队等待生成"
        case .fast: return "快速模式，标准选择，立即开始处理"
        case .turbo: return "极速模式，速度最快，消耗双倍额度"
        }
    }
}


// --- 使用 Extension 来遵循新协议 ---
extension MidjourneyImageAi.AspectRatio: AspectRatioRepresentable {
    
    // 1. 满足 Identifiable 协议 (enum case 本身就是唯一的)
    var id: Self { self }
    
    // 2. 满足 AspectRatioRepresentable 协议的要求
    var ratioText: String {
        return self.description // 直接复用你已有的 description
    }
    
    var secondaryDescription: String? {
        switch self {
        case .ratio1_1: return  nil //  "头像图"
        case .ratio3_2: return  nil //"竖版"
        case .ratio2_3: return  nil //"横版"
        case .ratio4_5: return nil
        case .ratio5_4: return nil
        case .ratio16_9: return nil
        case .ratio9_16: return nil
        case .ratio21_9: return nil
        case .ratio9_21: return nil
        }
    }
    
    var widthRatio: CGFloat {
        switch self {
        case .ratio1_1: return  1 //  "头像图"
        case .ratio3_2: return  3 //"竖版"
        case .ratio2_3: return  2 //"横版"
        case .ratio4_5: return 4
        case .ratio5_4: return 5
        case .ratio16_9: return 16
        case .ratio9_16: return 9
        case .ratio21_9: return 21
        case .ratio9_21: return 9
        }
    }
    
    var heightRatio: CGFloat {
        switch self {
        case .ratio1_1: return  1 //  "头像图"
        case .ratio3_2: return  2 //"竖版"
        case .ratio2_3: return  3 //"横版"
        case .ratio4_5: return 5
        case .ratio5_4: return 4
        case .ratio16_9: return 9
        case .ratio9_16: return 16
        case .ratio21_9: return 9
        case .ratio9_21: return 21
        }
    }
}
