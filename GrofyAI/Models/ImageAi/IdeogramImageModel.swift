//
//  IdeogramImageModel.swift
//  GrofyAI
//
//  Created by kissy on 2025/4/27.
//
import SwiftUI

//MARK: Ideogram - ENUM
enum IdeogramImageAi {
    enum Model: String, Encodable, CaseIterable {
        case ideogram_v3 = "IDEOGRAM_V_3"
    }
    
    enum Speed: String, CaseIterable,AiDescription {
         case turbo = "TURBO"
        case def = "DEFAULT"
        case quality = "QUALITY"
        
        var description: String {
            switch self {
            case .turbo: return "快速渲染"
            case .def: return "默认"
            case .quality: return "高质量"
            }
        }
    }
      
    enum AspectRatio: String, AiModelProtocol {
        case aspect1_1 = "ASPECT_1_1"
        case aspect3_1 = "ASPECT_3_1"
        case aspect1_3 = "ASPECT_1_3"
        case aspect3_2 = "ASPECT_3_2"
        case aspect2_3 = "ASPECT_2_3"
        case aspect3_4 = "ASPECT_3_4"
        case aspect4_3 = "ASPECT_4_3"
        case aspect16_9 = "ASPECT_16_9"
        case aspect9_16 = "ASPECT_9_16"
        case aspect16_10 = "ASPECT_16_10"
        case aspect10_16 = "ASPECT_10_16"
        case aspect1_2 = "ASPECT_1_2"
        case aspect2_1 = "ASPECT_2_1"
        case aspect4_5 = "ASPECT_4_5"
        case aspect5_4 = "ASPECT_5_4"
        
        var description: String {
            switch self {
            case .aspect1_1:    return "1:1"
            case .aspect3_1:    return "3:1"
            case .aspect1_3:    return "1:3"
            case .aspect3_2:    return "3:2"
            case .aspect2_3:    return "2:3"
            case .aspect3_4:    return "3:4"
            case .aspect4_3:    return "4:3"
            case .aspect16_9:     return "16:9"
            case .aspect9_16:     return "9:16"
            case .aspect16_10:    return "16:10"
            case .aspect10_16:    return "10:16"
            case .aspect1_2:    return "1:2"
            case .aspect2_1:    return "2:1"
            case .aspect4_5:    return "4:5"
            case .aspect5_4:    return "5:4"
            }
        }
    }
    
    enum MagicPromptOption: String, GenericSelectorOptionsProtocol{
        var id: Self { self }
        
        case auto = "AUTO"
        case on = "ON"
        case off = "OFF"
        
        var description: String {
            switch self {
            case .auto: return "自动"
            case .on: return "打开"
            case .off: return "关闭"
            }
        }
    }
    
    enum StyleType: String, AiModelProtocol {
        case auto = "AUTO"
        case general = "GENERAL"
        case design = "DESIGN"
        case realistic = "REALISTIC"
//        case design = "DESIGN"
//        case design3D = "DESIGN_3D"
        
        
        var description: String {
            switch self {
            case .auto: return "自动"
            case .general: return "通用"
            case .design: return "设计"
            case .realistic: return "写实"
            }
        }
        
        var ImageName: String {
            switch self {
            case .auto: return "auto"
            case .general: return "realism"
            case .design: return "modelingCompound"
            case .realistic: return "cinematic"
            }
        }
    }
    
    case model(Model)
    case aspect_ratio(AspectRatio)
    case magic_prompt_option(MagicPromptOption)
    case style_type(StyleType)
}


//MARK: IdeogramImageAi.Model
extension IdeogramImageAi.Model: Identifiable {
    
    // 1. 遵循 Identifiable 协议
    // 我们可以直接使用它的 rawValue 作为唯一标识符。
    var id: String { self.rawValue }
    
    // 2. 添加一个用于展示的名称 (计算属性)
    var displayName: String {
        switch self {
        case .ideogram_v3: return "V3"
        
        }
    }
    // 3. 添加一个对应的图标 (计算属性)
    var icon: String {
        switch self {
        case .ideogram_v3:
            return "hammer.fill" // 代表开发、工具
        }
    }
    
    // 4. 添加一个对应的颜色 (计算属性)
    var displayColor: Color {
        switch self {
        case .ideogram_v3:
            return .green
        }
    }
}




// --- 使用 Extension 来遵循新协议 ---
extension IdeogramImageAi.AspectRatio: AspectRatioRepresentable {
    
    // 1. 满足 Identifiable 协议 (enum case 本身就是唯一的)
    var id: Self { self }
    
    // 2. 满足 AspectRatioRepresentable 协议的要求
    var ratioText: String {
        return self.description // 直接复用你已有的 description
    }
    
    var secondaryDescription: String? {
        switch self {
        case .aspect1_1: return  nil //  "头像图"
        case .aspect3_1: return nil
        case .aspect1_3: return nil
        case .aspect3_2: return  nil //"竖版"
        case .aspect2_3: return  nil //"横版"
        case .aspect3_4: return nil
        case .aspect4_3: return nil
        case .aspect16_9: return nil
        case .aspect9_16: return nil
        case .aspect16_10: return nil
        case .aspect10_16: return nil
        case .aspect1_2: return nil
        case .aspect2_1: return nil
        case .aspect4_5: return nil
        case .aspect5_4: return nil
        }
    }
    
    var widthRatio: CGFloat {
        switch self {
        case .aspect1_1: return  1 //  "头像图"
        case .aspect3_1: return 3
        case .aspect1_3: return 1
        case .aspect3_2: return  3 //"竖版"
        case .aspect2_3: return  2 //"横版"
        case .aspect3_4: return 3
        case .aspect4_3: return 4
        case .aspect16_9: return 16
        case .aspect9_16: return 9
        case .aspect16_10: return 16
        case .aspect10_16: return 10
        case .aspect1_2: return 1
        case .aspect2_1: return 2
        case .aspect4_5: return 4
        case .aspect5_4: return 5
        }
    }
    
    var heightRatio: CGFloat {
        switch self {
        case .aspect1_1: return  1 //  "头像图"
        case .aspect3_1: return 1
        case .aspect1_3: return 3
        case .aspect3_2: return  2 //"竖版"
        case .aspect2_3: return  3 //"横版"
        case .aspect3_4: return 4
        case .aspect4_3: return 3
        case .aspect16_9: return 9
        case .aspect9_16: return 16
        case .aspect16_10: return 10
        case .aspect10_16: return 16
        case .aspect1_2: return 2
        case .aspect2_1: return 1
        case .aspect4_5: return 5
        case .aspect5_4: return 4
        }
    }
}
