//
//  DalleImageModel.swift
//  GrofyAI
//
//  Created by kissy on 2025/4/27.
//
import SwiftUI

protocol AiDescription {
    var description: String { get }
}

// 任何想要在 AspectRatioSelector 中显示的类型，都必须遵循此协议
protocol AspectRatioRepresentable: Identifiable, Hashable {
    // UI 需要的数据
    var ratioText: String { get }          // e.g., "1:1"
    var secondaryDescription: String? { get }  // e.g., "头像图"
    var widthRatio: CGFloat { get }        // e.g., 1.0
    var heightRatio: CGFloat { get }       // e.g., 1.0
}

typealias AiModelProtocol =  Encodable & CaseIterable & AiDescription


//MARK: Dalle - ENUM
enum OpenAiImageAi {
    
    enum Background: String, AiModelProtocol {
        case auto = "AUTO"
        case transparent = "TRANSPARENT"
        case opaque = "OPAQUE"
        
        var description: String {
            switch self {
            case .auto: return "自动"
            case .transparent: return "透明"
            case .opaque: return "不透明"
            }
        }
    }
    enum Model: String, Encodable, CaseIterable  {
        case openAi_dall_e_3 = "OpenAI_DALL_E_3"
        case openAI_gpt_image_1 = "OpenAI_GPT_IMAGE_1"
    }
    
    enum Size: String, AiModelProtocol{
        case ratio1_1 = "RATIO_1_1"
        case ratio2_3 = "RATIO_2_3"
        case ratio3_2 = "RATIO_3_2"
        case ratio4_7 = "RATIO_4_7"
        case ratio7_4 = "RATIO_7_4"
        
        var description: String {
            switch self {
            case .ratio1_1:   return "1:1"
            case .ratio2_3: return "2:3"
            case .ratio3_2: return "3:2"
            case .ratio4_7:   return "4:7"
            case .ratio7_4:   return "7:4"
            }
        }
        
        var supportedModel: [Model] {
            switch self {
            case .ratio1_1 :
                return [.openAI_gpt_image_1,.openAi_dall_e_3]
            case .ratio4_7, .ratio7_4 :
                return [.openAi_dall_e_3]
            case .ratio2_3, .ratio3_2 :
                return [.openAI_gpt_image_1]
            }
        }
    }
    
    enum Quality: String, AiModelProtocol {
        case standanrd = "STANDARD"
        case hd = "HD"
        case high = "HIGH"
        case medium = "MEDIUM"
        case low = "LOW"

        var description: String {
            switch self {
            case .standanrd:   return "标准"
            case .hd:   return "高清"
            case .high: return "高"
            case .medium: return "中"
            case .low: return "低"
            }
        }
        
        var supportedModel:  Model {
            switch self {
            case .standanrd,.hd :
                return .openAi_dall_e_3
            case .high,.medium,.low :
                return .openAI_gpt_image_1
            }
        }
        
    }
    
    enum Style: String, AiModelProtocol {
        case natural = "NATURAL"
        case vivid = "VIVID"

        var description: String {
            switch self {
            case .natural:   return "自然"
            case .vivid:   return "生动"
            }
        }
        
        var ImageName: String {
            switch self {
            case .natural:   return "landscape"
            case .vivid:   return "cinematic"
            }
        }
    }

}

// --- 使用 Extension 来遵循新协议 ---
extension OpenAiImageAi.Size: AspectRatioRepresentable {
    
    // 1. 满足 Identifiable 协议 (enum case 本身就是唯一的)
    var id: Self { self }
    
    // 2. 满足 AspectRatioRepresentable 协议的要求
    var ratioText: String {
        return self.description // 直接复用你已有的 description
    }
    
    var secondaryDescription: String? {
        switch self {
        case .ratio1_1: return  nil //  "头像图"
        case .ratio4_7: return  nil //"竖版"
        case .ratio7_4: return  nil //"横版"
        case .ratio2_3: return nil
        case .ratio3_2: return nil
        }
    }
    
    var widthRatio: CGFloat {
        switch self {
        case .ratio1_1: return 1
        case .ratio4_7: return 4
        case .ratio7_4: return 7
        case .ratio2_3: return 2
        case .ratio3_2: return 3
        }
    }
    
    var heightRatio: CGFloat {
        switch self {
        case .ratio1_1: return 1
        case .ratio4_7: return 7
        case .ratio7_4: return 4
        case .ratio2_3: return 3
        case .ratio3_2: return 2
        }
    }
}


extension OpenAiImageAi.Model :Identifiable {
    var id: String { self.rawValue }
    // 2. 添加一个用于展示的名称 (计算属性)
    var displayName: String {
        switch self {
        case .openAi_dall_e_3:
            return "DALL_E_3"
        case .openAI_gpt_image_1:
            return "GPT_IMAGE_1"
        }
    }
    
    // 3. 添加一个对应的图标 (计算属性)
    var icon: String {
        switch self {
//        case .openAi_dall_e_3:
//            return "hammer.fill" // 代表开发、工具
//        case .openAI_gpt_image_1:
//            return "star.fill"   // 代表专业版
        default:
            return "IconOpenAi"
        }
    }
    
    var icon_darkTheme:String {
        switch self {
        default:
            return "IconOpenAi__customWhite"
        }
    }
    
    var describe: String {
        switch self {
        case .openAi_dall_e_3:
            return "OpenAI 主力模型，擅长理解自然语言"
        case .openAI_gpt_image_1:
            return "OpenAI 下一代技术，追求更高真实感"
        }
    }
}
