//
//  ImageModelsIcon.swift
//  GrofyAI
//
//  Created by kissy on 2025/7/25.
//

enum ImageModelsIcon:String, CaseIterable {
    case stableDiffusion = "IconStableDiffusion"
    case openAi = "IconOpenAi"
    case flux = "IconFlux"
    case google     = "IconGoogle"
    case midjourney = "IconMidjourney"
    case qwen       = "IconQwen"
    case kling      = "IconKling"
    case hailu      = "IconHailuo" // "Hailuo" 对应 "hailu" 比较符合 Swift 命名习惯
    case ideogram   = "IconIdeogram"
    case minimax    = "IconMinimax"
    case pixverse   = "IconPixverse"
    
    var darkTheme: String {
        switch self {
        case .openAi, .flux, .midjourney, .qwen, .ideogram:
            return self.rawValue + "__customWhite"
        default:
            return self.rawValue
        }
    }
}
