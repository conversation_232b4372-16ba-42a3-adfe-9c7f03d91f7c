import Foundation

// 菜单选项枚举
enum TabItem: Int, CaseIterable {
    case chat = 0
    case image = 1
    case knowledge = 2
    case video = 3
    case profile = 4

    var title: String {
        switch self {
        case .chat: return "聊天"
        case .image: return "画图"
        case .video: return "视频"
        case .knowledge: return "知识库"
        case .profile: return "我的"
        }
    }

    var icon: String {
        switch self {
        case .chat: return "IconTabChat"
        case .image: return "IconTabImage"
        case .video: return "IconTabVideo"
        case .knowledge: return "IconTabKnowledge"
        case .profile: return "IconTabProfile"
        }
    }

    var selectedIcon: String {
        switch self {
        case .chat: return "IconTabChatSelected"
        case .image: return "IconTabImageSelected"
        case .video: return "IconTabVideoSelected"
        case .knowledge: return "IconTabKnowledgeSelected"
        case .profile: return "IconTabProfileSelected"
        }
    }
}
