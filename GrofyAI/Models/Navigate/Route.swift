import Foundation

// 路由枚举
enum Route: Hashable {
    // 对话
    case textChat(initialMessage: String? = nil, threadId: String? = nil)
    case fileChat(knowledgeId: Int, fileName: String? = nil, initialMessage: String? = nil, threadId: String? = nil)

    // 图片
    case imageGeneration
    case imageRecognitionChat(threadId: String?)
    case createImage(detail: ArtWorksCell?)
    case imageGeneratorChat(initialMessage: String? = nil, threadId: String? = nil)
    case imageArtWorkCreationResult(transactionID: Int?, detail: ArtWorksCell?)

    // 视频
    case effectVideo
    case videoGeneration
    case audioGeneration
    case imageBook
    case audioMusic
    case createVideo(detail: ArtWorksCell?, createType: CreateType?)
    case createEffectVideo(type: EffectVideoAi.EffectType?, detail: ArtWorksCell? = nil)
    case videoRecommendedArtwork(detail: ArtWorksCell)
    case videoArtWorkCreationResult(transactionID: Int?, detail: ArtWorksCell?)

    // 历史记录
    case artWorkHistory(defaultMode: HistoryMode?)
    case chatHistory(chatMode: ChatMode? = .agent)
    case imageRecognitionHistoryList
    case creditsHistory

    // 知识库
    case personalKnowledgeBase
    case knowledgeBaseFiles(categoryId: Int, categoryName: String? = nil)
    case editKnowledgeCategory(categoryId: Int)
    case createKnowledgeCategory

    // 支付
    case payment

    // 鉴权
    case authentication

    // 个人首页与设置
    case profile
    case settings
    case themeSetting
    case aboutUs
    case imageRecognitionHistory
    case problemFeedback
}
