//
//  ThemeModels.swift
//  GrofyAI
//
//  Created by kissy on 2025/6/18.
//
import SwiftUI

enum ColorSchemeOption: String, CaseIterable {
    case system = "跟随系统"
    case light = "亮色模式"
    case dark = "暗色模式"
    
    // 一个计算属性，用于将我们的选项转换为 SwiftUI 的 ColorScheme 类型
    var override: ColorScheme? {
        switch self {
        case .system:
            return nil // nil 表示跟随系统
        case .light:
            return .light
        case .dark:
            return .dark
        }
    }
}
