import SwiftUI

// MARK: - 功能卡片项数据模型

/// 首页 功能Card入口 模型
struct FeatureCardItem {
    let id: String
    let feature: FeatureItem
    let route: Route
}

// MARK: - 功能项目数据模型

/// 功能项目模型
struct FeatureItem: Identifiable {
    let id: String
    let title: String
    let subtitle: String?
    let description: String?
    let iconName: String
    let iconColor: Color

    init(
        id: String,
        title: String,
        subtitle: String? = nil,
        description: String? = nil,
        iconName: String,
        iconColor: Color = .blue
    ) {
        self.id = id
        self.title = title
        self.subtitle = subtitle
        self.description = description
        self.iconName = iconName
        self.iconColor = iconColor
    }
}
