import SwiftUI

// MARK: - 最近活动数据模型

/// 最近上传
struct RecentActivityItem {
    /// 唯一标识符
    let id: String
    /// 活动标题
    let title: String
    /// 活动副标题（可选）
    let subtitle: String?
    /// 图标名称（SF Symbols）
    let iconName: String
    /// 图标颜色
    let iconColor: Color
    /// 时间描述（如"2小时前"）
    let timeAgo: String
    /// 活动类型
    let activityType: ActivityType

    /// 活动类型枚举
    enum ActivityType {
        case document
        case image
        case video
        case audio
        case chat
    }

    init(
        id: String = UUID().uuidString,
        title: String,
        subtitle: String? = nil,
        iconName: String,
        iconColor: Color = DesignSystem.Colors.primary,
        timeAgo: String,
        activityType: ActivityType = .document
    ) {
        self.id = id
        self.title = title
        self.subtitle = subtitle
        self.iconName = iconName
        self.iconColor = iconColor
        self.timeAgo = timeAgo
        self.activityType = activityType
    }
}
